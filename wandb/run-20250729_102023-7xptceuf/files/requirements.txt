lit==15.0.7
cmake==3.25.0
torchvision==0.15.2+cu118
filelock==3.18.0
six==1.17.0
pycocotools==2.0.10
triton==3.3.1
Pillow==9.5.0
scipy==1.10.1
einops==0.8.1
timm==1.0.15
absl-py==2.3.1
rouge-score==0.1.2
cssselect2==0.8.0
cairocffi==1.7.1
CairoSVG==2.8.2
nvidia-cusparselt-cu12==0.6.3
tqdm==4.67.1
sympy==1.14.0
nvidia-nvtx-cu12==12.6.77
nvidia-nvjitlink-cu12==12.6.85
nvidia-nccl-cu12==2.26.2
nvidia-curand-cu12==*********
nvidia-cufile-cu12==********
nvidia-cuda-runtime-cu12==12.6.77
nvidia-cuda-nvrtc-cu12==12.6.77
nvidia-cuda-cupti-cu12==12.6.80
nvidia-cublas-cu12==********
nvidia-cusparse-cu12==********
nvidia-cudnn-cu12==********
nvidia-cusolver-cu12==********
transformers==4.53.0
bitsandbytes==0.46.1
safetensors==0.5.3
certifi==2025.6.15
pycocoevalcap==1.2
networkx==3.5
packaging==25.0
MarkupSafe==3.0.2
dill==0.3.8
frozenlist==1.7.0
aiohttp==3.12.13
pytz==2025.2
mpmath==1.3.0
xxhash==3.5.0
tzdata==2025.2
typing_extensions==4.14.1
setuptools==80.9.0
regex==2024.11.6
PyYAML==6.0.2
pyarrow==20.0.0
propcache==0.3.2
multidict==6.6.3
idna==3.10
hf-xet==1.1.5
charset-normalizer==3.4.2
attrs==25.3.0
aiohappyeyeballs==2.6.1
yarl==1.20.1
requests==2.32.4
python-dateutil==2.9.0.post0
multiprocess==0.70.16
Jinja2==3.1.6
aiosignal==1.4.0
pandas==2.3.0
pandas==2.3.0
pandas==2.3.1
pyarrow-hotfix==0.7
fsspec==2024.5.0
numpy==1.26.4
transformers==4.44.2
transformers==4.53.1
transformers==4.53.2
transformers==4.54.0
datasets==2.20.0
urllib3==1.26.20
accelerate==0.33.0
peft==0.16.0
nvidia-cufft-cu12==********
huggingface-hub==0.34.1
torch==2.7.1
tokenizers==0.21.2
bert-score==0.3.13
smmap==5.0.2
sentry-sdk==2.33.2
protobuf==6.31.1
gitdb==4.0.12
GitPython==3.1.45
wandb==0.21.0
aiofiles==22.1.0
aiosqlite==0.18.0
alabaster==0.7.12
anaconda-anon-usage==0.4.2
appdirs==1.4.4
async-timeout==4.0.2
atomicwrites==1.4.0
attrs==22.1.0
backcall==0.2.0
boltons==23.0.0
certifi==2023.7.22
chardet==4.0.0
charset-normalizer==2.0.4
click==8.0.4
cloudpickle==2.2.1
colorama==0.4.6
constantly==15.1.0
cssselect==1.1.0
cycler==0.11.0
debugpy==1.6.7
decorator==5.1.1
defusedxml==0.7.1
diff-match-patch==20200713
dill==0.3.6
docstring-to-markdown==0.11
docutils==0.18.1
entrypoints==0.4
et-xmlfile==1.1.0
executing==0.8.3
filelock==3.9.0
frozenlist==1.3.3
fsspec==2023.4.0
future==0.18.3
glob2==0.7
gmpy2==2.1.2
greenlet==2.0.1
HeapDict==1.0.1
idna==3.4
imagesize==1.4.1
incremental==21.3.0
inflection==0.5.1
iniconfig==1.1.1
ipython-genutils==0.2.0
isort==5.9.3
itemadapter==0.3.0
itsdangerous==2.0.1
jeepney==0.7.1
jellyfish==1.0.1
jmespath==0.10.0
joblib==1.2.0
json5==0.9.6
jsonpointer==2.1
jupyterlab-widgets==3.0.5
kiwisolver==1.4.4
lazy-object-proxy==1.6.0
lazy_loader==0.2
llvmlite==0.40.0
locket==1.0.0
lxml==4.9.3
lz4==4.3.2
Markdown==3.4.1
MarkupSafe==2.1.1
mccabe==0.7.0
mdurl==0.1.0
mistune==0.8.4
mkl-service==2.4.0
more-itertools==8.12.0
mpmath==1.3.0
msgpack==1.0.3
multidict==6.0.2
munkres==1.1.4
mypy-extensions==1.0.0
nest-asyncio==1.5.6
networkx==3.1
packaging==23.1
pandocfilters==1.5.0
param==1.13.0
parso==0.8.3
pathlib==1.0.1
pathspec==0.10.3
pep8==1.7.1
pickleshare==0.7.5
Pillow==9.4.0
pkce==1.0.3
pkginfo==1.9.6
platformdirs==3.10.0
pluggy==1.0.0
ply==3.11
poyo==0.5.0
prometheus-client==0.14.1
psutil==5.9.0
ptyprocess==0.7.0
pure-eval==0.2.2
py-cpuinfo==8.0.0
pyasn1==0.4.8
pycodestyle==2.10.0
pycosat==0.6.4
pycparser==2.21
pycurl==7.45.2
PyDispatcher==2.0.5
pyflakes==3.0.1
Pygments==2.15.1
PyJWT==2.4.0
pylint-venv==2.3.0
pyodbc==4.0.34
pyparsing==3.0.9
pyrsistent==0.18.0
PySocks==1.7.1
fastjsonschema==2.16.2
python-json-logger==2.0.7
libarchive-c==2.9
lmdb==1.4.1
python-snappy==0.6.1
tzdata==2023.3
xxhash==2.0.2
pytz==2023.3.post1
pyxdg==0.27
PyYAML==6.0
pyzmq==23.2.0
QDarkStyle==3.0.2
queuelib==1.5.0
regex==2022.7.9
rfc3986-validator==0.1.1
Rtree==1.0.1
ruamel.yaml==0.17.21
ruamel-yaml-conda==0.17.21
safetensors==0.3.2
Send2Trash==1.8.0
setuptools==68.0.0
six==1.16.0
smart-open==5.2.1
sniffio==1.2.0
snowballstemmer==2.2.0
sortedcontainers==2.4.0
soupsieve==2.4
sphinxcontrib-applehelp==1.0.2
sphinxcontrib-devhelp==1.0.2
sphinxcontrib-htmlhelp==2.0.0
sphinxcontrib-jsmath==1.0.1
sphinxcontrib-qthelp==1.0.3
sphinxcontrib-serializinghtml==1.1.5
tabulate==0.8.10
TBB==0.2
tblib==1.7.0
tenacity==8.2.2
text-unidecode==1.3
textdistance==4.2.1
threadpoolctl==2.2.0
tokenizers==0.13.2
toml==0.10.2
tomlkit==0.11.1
toolz==0.12.0
tornado==6.3.2
tqdm==4.65.0
traitlets==5.7.1
typing_extensions==4.7.1
uc-micro-py==1.0.1
ujson==5.4.0
Unidecode==1.2.0
wcwidth==0.2.5
webencodings==0.5.1
whatthepatch==1.0.2
wheel==0.38.4
widgetsnbextension==4.0.5
wrapt==1.14.1
wurlitzer==3.0.2
xyzservices==2022.9.0
y-py==0.5.9
yapf==0.31.0
zipp==3.11.0
aioitertools==0.7.1
aiosignal==1.2.0
anyio==3.5.0
astroid==2.14.2
asttokens==2.0.5
Automat==20.2.0
autopep8==1.6.0
Babel==2.11.0
backports.functools-lru-cache==1.6.4
backports.weakref==1.0.post1
beautifulsoup4==4.12.2
binaryornot==0.4.4
black==0.0
bleach==4.1.0
cffi==1.15.1
clyent==1.2.2
comm==0.1.2
conda-pack==0.6.0
cytoolz==0.12.0
flake8==6.0.0
fonttools==4.25.0
hyperlink==21.0.0
importlib-metadata==6.0.0
intervaltree==3.1.0
jaraco.classes==3.2.1
jedi==0.18.1
Jinja2==3.1.2
jsonpatch==1.32
jsonschema==4.17.3
jupyter_core==5.3.0
jupyter-ydoc==0.2.4
jupyterlab-pygments==0.1.2
libmambapy==1.5.1
linkify-it-py==2.0.0
markdown-it-py==2.2.0
matplotlib-inline==0.1.6
multipledispatch==0.6.0
multiprocess==0.70.14
nltk==3.8.1
numpy==1.24.3
openpyxl==3.0.10
partd==1.4.0
pexpect==4.8.0
pip==23.2.1
plotly==5.9.0
prompt-toolkit==3.0.36
Protego==0.1.16
pyasn1-modules==0.2.8
pydocstyle==6.3.0
pytest==7.4.0
python-dateutil==2.8.2
python-dotenv==0.21.0
kaleido==0.2.1
python-lsp-jsonrpc==1.0.0
python-slugify==5.0.2
pytoolconfig==1.2.5
pyviz-comms==2.3.0
QtPy==2.2.0
rfc3339-validator==0.1.4
sip==6.6.2
SQLAlchemy==1.4.39
sympy==1.11.1
terminado==0.17.1
three-merge==0.1.1
tinycss2==1.2.1
w3lib==1.21.0
watchdog==2.1.6
websocket-client==0.58.0
Werkzeug==2.2.3
yarl==1.8.1
ypy-websocket==0.8.2
zict==2.2.0
zope.interface==5.4.0
aiohttp==3.8.5
argon2-cffi-bindings==21.2.0
arrow==1.2.3
backports.tempfile==1.0
bcrypt==3.2.0
brotlipy==0.7.0
cryptography==41.0.3
dask==2023.6.0
Flask==2.2.2
jupyter_client==7.4.9
jupyter-events==0.6.3
mdit-py-plugins==0.3.0
nbformat==5.9.2
parsel==1.6.0
pydantic==1.10.8
pylint==2.16.2
PyQt5-sip==12.11.0
qstylizer==0.2.2
QtAwesome==1.2.2
rope==1.7.0
stack-data==0.2.0
zstandard==0.19.0
argon2-cffi==21.3.0
conda-content-trust==0.2.0
conda_package_streaming==0.9.0
ipython==8.15.0
itemloaders==1.0.4
jinja2-time==0.2.0
nbclient==0.5.13
pyOpenSSL==23.2.0
python-lsp-server==1.7.2
SecretStorage==3.3.1
service-identity==18.1.0
conda-package-handling==2.2.0
ipykernel==6.25.0
keyring==23.13.1
nbconvert==6.5.4
pyls-spyder==0.4.0
python-lsp-black==1.2.1
Twisted==22.10.0
urllib3==1.26.16
botocore==1.29.76
conda-verify==3.4.2
distributed==2023.6.0
ipywidgets==8.0.4
jupyter-console==6.6.3
jupyter-server==1.23.4
qtconsole==5.4.2
requests==2.31.0
spyder-kernels==2.4.4
aiobotocore==2.5.0
anaconda-cloud-auth==0.1.3
conda==23.7.4
cookiecutter==1.7.3
huggingface-hub==0.15.1
jupyter_server_fileid==0.9.0
jupyterlab_server==2.22.0
notebook_shim==0.2.2
pyct==0.5.0
requests-file==1.5.1
requests-toolbelt==1.0.0
responses==0.13.3
Sphinx==5.0.2
anaconda-client==1.12.1
colorcet==3.0.1
conda_index==0.3.0
conda-libmamba-solver==23.7.0
conda-token==0.4.0
jupyter_server_ydoc==0.8.0
navigator-updater==0.4.0
nbclassic==0.5.5
numpydoc==1.5.0
s3fs==2023.4.0
tldextract==3.2.0
anaconda-project==0.11.1
conda-build==3.26.1
conda-repo-cli==1.0.75
notebook==6.5.4
Scrapy==2.8.0
spyder==5.4.3
anaconda-navigator==2.5.0
jupyterlab==3.6.3
jupyter==1.0.0
anaconda-catalogs==0.2.0
Bottleneck==1.3.5
contourpy==1.0.5
daal4py==2023.1.1
datashape==0.5.4
h5py==3.9.0
imagecodecs==2023.1.23
imageio==2.31.1
intake==0.6.8
matplotlib==3.7.2
mkl-fft==1.3.8
mkl-random==1.2.4
numba==0.57.1
numexpr==2.8.4
patsy==0.5.3
pyarrow==11.0.0
pyerfa==2.0.0
PyWavelets==1.4.1
scipy==1.11.1
tifffile==2023.4.12
astropy==5.1
gensim==4.3.0
pandas==2.0.3
tables==3.8.0
scikit-image==0.20.0
scikit-learn==1.3.0
bokeh==3.2.1
datasets==2.12.0
imbalanced-learn==0.10.1
scikit-learn-intelex==20230426.111612
seaborn==0.12.2
statsmodels==0.14.0
xarray==2023.6.0
datashader==0.15.2
panel==1.2.3
transformers==4.32.1
holoviews==1.17.1
hvplot==0.8.4
platformdirs==4.2.2
autocommand==2.2.2
backports.tarfile==1.2.0
importlib_metadata==8.0.0
inflect==7.3.1
jaraco.collections==5.1.0
jaraco.context==5.3.0
jaraco.functools==4.0.1
jaraco.text==3.12.1
more-itertools==10.3.0
packaging==24.2
tomli==2.0.1
typeguard==4.3.0
typing_extensions==4.12.2
wheel==0.45.1
zipp==3.19.2
