_wandb:
    value:
        cli_version: 0.21.0
        e:
            hh8iq0iv67e5ddnofldohkau89lzz9ju:
                args:
                    - --model_name
                    - meta-llama/Llama-3.1-8B-Instruct
                    - --data_file
                    - data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json
                    - --config_path
                    - experiments/xml_direct_input/configs/llama_t8_2gpu_final.json
                    - --output_dir
                    - experiments/xml_direct_input/outputs/llama_t8_continue
                    - --use_wandb
                    - --wandb_project
                    - svg_caption_llama_resume
                    - --wandb_run_name
                    - llama_t8_resume_simple_20250729_102011
                    - --resume_from_checkpoint
                    - experiments/xml_direct_input/outputs/llama_t8_continue/checkpoint-18750
                codePath: scripts/training/train_lora_simple.py
                codePathLocal: scripts/training/train_lora_simple.py
                cpu_count: 32
                cpu_count_logical: 64
                cudaVersion: "12.2"
                disk:
                    /:
                        total: "210241560576"
                        used: "64546881536"
                email: <EMAIL>
                executable: /homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/python
                gpu: Quadro RTX 5000
                gpu_count: 2
                gpu_nvidia:
                    - architecture: Turing
                      cudaCores: 3072
                      memoryTotal: "17179869184"
                      name: Quadro RTX 5000
                      uuid: GPU-a5f8e62a-a437-c889-3793-2480ac4bdaea
                    - architecture: Turing
                      cudaCores: 3072
                      memoryTotal: "17179869184"
                      name: Quadro RTX 5000
                      uuid: GPU-9320da97-5f25-8a83-f761-603b61d510fe
                host: gervasoni
                memory:
                    total: "************"
                os: Linux-5.15.0-144-generic-x86_64-with-glibc2.35
                program: /work/tesi_ediluzio/scripts/training/train_lora_simple.py
                python: CPython 3.11.5
                root: /work/tesi_ediluzio
                slurm:
                    cluster_name: aimagelab
                    conf: /etc/slurm/slurm.conf
                    cpus_on_node: "16"
                    cpus_per_task: "16"
                    gpus_on_node: "2"
                    gtids: "0"
                    job_account: tesi_ediluzio
                    job_cpus_per_node: "16"
                    job_end_time: "**********"
                    job_gid: "503"
                    job_gpus: 0,3
                    job_id: "2608977"
                    job_name: LLAMA_RESUME_SIMPLE
                    job_nodelist: gervasoni
                    job_num_nodes: "1"
                    job_partition: all_usr_prod
                    job_qos: all_qos_tprod
                    job_start_time: "**********"
                    job_uid: "92359"
                    job_user: ediluzio
                    jobid: "2608977"
                    localid: "0"
                    mem_per_node: "65536"
                    nnodes: "1"
                    nodeid: "0"
                    nodelist: gervasoni
                    nprocs: "1"
                    ntasks: "1"
                    ntasks_per_node: "1"
                    oom_kill_step: "0"
                    prio_process: "0"
                    procid: "0"
                    script_context: prolog_task
                    submit_dir: /work/tesi_ediluzio
                    submit_host: ailb-login-02
                    task_pid: "1878643"
                    tasks_per_node: "1"
                    topology_addr: gervasoni
                    topology_addr_pattern: node
                    tres_per_task: cpu=16
                startedAt: "2025-07-29T08:20:23.639508Z"
                writerId: hh8iq0iv67e5ddnofldohkau89lzz9ju
        m: []
        python_version: 3.11.5
        t:
            "1":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 71
                - 98
            "2":
                - 1
                - 5
                - 11
                - 41
                - 49
                - 51
                - 53
                - 63
                - 71
                - 98
            "3":
                - 13
                - 16
            "4": 3.11.5
            "5": 0.21.0
            "6": 4.54.0
            "12": 0.21.0
            "13": linux-x86_64
data_config:
    value:
        max_length: 2048
        padding: max_length
        truncation: true
lora_config:
    value:
        bias: none
        lora_alpha: 128
        lora_dropout: 0.05
        r: 64
        target_modules:
            - q_proj
            - k_proj
            - v_proj
            - o_proj
            - gate_proj
            - up_proj
            - down_proj
        task_type: CAUSAL_LM
model_name:
    value: meta-llama/Llama-3.1-8B-Instruct
model_type:
    value: llama
quantization:
    value:
        bnb_4bit_compute_dtype: float16
        bnb_4bit_quant_type: nf4
        bnb_4bit_use_double_quant: true
        load_in_4bit: false
training_config:
    value:
        dataloader_num_workers: 4
        dataloader_pin_memory: true
        ddp_find_unused_parameters: false
        eval_steps: 500
        evaluation_strategy: steps
        fp16: true
        gradient_accumulation_steps: 16
        greater_is_better: false
        group_by_length: false
        learning_rate: 0.0002
        load_best_model_at_end: true
        logging_steps: 10
        lr_scheduler_type: cosine
        metric_for_best_model: eval_loss
        num_train_epochs: 3
        per_device_train_batch_size: 1
        remove_unused_columns: false
        report_to:
            - wandb
        save_steps: 250
        save_strategy: steps
        save_total_limit: 3
        warmup_ratio: 0.1
        weight_decay: 0.01
