#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script per convertire i dataset esistenti in formato XML completo.
Utilizza la funzione de_parser per convertire i dati SVG semplificati in XML validi.
VERSIONE CORRETTA CON FUNZIONI DI LEONARDO
"""

import os
import json
import argparse
import logging
import re
from tqdm import tqdm

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def replacer(match):
    """Funzione helper per gestire i colori RGB - da Leonardo"""
    if "stroke" in match.group(0):
        new_s = match.group(0).replace("stroke:","stroke:rgb(") + ")"
    elif "fill" in match.group(0):
        new_s = match.group(0).replace("fill:","fill:rgb(") + ")"
    return new_s

def de_parser(data):
    """
    Funzione di Leonardo corretta per convertire SVG semplificati in XML validi.
    Include gestione colori RGB.
    
    Args:
        data: Stringa SVG semplificata
        
    Returns:
        Stringa XML valida
    """
    # Header
    res = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n<svg viewBox=\"0 0 512 512\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\">\n"

    data = data.replace("style=", "<path style=\"")
    # Gestione colori RGB con regex - correzione di Leonardo
    data = re.sub("stroke\:([0-9]{1,3},?){3}", replacer, data)
    data = re.sub("fill\:([0-9]{1,3},?){3}", replacer, data)
    data = data.replace("\t", "\" d=\"")
    data = data.replace("\n", "Z\" />\n")

    res += data

    # Footer
    res += "</svg>"

    return res

def process_dataset(input_file, output_file):
    """
    Processa un dataset convertendo i dati SVG in formato XML.
    
    Args:
        input_file: Path del file di input
        output_file: Path del file di output
    """
    logger.info(f"📊 Processando: {input_file}")
    logger.info(f"📁 Output: {output_file}")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    logger.info(f"📈 Esempi da processare: {len(data)}")
    
    processed_data = []
    
    for i, item in enumerate(tqdm(data, desc="Convertendo SVG")):
        try:
            # Converte il dato SVG usando de_parser di Leonardo
            if 'svg_data' in item:
                item['svg_data'] = de_parser(item['svg_data'])
            elif 'svg' in item:
                item['svg'] = de_parser(item['svg'])
            
            processed_data.append(item)
            
        except Exception as e:
            logger.error(f"❌ Errore processando esempio {i}: {e}")
            continue
    
    # Salva il dataset processato
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(processed_data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"✅ Processamento completato: {len(processed_data)} esempi salvati")

def main():
    """Funzione principale"""
    parser = argparse.ArgumentParser(description='Converte dataset SVG in formato XML')
    parser.add_argument('--input', required=True, help='File di input')
    parser.add_argument('--output', required=True, help='File di output')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        logger.error(f"❌ File di input non trovato: {args.input}")
        return
    
    # Crea directory di output se non esiste
    os.makedirs(os.path.dirname(args.output), exist_ok=True)
    
    # Processa il dataset
    process_dataset(args.input, args.output)

if __name__ == "__main__":
    main()
