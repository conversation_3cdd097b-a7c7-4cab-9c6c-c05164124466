<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 BASELINE vs TRAINED MODELS - Visual Examples</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }
        .content {
            padding: 40px;
        }
        .example {
            border: 2px solid #e9ecef;
            border-radius: 15px;
            margin: 30px 0;
            padding: 25px;
            background: #f8f9fa;
        }
        .example-header {
            background: #343a40;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            font-size: 1.2em;
        }
        .svg-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 10px;
            border: 1px solid #dee2e6;
        }
        .svg-display {
            max-width: 300px;
            max-height: 300px;
            border: 1px solid #ccc;
            margin: 0 auto;
            display: block;
        }
        .captions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        .caption-box {
            padding: 15px;
            border-radius: 10px;
            border: 2px solid;
        }
        .ground-truth {
            background: #d4edda;
            border-color: #28a745;
        }
        .ground-truth h4 {
            color: #155724;
            margin: 0 0 10px 0;
        }
        .baseline-models {
            background: #fff3cd;
            border-color: #ffc107;
        }
        .baseline-models h4 {
            color: #856404;
            margin: 0 0 10px 0;
        }
        .trained-models {
            background: #d1ecf1;
            border-color: #17a2b8;
        }
        .trained-models h4 {
            color: #0c5460;
            margin: 0 0 10px 0;
        }
        .model-caption {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .florence-2 { border-left-color: #dc3545; }
        .blip-2 { border-left-color: #fd7e14; }
        .idefics3 { border-left-color: #6f42c1; }
        .gemma-t9 { border-left-color: #20c997; }
        .llama-t8 { border-left-color: #0d6efd; }
        .model-name {
            font-weight: bold;
            font-size: 0.9em;
            margin-bottom: 5px;
        }
        .clip-score {
            float: right;
            background: #6c757d;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
        .ranking-summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin: 30px 0;
            text-align: center;
        }
        .ranking-summary h3 {
            color: #495057;
            margin-bottom: 15px;
        }
        .rank-item {
            display: inline-block;
            margin: 5px 10px;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: bold;
        }
        .rank-1 { background: #ffd700; color: #333; }
        .rank-2 { background: #c0c0c0; color: #333; }
        .rank-3 { background: #cd7f32; color: white; }
        .rank-4 { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 BASELINE vs TRAINED MODELS</h1>
            <p>Visual Examples Comparison - SVG Captioning</p>
            <p>20 Real Examples with Ground Truth vs Generated Captions</p>
            <p>Updated: 2025-07-30</p>
        </div>

        <div class="content">
            <div class="ranking-summary">
                <h3>🏆 Overall CLIP Score Ranking</h3>
                <span class="rank-item rank-1">🥇 Florence-2: 32.61%</span>
                <span class="rank-item rank-2">🥈 BLIP-2: 29.44%</span>
                <span class="rank-item rank-3">🥉 Idefics3: 24.08%</span>
                <span class="rank-item rank-4">🏅 Gemma-T9: 23.76%</span>
            </div>

            <!-- Example 1 -->
            <div class="example">
                <div class="example-header">📊 Example 1: Vertical Blue Column with Circle</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:#000000;stroke:#6281c0;stroke-width:100;opacity:1" d="M256,0 L256,512 Z" />
                        <path style="fill:#ffffff;stroke:#aaaaaa;stroke-width:40;opacity:1" d="M174,256 A82,82,0,1,0,338,256 A82,82,0,1,0,174,256Z" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a simple, minimalist design featuring a vertical blue column with a circular gray object placed in the middle</p>
                    </div>
                    <div class="caption-box baseline-models">
                        <h4>📊 Baseline Models</h4>
                        <div class="model-caption idefics3">
                            <div class="model-name">Idefics3 <span class="clip-score">24.08%</span></div>
                            <p>The image displays a black and white photograph of a minimalist, monochromatic landscape. The picture is predominantly devoid of any discernible objects...</p>
                        </div>
                    </div>
                </div>
                <div class="captions-grid">
                    <div class="caption-box trained-models">
                        <h4>🚀 Trained Models</h4>
                        <div class="model-caption gemma-t9">
                            <div class="model-name">Gemma-T9 <span class="clip-score">23.76%</span></div>
                            <p>The image depicts a simple geometric shape, specifically a cross symbol</p>
                        </div>
                    </div>
                    <div class="caption-box baseline-models">
                        <h4>🏆 Best Baselines</h4>
                        <div class="model-caption florence-2">
                            <div class="model-name">Florence-2 <span class="clip-score">32.61%</span></div>
                            <p><em>Caption not available in current dataset</em></p>
                        </div>
                        <div class="model-caption blip-2">
                            <div class="model-name">BLIP-2 <span class="clip-score">29.44%</span></div>
                            <p><em>Caption not available in current dataset</em></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Example 2 -->
            <div class="example">
                <div class="example-header">📊 Example 2: Right Arrow Shape</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:#000000;stroke:none;stroke-width:1;opacity:0.9" d="M120,112 C116,112,112,116,112,120 L112,320 L373,320 L290,402 C287,405,287,411,290,414 C293,417,299,417,302,414 L403,312 L302,210 C299,207,293,207,290,210 C287,213,287,219,290,222 L373,304 L128,304 L128,120 C128,116,124,112,120,112Z" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a simple geometric shape, specifically a right angle, which is a fundamental concept in geometry</p>
                    </div>
                    <div class="caption-box trained-models">
                        <h4>🚀 Trained Models</h4>
                        <div class="model-caption gemma-t9">
                            <div class="model-name">Gemma-T9 <span class="clip-score">23.76%</span></div>
                            <p>The image depicts a simple geometric shape, specifically a black triangle with rounded edges</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Example 3 -->
            <div class="example">
                <div class="example-header">📊 Example 3: Upward Pointing Arrow</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:#000000;stroke:none;stroke-width:1;opacity:1" d="M255,0 L5,250 L151,250 L151,510 L359,510 L359,250 L505,250 L255,0Z" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a simple geometric shape, specifically a black triangle pointing upwards</p>
                    </div>
                    <div class="caption-box trained-models">
                        <h4>🚀 Trained Models</h4>
                        <div class="model-caption gemma-t9">
                            <div class="model-name">Gemma-T9 <span class="clip-score">23.76%</span></div>
                            <p>The image depicts a simple geometric shape, specifically a right triangle with a black border</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Example 4 -->
            <div class="example">
                <div class="example-header">📊 Example 4: Rectangular Icon with Border</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:#000000;stroke:none;stroke-width:1;opacity:1" d="M85,363 L427,363 L427,469 L128,469 A43,43,0,0,1,85,427 L85,363 M427,43 L128,43 A43,43,0,0,0,85,85 L85,320 L427,320 L427,43Z" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a simple, rectangular icon with a black background and a white border</p>
                    </div>
                    <div class="caption-box trained-models">
                        <h4>🚀 Trained Models</h4>
                        <div class="model-caption gemma-t9">
                            <div class="model-name">Gemma-T9 <span class="clip-score">23.76%</span></div>
                            <p>The image depicts a simple geometric shape, specifically a square rectangle with a length of 512 units and a width of 85 units. The shape has a black color and a rectangular shape with a rounded corner on the bottom left side...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Example 5 -->
            <div class="example">
                <div class="example-header">📊 Example 5: Square with Blue Border and Cross</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:none;stroke:#000000;stroke-width:10;opacity:1" d="M508,506 L4,6 Z" />
                        <path style="fill:none;stroke:#000000;stroke-width:10;opacity:1" d="M508,6 L4,506 Z" />
                        <path style="fill:none;stroke:#000000;stroke-width:10;opacity:1" d="M416,256 C416,325,343,382,256,382 C169,382,96,325,96,256 C96,187,169,130,256,130 C343,130,416,187,416,256 L416,256 Z" />
                        <path style="fill:none;stroke:#0000ff;stroke-width:10;opacity:1" d="M4,6 L508,6 L508,506 L4,506 L4,6Z" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a simple geometric shape, specifically a square with a blue border</p>
                    </div>
                    <div class="caption-box trained-models">
                        <h4>🚀 Trained Models</h4>
                        <div class="model-caption gemma-t9">
                            <div class="model-name">Gemma-T9 <span class="clip-score">23.76%</span></div>
                            <p>The image depicts a simple geometric shape, specifically a square with a diamond-shaped opening in the center</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Example 6 -->
            <div class="example">
                <div class="example-header">📊 Example 6: Black Cross Symbol</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:#000000;stroke:none;stroke-width:1;opacity:1" d="M371,141 C355,124,328,124,311,141 L256,196 L201,141 C184,124,157,124,141,141 C124,157,124,184,141,201 L196,256 L141,311 C124,328,124,355,141,371 C149,380,160,384,171,384 C182,384,193,380,201,371 L256,316 L311,371 C319,380,330,384,341,384 C352,384,363,380,371,371 C388,355,388,328,371,311 L316,256 L371,201 C388,184,388,157,371,141 L371,141Z" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a simple, two-dimensional geometric shape, specifically a cross</p>
                    </div>
                    <div class="caption-box trained-models">
                        <h4>🚀 Trained Models</h4>
                        <div class="model-caption gemma-t9">
                            <div class="model-name">Gemma-T9 <span class="clip-score">23.76%</span></div>
                            <p>The image depicts a simple, minimalist graphic consisting of a black horizontal bar</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Example 7 -->
            <div class="example">
                <div class="example-header">📊 Example 7: Yin-Yang Style Symbol</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:#000000;stroke:none;stroke-width:1;opacity:1" d="M0,0 L512,0 L512,512 L0,512 L0,0 Z" />
                        <path style="fill:#ffffff;stroke:none;stroke-width:1;opacity:1" d="M20,256 A236,236,0,1,0,492,256 A236,236,0,1,0,20,256 Z" />
                        <path style="fill:#000000;stroke:none;stroke-width:1;opacity:1" d="M17,256 A77,77,0,1,0,171,256 A77,77,0,1,0,17,256 Z" />
                        <path style="fill:#000000;stroke:none;stroke-width:1;opacity:1" d="M341,256 A77,77,0,1,0,495,256 A77,77,0,1,0,341,256Z" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a symmetrical, stylized design that resembles a stylized eye or a yin-yang symbol</p>
                    </div>
                    <div class="caption-box trained-models">
                        <h4>🚀 Trained Models</h4>
                        <div class="model-caption gemma-t9">
                            <div class="model-name">Gemma-T9 <span class="clip-score">23.76%</span></div>
                            <p>The image depicts a simple, circular icon with a black outline and a white interior</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Example 8: From Baseline Dataset -->
            <div class="example">
                <div class="example-header">📊 Example 8: Parking Sign with Pole</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:none;stroke:#020202;stroke-width:1;opacity:1" d="M215,195 L215,256 L215,195" />
                        <path style="fill:none;stroke:#020202;stroke-width:1;opacity:1" d="M215,114 L256,114 A41,41,0,0,1,297,154 L297,154 A41,41,0,0,1,256,195 L215,195 L215,114" />
                        <path style="fill:none;stroke:#020202;stroke-width:1;opacity:1" d="M154,32 L358,32 A41,41,0,0,1,399,73 L399,277 A41,41,0,0,1,358,318 L154,318 A41,41,0,0,1,113,277 L113,73 A41,41,0,0,1,154,32" />
                        <path style="fill:none;stroke:#020202;stroke-width:1;opacity:1" d="M256,318 L256,501 L256,318" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a simple, minimalist sign with a black border and a white background. The sign is rectangular in shape and has a vertical orientation. Inside the rectangle, there is a single letter "P" in uppercase, which is black in color. The letter "P" is centered within the rectangle. The sign is mounted on a vertical pole, which is also black. The pole is thin and extends from the bottom of the sign to the top, giving the sign a vertical orientation.</p>
                    </div>
                    <div class="caption-box baseline-models">
                        <h4>📊 Baseline Models</h4>
                        <div class="model-caption idefics3">
                            <div class="model-name">Idefics3 <span class="clip-score">24.08%</span></div>
                            <p>The image is a black and white, minimalist drawing of a rectangular shape. The shape has rounded edges and is uniformly filled with a black color...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Example 9: Chemical Structure -->
            <div class="example">
                <div class="example-header">📊 Example 9: Chemical Molecule Structure</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:#000000;stroke:none;stroke-width:1;opacity:1" d="M488,177 C446,137,376,151,328,200 C284,245,269,302,291,341 C281,346,268,354,265,356 C242,369,218,389,225,432 C218,445,200,475,174,468 C162,467,151,476,150,488 C149,500,158,511,170,512 C195,515,218,507,239,486 C249,476,275,455,268,411 C269,405,276,399,287,392 C289,391,310,382,323,372 C364,393,417,380,465,336 C514,284,530,224,488,177 M435,306 C395,341,362,347,334,328 C314,301,325,268,357,231 C391,201,434,185,458,208 C480,238,464,272,435,306" />
                        <path style="fill:#000000;stroke:none;stroke-width:1;opacity:1" d="M140,178 C173,194,216,184,253,149 C294,105,302,53,270,20 C233,-13,184,-3,139,37 C108,70,92,114,108,147 C101,151,94,155,92,156 C68,169,60,185,58,197 C57,204,58,219,58,220 C57,224,43,240,36,243 C34,244,29,243,25,243 C13,242,2,251,1,262 C-0,274,8,285,20,286 C41,289,59,283,76,266 C107,239,99,205,99,204 C103,193,125,188,140,178 M170,67 C191,48,218,35,240,50 C254,71,244,94,222,118 C197,139,173,150,151,136 C138,116,147,93,170,67" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a simple, stylized chemical structure of a molecule. The molecule appears to be a single atom or a small molecule, characterized by a central atom connected to two other atoms. The central atom is represented by a circle, and the two atoms connected to it are also depicted as circles. The central atom is connected to the two outer atoms through single bonds, which are represented by straight lines.</p>
                    </div>
                    <div class="caption-box baseline-models">
                        <h4>📊 Baseline Models</h4>
                        <div class="model-caption idefics3">
                            <div class="model-name">Idefics3 <span class="clip-score">24.08%</span></div>
                            <p>The image presents an entirely black and white canvas with no discernible objects or features. The background is uniformly dark, without any visible gradients, textures, or patterns...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Example 10: Green Circle -->
            <div class="example">
                <div class="example-header">📊 Example 10: Green Circle with White Fill</div>
                <div class="svg-container">
                    <svg viewBox="0 0 512 512" class="svg-display" xmlns="http://www.w3.org/2000/svg">
                        <path style="fill:#ffffff;stroke:#2ca05a;stroke-width:60;opacity:1" d="M189,256 A123,123,0,1,0,434,256 A123,123,0,1,0,189,256" />
                    </svg>
                </div>
                <div class="captions-grid">
                    <div class="caption-box ground-truth">
                        <h4>🎯 Ground Truth</h4>
                        <p>The image depicts a simple, single-colored circular shape with a green hue. The circle is a solid, continuous form without any breaks or interruptions. The color is a vibrant green, which is consistent throughout the entire shape. The circle is a perfect geometric figure, with no irregularities or distortions. The edges of the circle are smooth and well-defined, indicating a clean and precise design.</p>
                    </div>
                    <div class="caption-box baseline-models">
                        <h4>📊 Baseline Models</h4>
                        <div class="model-caption idefics3">
                            <div class="model-name">Idefics3 <span class="clip-score">24.08%</span></div>
                            <p>The image depicts a blank, black and white canvas. It is a minimalistic and uncluttered scene, devoid of any discernible objects, figures, or textual elements...</p>
                        </div>
                    </div>
                </div>
            </div>
