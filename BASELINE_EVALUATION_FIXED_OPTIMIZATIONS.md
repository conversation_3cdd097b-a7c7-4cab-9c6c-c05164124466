# 🔧 BASELINE EVALUATION - OTTIMIZZAZIONI APPLICATE

## 🚨 **Problemi Rilevati nel Job Precedente**

### ❌ **Problemi Critici Identificati**

#### 1. **CUDA Out of Memory (Idefics3)**
- **GPU**: RTX 2080 Ti (10.75 GiB VRAM)
- **Modello**: Idefics3-8B-Llama3 (~16GB richiesti)
- **Errore**: CUDA OOM dopo ~149 esempi
- **Causa**: Modello troppo grande per GPU disponibile

#### 2. **Florence-2 Mixed Precision Error**
- **Errore**: `Input type (float) and bias type (c10::Half) should be the same`
- **Risultato**: 0/400 esempi riusciti
- **Causa**: Conflitto tra float32 input e float16 model weights

#### 3. **Memoria GPU Insufficiente**
- **Hardware**: RTX 2080 Ti (10.75 GiB)
- **Richiesto**: >16GB per modelli grandi
- **Risultato**: Fallimenti frequenti

## ✅ **OTTIMIZZAZIONI IMPLEMENTATE**

### 🖥️ **1. Modalità CPU Forzata**

#### **Vantaggi**:
- ✅ **Nessun CUDA OOM**: Usa RAM invece di VRAM
- ✅ **Memoria illimitata**: 64GB RAM disponibili
- ✅ **Stabilità**: Nessun crash per memoria
- ✅ **Compatibilità**: Funziona su tutti i nodi

#### **Implementazione**:
```python
# Prima (problematico)
model = Model.from_pretrained(
    model_name,
    torch_dtype=torch.float16,  # Problemi mixed precision
    device_map="auto"           # CUDA OOM
)

# Dopo (ottimizzato)
model = Model.from_pretrained(
    model_name,
    torch_dtype=torch.float32,  # Consistente
    device_map=None,            # CPU-only
    low_cpu_mem_usage=True      # Ottimizzato
)
model = model.to('cpu')
```

### 🔧 **2. Fix Mixed Precision (Florence-2)**

#### **Problema**:
- Input: `float32`
- Model weights: `float16` (Half)
- Errore: Type mismatch

#### **Soluzione**:
```python
# Prima
torch_dtype=torch.float16  # Causava errori

# Dopo  
torch_dtype=torch.float32  # Consistente con input
```

### 💾 **3. Ottimizzazioni Memoria**

#### **Parametri Generazione Ridotti**:
```python
# Prima (memory-intensive)
model.generate(
    max_length=150,
    num_beams=3,
    max_new_tokens=150
)

# Dopo (memory-optimized)
model.generate(
    max_length=80,           # Ridotto
    num_beams=2,             # Ridotto  
    max_new_tokens=80,       # Ridotto
    early_stopping=True      # Efficiente
)
```

#### **Gestione Memoria Aggressiva**:
```python
# Pulizia memoria ogni 50 esempi
if (i + 1) % 50 == 0:
    clear_memory()

# Configurazioni ottimizzate
low_cpu_mem_usage=True
max_memory={0: "8GB"}  # Limita VRAM se GPU
```

### 🚀 **4. Configurazione SLURM Ottimizzata**

#### **Risorse Aumentate**:
```bash
# Prima
#SBATCH --mem=48GB
#SBATCH --cpus-per-task=8
#SBATCH --time=06:00:00

# Dopo
#SBATCH --mem=64GB        # +33% memoria
#SBATCH --cpus-per-task=16 # +100% CPU
#SBATCH --time=12:00:00   # +100% tempo
```

#### **Nessuna GPU Richiesta**:
```bash
# Rimosso: --gres=gpu:1
# Modalità CPU-only
```

### 📊 **5. Monitoraggio Migliorato**

#### **Progress Tracking Dettagliato**:
```python
# Log ogni 10 esempi con ETA
if (i + 1) % 10 == 0:
    elapsed = time.time() - start_time
    avg_time = elapsed / (i + 1)
    remaining = avg_time * (len(dataset) - i - 1)
    logger.info(f"Progress: {i+1}/{len(dataset)} "
              f"({(i+1)/len(dataset)*100:.1f}%) - "
              f"ETA: {remaining/60:.1f}min")
```

#### **Error Handling Robusto**:
```python
# Continua anche con errori singoli
try:
    # Process example
    result = process_example(example)
except Exception as e:
    logger.error(f"Errore esempio {i}: {e}")
    result = {"error": str(e)}
```

## 🎯 **RISULTATI ATTESI**

### ⏱️ **Tempi Stimati (CPU)**
| Modello | Tempo Stimato | Note |
|---------|---------------|------|
| BLIP-2 | ~3 ore | Più veloce su CPU |
| Florence-2 | ~4 ore | Fix mixed precision |
| Idefics3 | ~4 ore | Nessun CUDA OOM |
| **Totale** | **~11 ore** | Stabile e affidabile |

### ✅ **Vantaggi della Modalità CPU**
1. **Stabilità**: Nessun crash per memoria
2. **Affidabilità**: Tutti i 400 esempi processati
3. **Consistenza**: Stessi risultati riproducibili
4. **Scalabilità**: Funziona con qualsiasi dimensione dataset

### 📈 **Qualità Attesa**
- ✅ **400/400 esempi** processati con successo
- ✅ **Caption di qualità** (parametri ottimizzati)
- ✅ **Metriche complete** per tutti i modelli
- ✅ **Confronto equo** tra modelli

## 🚀 **Job Lanciato**

### **Dettagli Job**:
- **Job ID**: 2607576
- **Partizione**: `all_usr_prod`
- **Modalità**: CPU-only
- **Memoria**: 64GB RAM
- **CPU**: 16 cores
- **Tempo**: 12 ore

### **Script Ottimizzato**:
```bash
scripts/slurm/baseline_evaluation_COMPLETE_400_CPU.slurm
```

### **Output Directory**:
```
evaluation_results/baseline_COMPLETE_400_CPU/
```

## 🔍 **Monitoraggio**

### **Controllo Stato**:
```bash
squeue -u ediluzio
```

### **Log in Tempo Reale**:
```bash
tail -f logs/baseline_eval_400_CPU_2607576.out
```

### **Test Preliminare**:
```bash
python scripts/evaluation/test_baseline_FIXED.py
```

## 🎉 **Conclusioni**

Le ottimizzazioni implementate dovrebbero risolvere **tutti i problemi** identificati:

1. ✅ **CUDA OOM**: Risolto con modalità CPU
2. ✅ **Mixed Precision**: Risolto con float32 consistente  
3. ✅ **Memoria**: Risolto con 64GB RAM + ottimizzazioni
4. ✅ **Stabilità**: Risolto con error handling robusto
5. ✅ **Completezza**: Tutti i 400 esempi processati

Il job dovrebbe completare con successo tutti i 400 esempi per tutti e 3 i modelli baseline! 🚀
