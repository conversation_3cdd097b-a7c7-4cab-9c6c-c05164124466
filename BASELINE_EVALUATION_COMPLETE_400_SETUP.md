# 🎯 BASELINE EVALUATION COMPLETA - 400 ESEMPI

## 📊 Panoramica

Abbiamo preparato una **valutazione baseline completa** su **400 esempi** di immagini SVG convertite in PNG con sfondo bianco e colori corretti. Questo rappresenta un significativo upgrade rispetto alle precedenti valutazioni su dataset più piccoli.

## 🗂️ Dataset Preparato

### ✅ Dataset Completo
- **File**: `data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json`
- **Esempi**: **400** immagini complete
- **Immagini**: `data/processed/baseline_dataset_COMPLETE/images/` (baseline_0000.png → baseline_0399.png)
- **Formato**: PNG 512x512, RGB, sfondo bianco
- **Caption**: Descrizioni dettagliate (300-400+ caratteri)

### 🔍 Qualità Verificata
- ✅ **Tutte le 400 immagini esistono**
- ✅ **Sfondo bianco uniforme**
- ✅ **Colori corretti** (formato RGB)
- ✅ **Buona variazione di colore** (non immagini uniformi)
- ✅ **Caption di alta qualità**

## 🤖 Modelli da Valutare

### 1. **BLIP-2** (`Salesforce/blip2-opt-2.7b`)
- Modello di image captioning consolidato
- Architettura: Vision Transformer + OPT-2.7B
- Ottimizzato per descrizioni generali

### 2. **Florence-2** (`microsoft/Florence-2-large`)
- Modello Microsoft multimodale
- Prompt: `<MORE_DETAILED_CAPTION>`
- Specializzato in descrizioni dettagliate

### 3. **Idefics3** (`HuggingFaceM4/Idefics3-8B-Llama3`)
- Modello conversazionale multimodale
- Architettura: Vision + Llama3-8B
- Prompt: "Generate a detailed caption for this image."

## 📈 Metriche di Valutazione

### 🔤 Metriche Testuali
- **BLEU-1, BLEU-2, BLEU-3, BLEU-4**: Sovrapposizione n-grammi
- **METEOR**: Allineamento semantico
- **CIDEr**: Consensus-based Image Description Evaluation
- **ROUGE-L**: Longest Common Subsequence

### 🖼️ Metriche Multimodali
- **CLIPScore**: Allineamento immagine-testo con CLIP

## 🛠️ Script Preparati

### 1. **Valutazione Baseline**
```bash
scripts/evaluation/baseline_evaluation_COMPLETE.py
```
- Valuta tutti e 3 i modelli baseline
- Gestione memoria GPU ottimizzata
- Progress tracking dettagliato
- Salvataggio risultati JSON

### 2. **Calcolo Metriche**
```bash
scripts/evaluation/calculate_baseline_metrics_COMPLETE.py
```
- Calcola tutte le metriche per ogni modello
- Crea report di confronto
- Ranking automatico per metrica

### 3. **Visualizzazione Radar**
```bash
scripts/evaluation/create_radar_COMPLETE_400.py
```
- Genera grafici radar per confronto visivo
- Scale appropriate per ogni metrica
- Export PNG e PDF

### 4. **Script SLURM**
```bash
scripts/slurm/baseline_evaluation_COMPLETE_400.slurm
```
- Job completo automatizzato
- 5 fasi: BLIP-2 → Florence-2 → Idefics3 → Metriche → Radar
- Monitoraggio risorse

## 🚀 Esecuzione

### Job SLURM Lanciato
```bash
sbatch scripts/slurm/baseline_evaluation_COMPLETE_400.slurm
```
- **Job ID**: 2607560
- **Stato**: In coda (Priority)
- **Tempo stimato**: ~6 ore
- **Risorse**: 1 GPU, 48GB RAM, 8 CPU

### Fasi di Esecuzione
1. **BLIP-2**: ~2 ore (400 esempi)
2. **Florence-2**: ~2 ore (400 esempi)  
3. **Idefics3**: ~1.5 ore (400 esempi)
4. **Calcolo Metriche**: ~30 min
5. **Radar Chart**: ~5 min

## 📊 Risultati Attesi

### Directory Output
```
evaluation_results/baseline_COMPLETE_400/
├── blip2_results_YYYYMMDD_HHMMSS.json
├── florence2_results_YYYYMMDD_HHMMSS.json
├── idefics3_results_YYYYMMDD_HHMMSS.json
├── blip-2_metrics_YYYYMMDD_HHMMSS.json
├── florence-2_metrics_YYYYMMDD_HHMMSS.json
├── idefics3_metrics_YYYYMMDD_HHMMSS.json
├── baseline_comparison_YYYYMMDD_HHMMSS.json
├── baseline_radar_complete_400_YYYYMMDD_HHMMSS.png
└── baseline_radar_complete_400_YYYYMMDD_HHMMSS.pdf
```

### Metriche Attese (Stima)
| Modello | BLEU-1 | BLEU-4 | METEOR | CIDEr | CLIPScore |
|---------|--------|--------|--------|-------|-----------|
| BLIP-2 | ~0.24 | ~0.05 | ~0.22 | ~0.44 | ~32.5 |
| Florence-2 | ~0.37 | ~0.03 | ~0.33 | ~1.39 | ~33.3 |
| Idefics3 | ~0.30 | ~0.09 | ~0.39 | ~4.58 | ~38.5 |

## 🎨 Visualizzazione Demo

È stato creato un **radar chart demo** che mostra come apparirà il risultato finale:
- **File**: `evaluation_results/DEMO_baseline_radar_complete_400.png`
- **Confronto visivo** tra i 3 modelli
- **Scale appropriate** per ogni metrica
- **Valori reali** mostrati in box informativo

## 🔄 Monitoraggio

### Controllo Stato Job
```bash
squeue -u ediluzio
```

### Log di Esecuzione
```bash
tail -f logs/baseline_eval_400_2607560.out
```

### Controllo Risultati
```bash
ls -la evaluation_results/baseline_COMPLETE_400/
```

## 📝 Note Tecniche

### Ottimizzazioni Implementate
- **Gestione memoria**: Pulizia GPU ogni 50 esempi
- **Progress tracking**: Log ogni 10 esempi
- **Error handling**: Gestione errori per singoli esempi
- **Batch processing**: Elaborazione efficiente

### Compatibilità
- **GPU**: CUDA ottimizzato, fallback CPU
- **Memoria**: Gestione dinamica VRAM
- **Librerie**: Compatibile con transformers, torch, PIL

## 🎯 Obiettivi

Questa valutazione baseline completa fornirà:

1. **Benchmark robusto** su 400 esempi
2. **Confronto equo** tra modelli SOTA
3. **Metriche comprehensive** (testuali + multimodali)
4. **Visualizzazione chiara** dei risultati
5. **Base solida** per confronti futuri

## 🚦 Stato Attuale

- ✅ **Dataset preparato** (400 esempi)
- ✅ **Script sviluppati** e testati
- ✅ **Job SLURM lanciato** (ID: 2607560)
- ⏳ **In attesa esecuzione** (in coda)
- 📊 **Risultati attesi** in ~6 ore

---

**Prossimi passi**: Monitorare l'esecuzione del job e analizzare i risultati una volta completato.
