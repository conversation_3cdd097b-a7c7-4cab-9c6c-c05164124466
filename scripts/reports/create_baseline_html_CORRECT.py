#!/usr/bin/env python3
"""
📄 REPORT HTML BASELINE CORRETTO
Genera report HTML qualitativo per i risultati baseline corretti
"""

import os
import json
import argparse
import logging
import base64
from datetime import datetime
from PIL import Image

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_results(results_file):
    """Carica risultati da file JSON"""
    try:
        with open(results_file, 'r') as f:
            data = json.load(f)
        return data
    except Exception as e:
        logger.error(f"❌ Errore caricamento {results_file}: {e}")
        return []

def image_to_base64(image_path):
    """Converte immagine in base64 per embedding HTML"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        return base64.b64encode(image_data).decode('utf-8')
    except Exception as e:
        logger.warning(f"⚠️ Errore conversione immagine {image_path}: {e}")
        return None

def create_html_report(all_results, output_path, max_examples=50):
    """
    Crea report HTML qualitativo completo
    """
    logger.info(f"📄 Creazione report HTML con max {max_examples} esempi...")
    
    # Header HTML
    html_content = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>📊 Baseline Models Qualitative Report</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
                line-height: 1.6;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 0 20px rgba(0,0,0,0.1);
            }}
            h1 {{
                color: #2c3e50;
                text-align: center;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
            }}
            h2 {{
                color: #34495e;
                border-left: 4px solid #3498db;
                padding-left: 15px;
                margin-top: 30px;
            }}
            .summary {{
                background-color: #ecf0f1;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
            }}
            .example {{
                border: 1px solid #ddd;
                margin: 20px 0;
                border-radius: 8px;
                overflow: hidden;
                background-color: #fafafa;
            }}
            .example-header {{
                background-color: #34495e;
                color: white;
                padding: 10px 15px;
                font-weight: bold;
            }}
            .example-content {{
                padding: 15px;
                display: grid;
                grid-template-columns: 200px 1fr;
                gap: 20px;
                align-items: start;
            }}
            .image-container {{
                text-align: center;
            }}
            .image-container img {{
                max-width: 180px;
                max-height: 180px;
                border: 2px solid #ddd;
                border-radius: 5px;
                background-color: white;
            }}
            .predictions {{
                display: flex;
                flex-direction: column;
                gap: 15px;
            }}
            .prediction {{
                padding: 10px;
                border-radius: 5px;
                border-left: 4px solid #3498db;
            }}
            .ground-truth {{
                background-color: #d5f4e6;
                border-left-color: #27ae60;
                font-weight: bold;
            }}
            .model-blip2 {{
                background-color: #ffeaa7;
                border-left-color: #fdcb6e;
            }}
            .model-florence2 {{
                background-color: #fab1a0;
                border-left-color: #e17055;
            }}
            .model-idefics3 {{
                background-color: #a29bfe;
                border-left-color: #6c5ce7;
            }}
            .model-error {{
                background-color: #ffcccc;
                border-left-color: #e74c3c;
                color: #c0392b;
            }}
            .model-name {{
                font-weight: bold;
                margin-bottom: 5px;
                text-transform: uppercase;
                font-size: 0.9em;
            }}
            .stats {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin: 20px 0;
            }}
            .stat-card {{
                background-color: #3498db;
                color: white;
                padding: 15px;
                border-radius: 8px;
                text-align: center;
            }}
            .stat-number {{
                font-size: 2em;
                font-weight: bold;
                display: block;
            }}
            .navigation {{
                position: fixed;
                top: 20px;
                right: 20px;
                background-color: #2c3e50;
                color: white;
                padding: 10px;
                border-radius: 5px;
                z-index: 1000;
            }}
            .navigation a {{
                color: #3498db;
                text-decoration: none;
                display: block;
                margin: 5px 0;
            }}
            .navigation a:hover {{
                text-decoration: underline;
            }}
            @media (max-width: 768px) {{
                .example-content {{
                    grid-template-columns: 1fr;
                }}
                .container {{
                    padding: 15px;
                }}
            }}
        </style>
    </head>
    <body>
        <div class="navigation">
            <strong>📋 Navigation</strong>
            <a href="#summary">Summary</a>
            <a href="#examples">Examples</a>
        </div>
        
        <div class="container">
            <h1>📊 Baseline Models Qualitative Report</h1>
            <p style="text-align: center; color: #7f8c8d;">
                <strong>Generated:</strong> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br>
                <strong>Task:</strong> SVG Captioning with Corrected Parameters
            </p>
    """
    
    # Summary section
    html_content += '<div id="summary"><h2>📈 Summary</h2><div class="summary">'
    
    # Statistiche per modello
    model_stats = {}
    total_examples = 0
    
    for model_name, results in all_results.items():
        if not results:
            continue
            
        model_stats[model_name] = {
            'total': len(results),
            'successful': len([r for r in results if not r.get('prediction', '').startswith('[') or 'Error' not in r.get('prediction', '')]),
            'errors': len([r for r in results if r.get('prediction', '').startswith('[') and 'Error' in r.get('prediction', '')])
        }
        total_examples = max(total_examples, len(results))
    
    # Cards statistiche
    html_content += '<div class="stats">'
    html_content += f'<div class="stat-card"><span class="stat-number">{len(all_results)}</span>Models Evaluated</div>'
    html_content += f'<div class="stat-card"><span class="stat-number">{total_examples}</span>Total Examples</div>'
    
    successful_models = len([m for m, s in model_stats.items() if s['successful'] > 0])
    html_content += f'<div class="stat-card"><span class="stat-number">{successful_models}</span>Successful Models</div>'
    html_content += '</div>'
    
    # Dettagli per modello
    for model_name, stats in model_stats.items():
        success_rate = (stats['successful'] / stats['total'] * 100) if stats['total'] > 0 else 0
        html_content += f"""
        <p><strong>{model_name}:</strong> 
        {stats['successful']}/{stats['total']} successful predictions ({success_rate:.1f}%), 
        {stats['errors']} errors</p>
        """
    
    html_content += '</div></div>'
    
    # Examples section
    html_content += '<div id="examples"><h2>🖼️ Qualitative Examples</h2>'
    
    # Prendi esempi da tutti i modelli (assumendo stesso ordine)
    if all_results:
        first_model = list(all_results.keys())[0]
        examples_to_show = min(max_examples, len(all_results[first_model]))
        
        for i in range(examples_to_show):
            html_content += f'<div class="example">'
            html_content += f'<div class="example-header">Example {i+1}</div>'
            html_content += '<div class="example-content">'
            
            # Immagine
            html_content += '<div class="image-container">'
            
            # Prendi path immagine dal primo modello disponibile
            image_path = None
            for model_name, results in all_results.items():
                if i < len(results) and 'image_path' in results[i]:
                    image_path = results[i]['image_path']
                    break
            
            if image_path and os.path.exists(image_path):
                image_b64 = image_to_base64(image_path)
                if image_b64:
                    html_content += f'<img src="data:image/png;base64,{image_b64}" alt="Example {i+1}">'
                else:
                    html_content += f'<p>Image: {os.path.basename(image_path)}</p>'
            else:
                html_content += '<p>Image not available</p>'
            
            html_content += '</div>'
            
            # Predizioni
            html_content += '<div class="predictions">'
            
            # Ground truth (dal primo modello disponibile)
            ground_truth = None
            for model_name, results in all_results.items():
                if i < len(results) and 'ground_truth' in results[i]:
                    ground_truth = results[i]['ground_truth']
                    break
            
            if ground_truth:
                html_content += f"""
                <div class="prediction ground-truth">
                    <div class="model-name">Ground Truth</div>
                    {ground_truth}
                </div>
                """
            
            # Predizioni per ogni modello
            for model_name, results in all_results.items():
                if i >= len(results):
                    continue
                
                prediction = results[i].get('prediction', 'No prediction')
                
                # Determina classe CSS
                if model_name.upper() == 'BLIP2':
                    css_class = 'model-blip2'
                elif model_name.upper() == 'FLORENCE2':
                    css_class = 'model-florence2'
                elif model_name.upper() == 'IDEFICS3':
                    css_class = 'model-idefics3'
                else:
                    css_class = 'model-blip2'
                
                if prediction.startswith('[') and 'Error' in prediction:
                    css_class = 'model-error'
                
                html_content += f"""
                <div class="prediction {css_class}">
                    <div class="model-name">{model_name}</div>
                    {prediction}
                </div>
                """
            
            html_content += '</div></div></div>'
    
    html_content += '</div>'
    
    # Footer
    html_content += f"""
            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; text-align: center; color: #7f8c8d;">
                <p>📊 Generated by Baseline Evaluation CORRECT pipeline</p>
                <p>🎯 Task: SVG Captioning | 🤖 Models: {', '.join(all_results.keys())}</p>
                <p><em>This report shows qualitative examples with corrected model parameters</em></p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # Salva file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"✅ Report HTML salvato: {output_path}")
    return True

def main():
    parser = argparse.ArgumentParser(description="Crea report HTML qualitativo per baseline corretti")
    parser.add_argument("--results_dir",
                       default="evaluation_results/baseline_CORRECT_FINAL",
                       help="Directory con risultati baseline")
    parser.add_argument("--output_dir",
                       default="evaluation_results/baseline_CORRECT_FINAL",
                       help="Directory di output")
    parser.add_argument("--max_examples",
                       type=int,
                       default=50,
                       help="Numero massimo di esempi da mostrare")
    
    args = parser.parse_args()
    
    print("📄 CREAZIONE REPORT HTML BASELINE CORRETTO")
    print("=" * 50)
    print(f"📁 Directory risultati: {args.results_dir}")
    print(f"📈 Max esempi: {args.max_examples}")
    print("=" * 50)
    
    if not os.path.exists(args.results_dir):
        print(f"❌ Directory non trovata: {args.results_dir}")
        return
    
    # Trova file risultati
    results_files = {}
    for file in os.listdir(args.results_dir):
        if file.endswith('_results_CORRECT_.json') or '_results_CORRECT_' in file:
            # Estrai nome modello
            if 'BLIP2' in file:
                model_name = 'BLIP2'
            elif 'Florence2' in file:
                model_name = 'Florence2'
            elif 'Idefics3' in file:
                model_name = 'Idefics3'
            else:
                model_name = file.split('_')[0]
            
            results_files[model_name] = os.path.join(args.results_dir, file)
    
    if not results_files:
        print(f"❌ Nessun file risultati trovato in {args.results_dir}")
        return
    
    print(f"📄 File risultati trovati: {list(results_files.keys())}")
    
    # Carica tutti i risultati
    all_results = {}
    for model_name, file_path in results_files.items():
        results = load_results(file_path)
        if results:
            all_results[model_name] = results
            print(f"✅ {model_name}: {len(results)} esempi")
        else:
            print(f"⚠️ {model_name}: nessun risultato caricato")
    
    if not all_results:
        print("❌ Nessun risultato caricato")
        return
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Genera report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = os.path.join(args.output_dir, f"baseline_qualitative_report_CORRECT_{timestamp}.html")
    
    success = create_html_report(all_results, output_path, args.max_examples)
    
    if success:
        print("=" * 50)
        print("🎉 REPORT HTML CREATO!")
        print("=" * 50)
        print(f"📄 File: {os.path.basename(output_path)}")
        print(f"📁 Directory: {args.output_dir}")
        print(f"🌐 Apri con: firefox {output_path}")

if __name__ == "__main__":
    main()
