#!/usr/bin/env python3
"""
🎯 MASTER EVALUATION PIPELINE
Orchestrazione completa: Baseline → Comprehensive Metrics → Report Finale
"""

import os
import sys
import json
import subprocess
import time
import logging
import argparse
from datetime import datetime
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EvaluationPipeline:
    """Pipeline completa di valutazione"""
    
    def __init__(self, dataset_path, output_dir="evaluation_results"):
        self.dataset_path = dataset_path
        self.output_dir = output_dir
        self.timestamp = datetime.now().strftime("%Y%m%d")
        
        # File risultati attesi
        self.expected_results = {
            'florence2': f"{output_dir}/florence2_results_{self.timestamp}.json",
            'idefics3': f"{output_dir}/idefics3_results_{self.timestamp}.json",
            'blip2': f"{output_dir}/blip2_results_{self.timestamp}.json",
            'gemma_t9': f"{output_dir}/gemma_t9_results_{self.timestamp}.json"
        }
        
        # File comprehensive metrics
        self.comprehensive_dir = f"{output_dir}/comprehensive_metrics"
        self.comprehensive_results = {
            'florence2': f"{self.comprehensive_dir}/florence2_comprehensive_metrics.json",
            'idefics3': f"{self.comprehensive_dir}/idefics3_comprehensive_metrics.json", 
            'blip2': f"{self.comprehensive_dir}/blip2_comprehensive_metrics.json",
            'gemma_t9': f"{self.comprehensive_dir}/gemma_t9_comprehensive_metrics.json"
        }
        
        # Report finale
        self.final_report = f"{output_dir}/reports/FINAL_COMPREHENSIVE_REPORT_{self.timestamp}.html"
        
        # Crea directory
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(self.comprehensive_dir, exist_ok=True)
        os.makedirs(f"{output_dir}/reports", exist_ok=True)
    
    def check_slurm_job_status(self, job_id):
        """Controlla status job SLURM"""
        try:
            result = subprocess.run(['squeue', '-j', str(job_id)], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0 and len(result.stdout.strip().split('\n')) > 1:
                # Job esiste ancora
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:
                    job_line = lines[1]  # Prima riga dopo header
                    status = job_line.split()[4]  # Colonna ST
                    return status
            
            # Job non trovato = completato
            return "COMPLETED"
            
        except Exception as e:
            logger.error(f"Errore controllo job {job_id}: {e}")
            return "UNKNOWN"
    
    def wait_for_slurm_jobs(self, job_ids, max_wait_hours=4):
        """Aspetta completamento job SLURM"""
        logger.info(f"⏳ Aspettando completamento job: {job_ids}")
        
        start_time = time.time()
        max_wait_seconds = max_wait_hours * 3600
        
        while time.time() - start_time < max_wait_seconds:
            all_completed = True
            
            for job_id in job_ids:
                status = self.check_slurm_job_status(job_id)
                logger.info(f"   Job {job_id}: {status}")
                
                if status in ["PD", "R", "CG"]:  # Pending, Running, Completing
                    all_completed = False
            
            if all_completed:
                logger.info("✅ Tutti i job completati!")
                return True
            
            logger.info("   Aspetto 60 secondi...")
            time.sleep(60)
        
        logger.warning(f"⚠️ Timeout dopo {max_wait_hours} ore")
        return False
    
    def check_baseline_results(self):
        """Controlla se i risultati baseline esistono"""
        missing = []
        existing = []
        
        for model, file_path in self.expected_results.items():
            if os.path.exists(file_path):
                existing.append(model)
                logger.info(f"✅ {model}: {file_path}")
            else:
                missing.append(model)
                logger.warning(f"❌ {model}: {file_path} MANCANTE")
        
        return existing, missing
    
    def submit_baseline_evaluation(self, models):
        """Sottometti job per valutazione baseline"""
        logger.info(f"🚀 Sottomissione valutazione baseline: {models}")
        
        job_ids = []
        
        for model in models:
            if model in ['florence2', 'idefics3']:
                # Usa script esistente
                cmd = [
                    'sbatch',
                    '--job-name=f'EVAL_{model.upper()}',
                    'scripts/slurm/evaluate_missing_baselines.slurm'
                ]
                
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, cwd='/work/tesi_ediluzio')
                    
                    if result.returncode == 0:
                        # Estrai job ID
                        output = result.stdout.strip()
                        if "Submitted batch job" in output:
                            job_id = output.split()[-1]
                            job_ids.append(job_id)
                            logger.info(f"   ✅ {model}: Job {job_id} sottomesso")
                    else:
                        logger.error(f"   ❌ Errore sottomissione {model}: {result.stderr}")
                        
                except Exception as e:
                    logger.error(f"   ❌ Errore {model}: {e}")
        
        return job_ids
    
    def calculate_comprehensive_metrics(self, models):
        """Calcola comprehensive metrics per tutti i modelli"""
        logger.info("📊 Calcolo comprehensive metrics...")
        
        for model in models:
            if model not in self.expected_results:
                continue
                
            results_file = self.expected_results[model]
            output_file = self.comprehensive_results[model]
            
            if not os.path.exists(results_file):
                logger.warning(f"⚠️ {model}: File risultati non trovato: {results_file}")
                continue
            
            logger.info(f"   🔄 Processando {model}...")
            
            cmd = [
                'python', 'scripts/evaluation/COMPREHENSIVE_metrics_REAL_CLIP.py',
                '--results_file', results_file,
                '--model_name', model,
                '--output_dir', self.comprehensive_dir
            ]
            
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, cwd='/work/tesi_ediluzio')
                
                if result.returncode == 0:
                    logger.info(f"   ✅ {model}: Comprehensive metrics completate")
                else:
                    logger.error(f"   ❌ {model}: Errore comprehensive metrics")
                    logger.error(f"      STDOUT: {result.stdout}")
                    logger.error(f"      STDERR: {result.stderr}")
                    
            except Exception as e:
                logger.error(f"   ❌ {model}: Errore: {e}")
    
    def create_final_report(self):
        """Crea report finale"""
        logger.info("📄 Creazione report finale...")
        
        cmd = [
            'python', 'scripts/evaluation/create_FINAL_COMPREHENSIVE_REPORT.py',
            '--results_dir', self.comprehensive_dir,
            '--output_file', self.final_report
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, cwd='/work/tesi_ediluzio')
            
            if result.returncode == 0:
                logger.info(f"✅ Report finale creato: {self.final_report}")
                return True
            else:
                logger.error(f"❌ Errore creazione report finale")
                logger.error(f"   STDOUT: {result.stdout}")
                logger.error(f"   STDERR: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Errore report finale: {e}")
            return False
    
    def run_full_pipeline(self, skip_baseline=False, skip_comprehensive=False):
        """Esegui pipeline completa"""
        logger.info("🎯 AVVIO PIPELINE COMPLETA")
        logger.info("=" * 60)
        logger.info(f"Dataset: {self.dataset_path}")
        logger.info(f"Output: {self.output_dir}")
        logger.info(f"Timestamp: {self.timestamp}")
        logger.info("=" * 60)
        
        # FASE 1: Controlla risultati baseline esistenti
        existing_models, missing_models = self.check_baseline_results()
        
        if missing_models and not skip_baseline:
            logger.info(f"\n🚀 FASE 1: Valutazione Baseline Mancanti")
            logger.info(f"Modelli mancanti: {missing_models}")
            
            # Sottometti job per modelli mancanti
            job_ids = self.submit_baseline_evaluation(missing_models)
            
            if job_ids:
                # Aspetta completamento
                success = self.wait_for_slurm_jobs(job_ids, max_wait_hours=3)
                if not success:
                    logger.error("❌ Timeout job baseline - continuando con modelli esistenti")
            
            # Ricontrolla risultati
            existing_models, missing_models = self.check_baseline_results()
        
        # FASE 2: Comprehensive Metrics
        if existing_models and not skip_comprehensive:
            logger.info(f"\n📊 FASE 2: Comprehensive Metrics")
            logger.info(f"Modelli da processare: {existing_models}")
            
            self.calculate_comprehensive_metrics(existing_models)
        
        # FASE 3: Report Finale
        logger.info(f"\n📄 FASE 3: Report Finale")
        success = self.create_final_report()
        
        # SUMMARY
        logger.info("\n" + "=" * 60)
        logger.info("🏆 PIPELINE COMPLETATA!")
        logger.info("=" * 60)
        logger.info(f"Modelli valutati: {len(existing_models)}")
        logger.info(f"Report finale: {self.final_report if success else 'ERRORE'}")
        logger.info("=" * 60)
        
        return success

def main():
    parser = argparse.ArgumentParser(description="Pipeline completa valutazione")
    parser.add_argument("--dataset_path", 
                       default="data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json",
                       help="Path dataset")
    parser.add_argument("--output_dir", default="evaluation_results", help="Directory output")
    parser.add_argument("--skip_baseline", action="store_true", help="Salta valutazione baseline")
    parser.add_argument("--skip_comprehensive", action="store_true", help="Salta comprehensive metrics")
    
    args = parser.parse_args()
    
    # Inizializza pipeline
    pipeline = EvaluationPipeline(args.dataset_path, args.output_dir)
    
    # Esegui pipeline
    success = pipeline.run_full_pipeline(
        skip_baseline=args.skip_baseline,
        skip_comprehensive=args.skip_comprehensive
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
