#!/usr/bin/env python3
"""
🚀 QUANTIZZAZIONE SOLO CHECKPOINT
Quantizza direttamente i checkpoint LoRA senza base model
"""

import os
import sys
import json
import torch
import argparse
import logging
from datetime import datetime
from pathlib import Path
import gc
import psutil
from transformers import (
    AutoModelForCausalLM, 
    AutoTokenizer,
    BitsAndBytesConfig
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_memory_usage():
    """Ottieni uso memoria corrente"""
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    return {
        'rss_gb': memory_info.rss / (1024**3),
        'gpu_allocated_gb': torch.cuda.memory_allocated() / (1024**3) if torch.cuda.is_available() else 0,
        'gpu_reserved_gb': torch.cuda.memory_reserved() / (1024**3) if torch.cuda.is_available() else 0
    }

def create_quantization_configs():
    """Crea diverse configurazioni di quantizzazione"""
    configs = {
        '4bit_nf4': BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="nf4",
            bnb_4bit_use_double_quant=True,
        ),
        '4bit_fp4': BitsAndBytesConfig(
            load_in_4bit=True,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_quant_type="fp4",
            bnb_4bit_use_double_quant=True,
        ),
        '8bit': BitsAndBytesConfig(
            load_in_8bit=True,
            llm_int8_threshold=6.0,
            llm_int8_has_fp16_weight=False,
        )
    }
    return configs

def quantize_checkpoint_direct(checkpoint_path, output_dir, quantization_types):
    """Quantizza checkpoint direttamente (se possibile)"""
    
    results = {}
    
    # Verifica che il checkpoint esista
    if not os.path.exists(checkpoint_path):
        logger.error(f"❌ Checkpoint non trovato: {checkpoint_path}")
        return results
    
    logger.info(f"🔧 Analisi checkpoint: {checkpoint_path}")
    
    # Controlla se il checkpoint contiene un modello completo
    try:
        # Prova a caricare come modello completo
        logger.info("🔧 Tentativo caricamento come modello completo...")
        tokenizer = AutoTokenizer.from_pretrained(checkpoint_path)
        
        # Test caricamento modello
        test_model = AutoModelForCausalLM.from_pretrained(
            checkpoint_path,
            torch_dtype=torch.float16,
            device_map="cpu",  # Prima su CPU per test
            trust_remote_code=True,
            local_files_only=True
        )
        
        logger.info("✅ Checkpoint contiene modello completo!")
        
        # Libera memoria test
        del test_model
        torch.cuda.empty_cache()
        gc.collect()
        
        # Ora quantizza
        quantization_configs = create_quantization_configs()
        
        for quant_type in quantization_types:
            if quant_type not in quantization_configs:
                logger.warning(f"⚠️ Tipo quantizzazione non supportato: {quant_type}")
                continue
                
            logger.info(f"🚀 Quantizzazione {quant_type}...")
            
            try:
                # Carica modello con quantizzazione
                quantized_model = AutoModelForCausalLM.from_pretrained(
                    checkpoint_path,
                    quantization_config=quantization_configs[quant_type],
                    torch_dtype=torch.float16,
                    device_map="auto",
                    trust_remote_code=True,
                    local_files_only=True
                )
                
                # Salva modello quantizzato
                quant_output_dir = os.path.join(output_dir, f"quantized_{quant_type}")
                os.makedirs(quant_output_dir, exist_ok=True)
                
                quantized_model.save_pretrained(quant_output_dir)
                tokenizer.save_pretrained(quant_output_dir)
                
                # Memoria dopo quantizzazione
                memory_after = get_memory_usage()
                
                results[quant_type] = {
                    'status': 'success',
                    'output_path': quant_output_dir,
                    'memory_usage_gb': memory_after['gpu_allocated_gb'],
                    'timestamp': datetime.now().isoformat()
                }
                
                logger.info(f"✅ {quant_type} completato: {quant_output_dir}")
                logger.info(f"📊 Memoria GPU: {memory_after['gpu_allocated_gb']:.2f}GB")
                
                # Cleanup
                del quantized_model
                torch.cuda.empty_cache()
                gc.collect()
                
            except Exception as e:
                logger.error(f"❌ Errore quantizzazione {quant_type}: {e}")
                results[quant_type] = {
                    'status': 'error',
                    'error': str(e),
                    'timestamp': datetime.now().isoformat()
                }
        
    except Exception as e:
        logger.error(f"❌ Checkpoint non è un modello completo: {e}")
        logger.info("💡 Questo è probabilmente un adapter LoRA, serve il modello base")
        
        results['error'] = {
            'status': 'error',
            'error': f'Checkpoint è un adapter LoRA, non un modello completo: {str(e)}',
            'timestamp': datetime.now().isoformat()
        }
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Quantizza checkpoint direttamente")
    parser.add_argument("--checkpoint_path", required=True, help="Path al checkpoint")
    parser.add_argument("--output_dir", required=True, help="Directory output")
    parser.add_argument("--quantization_types", nargs="+", default=["4bit_nf4"], 
                       choices=["4bit_nf4", "4bit_fp4", "8bit"], help="Tipi quantizzazione")
    parser.add_argument("--model_name", required=True, help="Nome modello per output")
    
    args = parser.parse_args()
    
    # Crea directory output
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_output_dir = os.path.join(args.output_dir, f"{args.model_name}_{timestamp}")
    os.makedirs(model_output_dir, exist_ok=True)
    
    logger.info("🚀 QUANTIZZAZIONE CHECKPOINT DIRETTO")
    logger.info("=" * 50)
    logger.info(f"📁 Checkpoint: {args.checkpoint_path}")
    logger.info(f"📊 Quantizzazioni: {args.quantization_types}")
    logger.info(f"💾 Output: {model_output_dir}")
    logger.info("=" * 50)
    
    # Memoria iniziale
    memory_start = get_memory_usage()
    logger.info(f"📊 Memoria iniziale: RAM={memory_start['rss_gb']:.2f}GB, GPU={memory_start['gpu_allocated_gb']:.2f}GB")
    
    # Quantizza
    results = quantize_checkpoint_direct(
        args.checkpoint_path,
        model_output_dir,
        args.quantization_types
    )
    
    # Salva risultati
    results_file = os.path.join(model_output_dir, f"quantization_results_{timestamp}.json")
    with open(results_file, 'w') as f:
        json.dump({
            'model_name': args.model_name,
            'checkpoint_path': args.checkpoint_path,
            'quantization_types': args.quantization_types,
            'results': results,
            'timestamp': timestamp
        }, f, indent=2)
    
    logger.info("=" * 50)
    logger.info("✅ QUANTIZZAZIONE COMPLETATA!")
    logger.info(f"📁 Risultati: {results_file}")
    
    # Riepilogo
    successful = sum(1 for r in results.values() if r.get('status') == 'success')
    total = len([r for r in results.values() if 'status' in r])
    logger.info(f"📊 Successi: {successful}/{total}")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
