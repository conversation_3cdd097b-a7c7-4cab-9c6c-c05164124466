#!/usr/bin/env python3
"""
🚀 INFERENCE PRODUZIONE CON MODELLI QUANTIZZATI
Sistema ottimizzato per deployment su singola GPU
"""

import os
import sys
import json
import torch
import argparse
import logging
import time
from datetime import datetime
from pathlib import Path
import gc
import psutil
from transformers import (
    AutoModelForCausalLM, 
    AutoTokenizer,
    TextStreamer
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionInference:
    """Classe per inference ottimizzata in produzione"""
    
    def __init__(self, model_path, device="auto", max_memory_gb=None):
        self.model_path = model_path
        self.device = device
        self.max_memory_gb = max_memory_gb
        self.model = None
        self.tokenizer = None
        self.model_info = {}
        
        self._load_model()
    
    def _get_memory_usage(self):
        """Ottieni uso memoria corrente"""
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return {
            'rss_gb': memory_info.rss / (1024**3),
            'gpu_allocated_gb': torch.cuda.memory_allocated() / (1024**3) if torch.cuda.is_available() else 0,
            'gpu_reserved_gb': torch.cuda.memory_reserved() / (1024**3) if torch.cuda.is_available() else 0
        }
    
    def _load_model(self):
        """Carica modello e tokenizer"""
        logger.info(f"🤖 Caricamento modello da: {self.model_path}")
        
        # Memoria prima del caricamento
        mem_before = self._get_memory_usage()
        start_time = time.time()
        
        try:
            # Carica tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Configurazione device map per memoria limitata
            device_map = "auto"
            if self.max_memory_gb:
                max_memory = {0: f"{self.max_memory_gb}GB"}
                device_map = "auto"
            
            # Carica modello
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                device_map=device_map,
                torch_dtype=torch.float16,
                trust_remote_code=True,
                low_cpu_mem_usage=True
            )
            
            # Memoria dopo caricamento
            mem_after = self._get_memory_usage()
            load_time = time.time() - start_time
            
            # Carica info quantizzazione se disponibile
            quant_info_path = os.path.join(self.model_path, 'quantization_info.json')
            if os.path.exists(quant_info_path):
                with open(quant_info_path, 'r') as f:
                    self.model_info = json.load(f)
            
            logger.info(f"✅ Modello caricato in {load_time:.2f}s")
            logger.info(f"📊 Memoria: {mem_before['gpu_allocated_gb']:.2f}GB → {mem_after['gpu_allocated_gb']:.2f}GB")
            
            self.model_info.update({
                'load_time_seconds': load_time,
                'memory_usage': {
                    'before_load': mem_before,
                    'after_load': mem_after
                }
            })
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento modello: {e}")
            raise
    
    def generate(self, prompt, max_new_tokens=512, temperature=0.7, do_sample=True, 
                 top_p=0.9, repetition_penalty=1.1, stream=False):
        """Genera risposta dal prompt"""
        if not self.model or not self.tokenizer:
            raise RuntimeError("Modello non caricato")
        
        start_time = time.time()
        
        # Tokenizza input
        inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=2048)
        inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
        
        # Configurazione generazione
        generation_config = {
            'max_new_tokens': max_new_tokens,
            'temperature': temperature,
            'do_sample': do_sample,
            'top_p': top_p,
            'repetition_penalty': repetition_penalty,
            'pad_token_id': self.tokenizer.eos_token_id,
            'eos_token_id': self.tokenizer.eos_token_id,
        }
        
        # Streaming se richiesto
        if stream:
            streamer = TextStreamer(self.tokenizer, skip_prompt=True, skip_special_tokens=True)
            generation_config['streamer'] = streamer
        
        # Genera
        with torch.no_grad():
            outputs = self.model.generate(**inputs, **generation_config)
        
        # Decodifica
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        response = generated_text[len(prompt):].strip()
        
        generation_time = time.time() - start_time
        tokens_generated = len(outputs[0]) - len(inputs['input_ids'][0])
        tokens_per_second = tokens_generated / generation_time if generation_time > 0 else 0
        
        return {
            'prompt': prompt,
            'response': response,
            'generation_time_seconds': generation_time,
            'tokens_generated': tokens_generated,
            'tokens_per_second': tokens_per_second,
            'memory_usage': self._get_memory_usage()
        }
    
    def batch_generate(self, prompts, **generation_kwargs):
        """Genera risposte per batch di prompt"""
        results = []
        total_time = 0
        
        logger.info(f"🔄 Generazione batch di {len(prompts)} prompt")
        
        for i, prompt in enumerate(prompts):
            logger.info(f"  Prompt {i+1}/{len(prompts)}")
            result = self.generate(prompt, **generation_kwargs)
            results.append(result)
            total_time += result['generation_time_seconds']
            
            # Cleanup periodico
            if (i + 1) % 5 == 0:
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
        
        return {
            'results': results,
            'total_time_seconds': total_time,
            'average_time_seconds': total_time / len(prompts),
            'total_prompts': len(prompts)
        }
    
    def get_model_info(self):
        """Ottieni informazioni sul modello"""
        info = {
            'model_path': self.model_path,
            'device': str(self.model.device) if self.model else None,
            'memory_usage': self._get_memory_usage(),
            'quantization_info': self.model_info
        }
        return info
    
    def cleanup(self):
        """Pulisci memoria"""
        if self.model:
            del self.model
        if self.tokenizer:
            del self.tokenizer
        
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        logger.info("🧹 Memoria pulita")

def interactive_mode(inference_engine):
    """Modalità interattiva"""
    logger.info("🎮 Modalità interattiva attivata (digita 'quit' per uscire)")
    
    while True:
        try:
            prompt = input("\n💬 Prompt: ").strip()
            
            if prompt.lower() in ['quit', 'exit', 'q']:
                break
            
            if not prompt:
                continue
            
            print("🤖 Generazione in corso...")
            result = inference_engine.generate(prompt, stream=True)
            print(f"\n⏱️ Tempo: {result['generation_time_seconds']:.2f}s")
            print(f"🚀 Velocità: {result['tokens_per_second']:.1f} token/s")
            
        except KeyboardInterrupt:
            print("\n👋 Uscita...")
            break
        except Exception as e:
            print(f"❌ Errore: {e}")

def main():
    parser = argparse.ArgumentParser(description="Inference produzione con modelli quantizzati")
    parser.add_argument("--model_path", required=True, help="Path al modello quantizzato")
    parser.add_argument("--prompts_file", help="File JSON con prompt di test")
    parser.add_argument("--interactive", action="store_true", help="Modalità interattiva")
    parser.add_argument("--max_new_tokens", type=int, default=512, help="Max token da generare")
    parser.add_argument("--temperature", type=float, default=0.7, help="Temperatura generazione")
    parser.add_argument("--max_memory_gb", type=int, help="Limite memoria GPU in GB")
    parser.add_argument("--output_file", help="File output per risultati")
    parser.add_argument("--stream", action="store_true", help="Output streaming")
    
    args = parser.parse_args()
    
    # Inizializza engine
    logger.info(f"🚀 Inizializzazione inference engine")
    inference_engine = ProductionInference(
        model_path=args.model_path,
        max_memory_gb=args.max_memory_gb
    )
    
    try:
        # Modalità interattiva
        if args.interactive:
            interactive_mode(inference_engine)
            return
        
        # Carica prompt da file
        if args.prompts_file:
            with open(args.prompts_file, 'r') as f:
                prompts = json.load(f)
            
            logger.info(f"📝 Caricati {len(prompts)} prompt da {args.prompts_file}")
            
            # Genera risposte
            results = inference_engine.batch_generate(
                prompts,
                max_new_tokens=args.max_new_tokens,
                temperature=args.temperature,
                stream=args.stream
            )
            
            # Aggiungi info modello
            results['model_info'] = inference_engine.get_model_info()
            results['timestamp'] = datetime.now().isoformat()
            
            # Salva risultati
            if args.output_file:
                with open(args.output_file, 'w') as f:
                    json.dump(results, f, indent=2)
                logger.info(f"💾 Risultati salvati: {args.output_file}")
            
            # Stampa summary
            logger.info(f"\n{'='*50}")
            logger.info(f"📊 SUMMARY INFERENCE")
            logger.info(f"{'='*50}")
            logger.info(f"Prompt processati: {results['total_prompts']}")
            logger.info(f"Tempo totale: {results['total_time_seconds']:.2f}s")
            logger.info(f"Tempo medio: {results['average_time_seconds']:.2f}s")
            avg_tokens_per_sec = sum(r['tokens_per_second'] for r in results['results']) / len(results['results'])
            logger.info(f"Velocità media: {avg_tokens_per_sec:.1f} token/s")
            logger.info(f"{'='*50}")
        
        else:
            logger.warning("⚠️ Nessun prompt specificato. Usa --prompts_file o --interactive")
    
    finally:
        # Cleanup
        inference_engine.cleanup()

if __name__ == "__main__":
    main()
