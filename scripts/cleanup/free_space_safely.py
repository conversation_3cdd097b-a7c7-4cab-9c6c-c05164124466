#!/usr/bin/env python3
"""
🧹 SCRIPT PULIZIA SPAZIO SICURA
Libera spazio mantenendo i file essenziali per i training in corso
"""

import os
import shutil
import json
import logging
from datetime import datetime
import subprocess

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_directory_size(path):
    """Calcola dimensione directory in GB"""
    try:
        result = subprocess.run(['du', '-sb', path], capture_output=True, text=True)
        if result.returncode == 0:
            size_bytes = int(result.stdout.split()[0])
            return size_bytes / (1024**3)  # Convert to GB
    except:
        pass
    return 0

def safe_remove_directory(path, description=""):
    """Rimuove directory in modo sicuro"""
    if os.path.exists(path):
        size_gb = get_directory_size(path)
        try:
            shutil.rmtree(path)
            logger.info(f"✅ Rimosso {description}: {path} ({size_gb:.1f}GB)")
            return size_gb
        except Exception as e:
            logger.error(f"❌ Errore rimozione {path}: {e}")
            return 0
    return 0

def safe_remove_files(pattern_path, description=""):
    """Rimuove file con pattern"""
    try:
        import glob
        files = glob.glob(pattern_path)
        total_size = 0
        for file in files:
            if os.path.exists(file):
                size = os.path.getsize(file) / (1024**3)
                os.remove(file)
                total_size += size
        if total_size > 0:
            logger.info(f"✅ Rimossi {description}: {len(files)} file ({total_size:.1f}GB)")
        return total_size
    except Exception as e:
        logger.error(f"❌ Errore rimozione {pattern_path}: {e}")
        return 0

def cleanup_work_directory():
    """Pulizia directory /work/tesi_ediluzio"""
    logger.info("🧹 PULIZIA /work/tesi_ediluzio")
    logger.info("=" * 50)
    
    total_freed = 0
    
    # 1. Rimuovi chunks temporanei (se non in uso)
    chunks_size = safe_remove_directory("chunks", "chunks temporanei")
    total_freed += chunks_size
    
    # 2. Rimuovi vecchi log SLURM (mantieni solo ultimi 10 giorni)
    logger.info("🗑️ Pulizia log vecchi...")
    old_logs = []
    if os.path.exists("logs"):
        for file in os.listdir("logs"):
            if file.endswith(('.out', '.err')):
                filepath = os.path.join("logs", file)
                # Mantieni solo log recenti (ultimi 7 giorni)
                if os.path.getmtime(filepath) < (datetime.now().timestamp() - 7*24*3600):
                    old_logs.append(filepath)
    
    for log_file in old_logs:
        try:
            size = os.path.getsize(log_file) / (1024**3)
            os.remove(log_file)
            total_freed += size
        except:
            pass
    
    if old_logs:
        logger.info(f"✅ Rimossi {len(old_logs)} log vecchi")
    
    # 3. Pulizia evaluation_results vecchi (mantieni solo essenziali)
    cleanup_dirs = [
        "evaluation_results/baseline_NEW_CORRECTED",
        "evaluation_results/baseline_images_FIXED", 
        "evaluation_results/blip2_FIXED_XML_400",
        "evaluation_results/blip2_OFFICIAL_400",
        "evaluation_results/blip2_WORKING_400",
        "evaluation_results/gemma_DEBUG",
        "evaluation_results/idefics3_WORKING_400",
        "evaluation_results/merged_results",
        "evaluation_results/radar_charts_NEW_BASELINE",
        "evaluation_results/reports"
    ]
    
    for dir_path in cleanup_dirs:
        total_freed += safe_remove_directory(dir_path, f"evaluation results vecchi")
    
    # 4. Pulizia wandb run vecchi (mantieni solo ultimi 5)
    if os.path.exists("wandb"):
        wandb_runs = []
        for item in os.listdir("wandb"):
            if item.startswith("run-"):
                wandb_runs.append(item)
        
        # Ordina per data e mantieni solo gli ultimi 5
        wandb_runs.sort()
        old_runs = wandb_runs[:-5] if len(wandb_runs) > 5 else []
        
        for run in old_runs:
            total_freed += safe_remove_directory(f"wandb/{run}", "wandb run vecchio")
    
    # 5. Pulizia file temporanei
    temp_files = [
        "test_quantization_local_*.json",
        "*.tmp",
        "*.cache"
    ]
    
    for pattern in temp_files:
        total_freed += safe_remove_files(pattern, "file temporanei")
    
    logger.info("=" * 50)
    logger.info(f"🎯 TOTALE LIBERATO: {total_freed:.1f}GB")
    return total_freed

def cleanup_homes_directory():
    """Pulizia directory /homes/ediluzio"""
    logger.info("🧹 PULIZIA /homes/ediluzio")
    logger.info("=" * 50)
    
    homes_path = "/homes/ediluzio"
    if not os.path.exists(homes_path):
        logger.warning("⚠️ Directory /homes/ediluzio non accessibile")
        return 0
    
    total_freed = 0
    
    # Pulizia cache conda
    try:
        result = subprocess.run(['conda', 'clean', '--all', '-y'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ Cache conda pulita")
    except:
        pass
    
    # Pulizia cache pip
    try:
        result = subprocess.run(['pip', 'cache', 'purge'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            logger.info("✅ Cache pip pulita")
    except:
        pass
    
    # Pulizia .cache directory
    cache_dirs = [
        f"{homes_path}/.cache/pip",
        f"{homes_path}/.cache/huggingface",
        f"{homes_path}/.cache/torch",
        f"{homes_path}/.cache/matplotlib"
    ]
    
    for cache_dir in cache_dirs:
        total_freed += safe_remove_directory(cache_dir, "cache directory")
    
    logger.info("=" * 50)
    logger.info(f"🎯 TOTALE LIBERATO: {total_freed:.1f}GB")
    return total_freed

def check_active_jobs():
    """Controlla job attivi per evitare di rimuovere file in uso"""
    try:
        result = subprocess.run(['squeue', '-u', 'ediluzio'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            active_jobs = []
            for line in lines:
                if line.strip():
                    parts = line.split()
                    if len(parts) >= 3:
                        job_id = parts[0]
                        job_name = parts[2]
                        status = parts[4]
                        active_jobs.append((job_id, job_name, status))
            return active_jobs
    except:
        pass
    return []

def main():
    logger.info("🧹 PULIZIA SPAZIO SICURA")
    logger.info("=" * 60)
    
    # Controlla job attivi
    active_jobs = check_active_jobs()
    if active_jobs:
        logger.info("🔄 JOB ATTIVI RILEVATI:")
        for job_id, job_name, status in active_jobs:
            logger.info(f"   {job_id}: {job_name} ({status})")
        logger.info("⚠️ Pulizia conservativa per non interferire con training")
    else:
        logger.info("✅ Nessun job attivo rilevato")
    
    logger.info("=" * 60)
    
    # Spazio iniziale
    try:
        result = subprocess.run(['df', '-h', '/work/tesi_ediluzio'], 
                              capture_output=True, text=True)
        logger.info("📊 SPAZIO INIZIALE:")
        logger.info(result.stdout)
    except:
        pass
    
    # Pulizia work directory
    work_freed = cleanup_work_directory()
    
    # Pulizia homes directory
    homes_freed = cleanup_homes_directory()
    
    # Spazio finale
    try:
        result = subprocess.run(['df', '-h', '/work/tesi_ediluzio'], 
                              capture_output=True, text=True)
        logger.info("📊 SPAZIO FINALE:")
        logger.info(result.stdout)
    except:
        pass
    
    # Summary
    total_freed = work_freed + homes_freed
    logger.info("=" * 60)
    logger.info("🎉 PULIZIA COMPLETATA!")
    logger.info("=" * 60)
    logger.info(f"🗑️ Work directory: {work_freed:.1f}GB liberati")
    logger.info(f"🏠 Homes directory: {homes_freed:.1f}GB liberati")
    logger.info(f"🎯 TOTALE: {total_freed:.1f}GB liberati")
    logger.info("=" * 60)
    
    return 0

if __name__ == "__main__":
    exit(main())
