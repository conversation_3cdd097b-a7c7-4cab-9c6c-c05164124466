#!/usr/bin/env python3
"""
📊 CONTROLLO DIMENSIONI DIRECTORY
Analizza lo spazio occupato da ogni directory
"""

import os
import subprocess
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_directory_size(path):
    """Calcola dimensione directory in GB"""
    try:
        if not os.path.exists(path):
            return 0
        result = subprocess.run(['du', '-sb', path], capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            size_bytes = int(result.stdout.split()[0])
            return size_bytes / (1024**3)  # Convert to GB
    except Exception as e:
        logger.warning(f"Errore calcolo {path}: {e}")
    return 0

def analyze_directory_sizes():
    """Analizza dimensioni delle directory principali"""
    
    directories_to_check = [
        # Directory principali
        ("chunks", "Chunks temporanei"),
        ("data", "Dataset"),
        ("experiments", "Esperimenti e checkpoint"),
        ("evaluation_results", "Risultati evaluation"),
        ("logs", "Log SLURM"),
        ("wandb", "WandB runs"),
        ("models", "Modelli"),
        
        # Subdirectory evaluation_results
        ("evaluation_results/baseline_COMPLETE_400", "Baseline completi"),
        ("evaluation_results/baseline_COMPLETE_400_CPU", "Baseline CPU"),
        ("evaluation_results/baseline_NEW_CORRECTED", "Baseline corretti (vecchi)"),
        ("evaluation_results/baseline_images_FIXED", "Immagini baseline (vecchie)"),
        ("evaluation_results/blip2_FINAL_400", "BLIP-2 finale"),
        ("evaluation_results/blip2_FIXED_XML_400", "BLIP-2 XML (vecchio)"),
        ("evaluation_results/blip2_OFFICIAL_400", "BLIP-2 ufficiale (vecchio)"),
        ("evaluation_results/blip2_WORKING_400", "BLIP-2 working (vecchio)"),
        ("evaluation_results/comprehensive_metrics", "Metriche comprehensive"),
        ("evaluation_results/gemma_COMPLETE_400_CPU", "Gemma CPU"),
        ("evaluation_results/gemma_DEBUG", "Gemma debug (vecchio)"),
        ("evaluation_results/gemma_GPU_BOOST_400", "Gemma GPU boost"),
        ("evaluation_results/idefics3_WORKING_400", "Idefics3 (vecchio)"),
        ("evaluation_results/radar_charts_PERFECT", "Grafici radar attuali"),
        ("evaluation_results/radar_charts_NEW_BASELINE", "Grafici radar vecchi"),
        ("evaluation_results/trained_models", "Modelli trained"),
        ("evaluation_results/reports", "Report (vecchi)"),
        ("evaluation_results/merged_results", "Risultati merged (vecchi)"),
        
        # Subdirectory experiments
        ("experiments/xml_direct_input/outputs", "Output training"),
        ("experiments/baseline_results", "Risultati baseline"),
        
        # Subdirectory wandb (ultimi 10 run)
        ("wandb/run-20250728_094159-t6f5565j", "WandB run attivo Gemma"),
        ("wandb/run-20250728_094210-p8bhzl2p", "WandB run attivo"),
        ("wandb/run-20250728_095304-nb6zmd4v", "WandB run attivo Gemma"),
        ("wandb/run-20250728_095318-puqde9s6", "WandB run attivo Llama"),
        ("wandb/run-20250727_200309-xskg8o2l", "WandB run recente"),
        ("wandb/run-20250727_200357-rxqqemn6", "WandB run recente"),
        ("wandb/run-20250727_204840-3moh973e", "WandB run recente"),
        ("wandb/run-20250722_152654-wzol1qdk", "WandB run vecchio"),
        ("wandb/run-20250721_195416-uorucqqv", "WandB run vecchio"),
        ("wandb/run-20250720_110719-qlnw9ue0", "WandB run molto vecchio"),
    ]
    
    logger.info("📊 ANALISI DIMENSIONI DIRECTORY")
    logger.info("=" * 80)
    
    results = []
    
    for path, description in directories_to_check:
        size_gb = get_directory_size(path)
        results.append((path, description, size_gb))
        
        if size_gb > 0:
            status = "🔴" if size_gb > 5 else "🟡" if size_gb > 1 else "🟢"
            logger.info(f"{status} {size_gb:6.2f}GB - {description}")
            logger.info(f"     📁 {path}")
        else:
            logger.info(f"⚪ {'':6} - {description} (non esistente)")
            logger.info(f"     📁 {path}")
        logger.info("")
    
    # Ordina per dimensione
    results.sort(key=lambda x: x[2], reverse=True)
    
    logger.info("=" * 80)
    logger.info("🎯 TOP 10 DIRECTORY PIÙ GRANDI:")
    logger.info("=" * 80)
    
    for i, (path, description, size_gb) in enumerate(results[:10]):
        if size_gb > 0:
            logger.info(f"{i+1:2d}. {size_gb:6.2f}GB - {description}")
            logger.info(f"     📁 {path}")
            logger.info("")
    
    # Calcola totale
    total_size = sum(size for _, _, size in results if size > 0)
    logger.info("=" * 80)
    logger.info(f"📊 TOTALE ANALIZZATO: {total_size:.2f}GB")
    logger.info("=" * 80)
    
    # Identifica candidati per pulizia
    logger.info("🗑️ CANDIDATI PER PULIZIA (SICURI):")
    logger.info("=" * 80)
    
    cleanup_candidates = [
        ("evaluation_results/baseline_NEW_CORRECTED", "Baseline corretti (vecchi)"),
        ("evaluation_results/baseline_images_FIXED", "Immagini baseline (vecchie)"),
        ("evaluation_results/blip2_FIXED_XML_400", "BLIP-2 XML (vecchio)"),
        ("evaluation_results/blip2_OFFICIAL_400", "BLIP-2 ufficiale (vecchio)"),
        ("evaluation_results/blip2_WORKING_400", "BLIP-2 working (vecchio)"),
        ("evaluation_results/gemma_DEBUG", "Gemma debug (vecchio)"),
        ("evaluation_results/idefics3_WORKING_400", "Idefics3 (vecchio)"),
        ("evaluation_results/radar_charts_NEW_BASELINE", "Grafici radar vecchi"),
        ("evaluation_results/reports", "Report (vecchi)"),
        ("evaluation_results/merged_results", "Risultati merged (vecchi)"),
        ("wandb/run-20250720_110719-qlnw9ue0", "WandB run molto vecchio"),
        ("wandb/run-20250720_111343-0ztznw7i", "WandB run molto vecchio"),
        ("wandb/run-20250720_162259-kmldd6rs", "WandB run molto vecchio"),
        ("wandb/run-20250721_094314-87i25ri6", "WandB run vecchio"),
        ("wandb/run-20250721_195126-oq4iy624", "WandB run vecchio"),
    ]
    
    total_cleanup = 0
    for path, description in cleanup_candidates:
        size_gb = get_directory_size(path)
        if size_gb > 0:
            total_cleanup += size_gb
            logger.info(f"🗑️ {size_gb:6.2f}GB - {description}")
            logger.info(f"     📁 {path}")
            logger.info("")
    
    logger.info("=" * 80)
    logger.info(f"🎯 TOTALE LIBERABILE: {total_cleanup:.2f}GB")
    logger.info("=" * 80)
    
    return results, total_cleanup

if __name__ == "__main__":
    analyze_directory_sizes()
