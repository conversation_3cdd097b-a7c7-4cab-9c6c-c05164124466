#!/bin/bash
# 🔍 MONITORAGGIO JOB BASELINE EVALUATION COMPLETA

JOB_ID="2607560"
OUTPUT_DIR="evaluation_results/baseline_COMPLETE_400"
LOG_DIR="logs"

echo "🔍 MONITORAGGIO JOB BASELINE EVALUATION"
echo "======================================="
echo "Job ID: $JOB_ID"
echo "Timestamp: $(date)"
echo ""

# Controlla stato job
echo "📊 STATO JOB:"
squeue -j $JOB_ID 2>/dev/null || echo "Job non trovato in coda (potrebbe essere completato o non ancora iniziato)"
echo ""

# Controlla log esistenti
echo "📋 LOG DISPONIBILI:"
if ls ${LOG_DIR}/baseline_eval_400_* 2>/dev/null; then
    echo ""
    echo "📄 ULTIMI LOG:"
    tail -20 ${LOG_DIR}/baseline_eval_400_${JOB_ID}.out 2>/dev/null || echo "Log output non ancora disponibile"
    echo ""
    echo "❌ ERRORI:"
    tail -10 ${LOG_DIR}/baseline_eval_400_${JOB_ID}.err 2>/dev/null || echo "Log errori non ancora disponibile"
else
    echo "Nessun log ancora disponibile"
fi
echo ""

# Controlla risultati parziali
echo "📁 RISULTATI PARZIALI:"
if [ -d "$OUTPUT_DIR" ]; then
    echo "Directory output esiste:"
    ls -la "$OUTPUT_DIR" 2>/dev/null || echo "Directory vuota"
else
    echo "Directory output non ancora creata"
fi
echo ""

# Controlla spazio disco
echo "💾 SPAZIO DISCO:"
df -h /work/tesi_ediluzio | head -2
echo ""

# Controlla processi utente
echo "🖥️ PROCESSI ATTIVI:"
ps aux | grep ediluzio | grep -v grep | wc -l
echo "processi attivi per utente ediluzio"
echo ""

echo "🔄 Per monitoraggio continuo:"
echo "watch -n 30 'bash scripts/monitoring/check_baseline_job.sh'"
echo ""
echo "📊 Per vedere la coda completa:"
echo "squeue -u ediluzio"
