#!/usr/bin/env python3
"""
🎯 GRAFICI RADAR PERFETTI
Individuali + Combinato con legende PICCOLE che non coprono i grafici
"""

import os
import sys
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from math import pi
import argparse
from datetime import datetime
import base64
from io import BytesIO
from PIL import Image, ImageDraw
import xml.etree.ElementTree as ET
import re
import cairosvg
import seaborn as sns

# Setup matplotlib
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def convert_svg_to_png_base64(xml_content, size=(400, 300)):
    """Converte contenuto XML/SVG in immagine PNG base64 usando cairosvg"""
    try:
        # Pulisci e prepara il contenuto SVG
        svg_content = xml_content.strip()

        # Se non inizia con <svg, è il nostro formato custom - convertilo
        if not svg_content.startswith('<svg'):
            # Parsing del formato custom: style=...;d=M...
            paths = []
            lines = svg_content.split('\n')

            for line in lines:
                line = line.strip()
                if 'd=' in line:
                    # Estrai stile e path
                    parts = line.split('\td=')
                    if len(parts) == 2:
                        style_part = parts[0]
                        path_part = parts[1]

                        # Converti stile in attributi SVG
                        svg_style = ""
                        if 'fill:rgb(' in style_part:
                            fill_match = re.search(r'fill:rgb\((\d+),(\d+),(\d+)\)', style_part)
                            if fill_match:
                                r, g, b = fill_match.groups()
                                svg_style += f'fill="rgb({r},{g},{b})" '

                        if 'stroke:rgb(' in style_part:
                            stroke_match = re.search(r'stroke:rgb\((\d+),(\d+),(\d+)\)', style_part)
                            if stroke_match:
                                r, g, b = stroke_match.groups()
                                svg_style += f'stroke="rgb({r},{g},{b})" '
                        elif 'stroke:None' in style_part:
                            svg_style += 'stroke="none" '

                        if 'stroke-width:' in style_part:
                            width_match = re.search(r'stroke-width:([\d.]+)', style_part)
                            if width_match:
                                svg_style += f'stroke-width="{width_match.group(1)}" '

                        if 'opacity:' in style_part:
                            opacity_match = re.search(r'opacity:([\d.]+)', style_part)
                            if opacity_match:
                                svg_style += f'opacity="{opacity_match.group(1)}" '

                        # Aggiungi path
                        paths.append(f'<path {svg_style} d="{path_part}" />')

            # Crea SVG completo con viewBox più ampio per non croppare
            width, height = size
            svg_content = f'''<svg xmlns="http://www.w3.org/2000/svg"
                             width="{width}" height="{height}"
                             viewBox="0 0 600 500">
                             {''.join(paths)}
                             </svg>'''

        # Converti SVG in PNG usando cairosvg
        png_data = cairosvg.svg2png(bytestring=svg_content.encode('utf-8'),
                                   output_width=size[0],
                                   output_height=size[1])

        # Converti in base64
        img_str = base64.b64encode(png_data).decode()
        return f"data:image/png;base64,{img_str}"

    except Exception as e:
        print(f"⚠️ Errore conversione SVG: {e}")
        # Fallback: crea immagine placeholder
        try:
            img = Image.new('RGB', size, 'white')
            draw = ImageDraw.Draw(img)
            draw.rectangle([10, 10, size[0]-10, size[1]-10], outline='gray', width=2)
            draw.text((size[0]//2-30, size[1]//2-10), "SVG Error", fill='gray')

            buffer = BytesIO()
            img.save(buffer, format='PNG')
            img_str = base64.b64encode(buffer.getvalue()).decode()
            return f"data:image/png;base64,{img_str}"
        except:
            return None

def select_diverse_examples(dataset_data, num_examples=25):
    """Seleziona esempi diversificati e interessanti dal dataset"""
    if not dataset_data or len(dataset_data) < num_examples:
        return list(range(min(len(dataset_data), num_examples)))

    # File da escludere (forme geometriche semplici e poco interessanti)
    excluded_files = {
        'wd_1406502.svg',  # Forma geometrica semplice
        'ki_0093555.svg',  # Forma geometrica semplice
        'wd_1231171.svg',  # Forma geometrica semplice
        'wd_1074214.svg'   # Forma geometrica semplice
    }

    # Filtra esempi con caption valide e file interessanti
    valid_examples = []
    for i, item in enumerate(dataset_data):
        caption = item.get('caption', '')
        xml_content = item.get('xml', '') or item.get('xml_content', '')
        filename = item.get('filename', {}).get('file', '')

        # Escludi file specifici
        if filename in excluded_files:
            continue

        # Criteri di validità
        if (len(caption) >= 30 and
            caption.strip().endswith(('.', '!', '?')) and
            len(xml_content) >= 50 and
            'The image depicts .' not in caption):  # Evita caption incomplete
            valid_examples.append(i)

    print(f"📊 Esempi validi trovati: {len(valid_examples)}/{len(dataset_data)} (esclusi {len(excluded_files)} file)")

    if len(valid_examples) < num_examples:
        print(f"⚠️ Solo {len(valid_examples)} esempi validi disponibili")
        return valid_examples

    # Criteri per selezionare esempi interessanti dai validi
    selected_indices = []

    # 1. Priorità per loghi e icone interessanti
    interesting_examples = []
    logo_keywords = ['logo', 'icon', 'brand', 'symbol', 'sign', 'badge', 'emblem', 'company', 'business']

    for i in valid_examples:
        caption = dataset_data[i].get('caption', '').lower()
        filename = dataset_data[i].get('filename', {}).get('file', '').lower()

        # Priorità per loghi e icone
        if any(keyword in caption or keyword in filename for keyword in logo_keywords):
            interesting_examples.append(i)

    print(f"🎨 Trovati {len(interesting_examples)} esempi con loghi/icone interessanti")

    # 2. Esempi con caption di lunghezza diversa (brevi, medie, lunghe)
    caption_lengths = [(i, len(dataset_data[i].get('caption', ''))) for i in valid_examples]
    caption_lengths.sort(key=lambda x: x[1])

    # Prendi esempi da diverse fasce di lunghezza
    short_examples = [x[0] for x in caption_lengths[:len(caption_lengths)//3]]
    medium_examples = [x[0] for x in caption_lengths[len(caption_lengths)//3:2*len(caption_lengths)//3]]
    long_examples = [x[0] for x in caption_lengths[2*len(caption_lengths)//3:]]

    # Seleziona proporzionalmente con priorità per loghi
    import random
    random.seed(42)  # Per risultati riproducibili

    # Prima aggiungi loghi interessanti (fino a 10)
    if interesting_examples:
        logo_count = min(10, len(interesting_examples))
        selected_indices.extend(random.sample(interesting_examples, logo_count))
        print(f"✅ Selezionati {logo_count} loghi/icone interessanti")

    # Poi aggiungi esempi diversificati per lunghezza
    remaining = num_examples - len(selected_indices)
    if remaining > 0:
        # Rimuovi i già selezionati dalle liste
        available_short = [x for x in short_examples if x not in selected_indices]
        available_medium = [x for x in medium_examples if x not in selected_indices]
        available_long = [x for x in long_examples if x not in selected_indices]

        # Distribuisci il rimanente
        short_count = min(remaining // 3, len(available_short))
        medium_count = min(remaining // 3, len(available_medium))
        long_count = min(remaining - short_count - medium_count, len(available_long))

        if available_short:
            selected_indices.extend(random.sample(available_short, short_count))
        if available_medium:
            selected_indices.extend(random.sample(available_medium, medium_count))
        if available_long:
            selected_indices.extend(random.sample(available_long, long_count))

    # Se ancora non abbiamo abbastanza, aggiungi casualmente dai validi
    remaining = num_examples - len(selected_indices)
    if remaining > 0:
        available = list(set(valid_examples) - set(selected_indices))
        if available:
            selected_indices.extend(random.sample(available, min(remaining, len(available))))

    return sorted(selected_indices[:num_examples])

def verify_data_authenticity(examples):
    """Verifica che tutti i dati siano reali e non inventati"""
    print("🔍 Verifica autenticità dati...")

    issues = []

    for i, example in enumerate(examples):
        # Verifica XML content
        xml_content = example.get('xml_content', '')
        if not xml_content or len(xml_content) < 50:
            issues.append(f"Esempio {i+1}: XML content troppo breve o mancante")

        # Verifica ground truth
        ground_truth = example.get('ground_truth', '')
        if not ground_truth or len(ground_truth) < 20:
            issues.append(f"Esempio {i+1}: Ground truth troppo breve o mancante")

        # Verifica che ci siano modelli
        models = example.get('models', {})
        if not models:
            issues.append(f"Esempio {i+1}: Nessun modello presente")

        # Verifica caption dei modelli
        for model_name, model_data in models.items():
            caption = model_data.get('caption', '')
            if not caption:
                issues.append(f"Esempio {i+1}, {model_name}: Caption mancante")
            elif model_name == 'BLIP-2' and len(caption) < 3:
                # BLIP-2 può avere caption molto brevi, soglia più bassa
                issues.append(f"Esempio {i+1}, {model_name}: Caption troppo breve")
            elif model_name != 'BLIP-2' and len(caption) < 10:
                issues.append(f"Esempio {i+1}, {model_name}: Caption troppo breve")
            elif caption == ground_truth:
                issues.append(f"Esempio {i+1}, {model_name}: Caption identica al ground truth (possibile errore)")

    if issues:
        print("⚠️ Problemi rilevati:")
        for issue in issues[:10]:  # Mostra solo i primi 10
            print(f"  - {issue}")
        if len(issues) > 10:
            print(f"  ... e altri {len(issues) - 10} problemi")
    else:
        print("✅ Tutti i dati sembrano autentici e completi")

    return len(issues) == 0

def load_qualitative_examples(results_dir, num_examples=25):
    """Carica esempi qualitativi diversificati dai file di risultati - Gemma, Llama e baseline"""
    examples = []

    # Prima carica il dataset originale per XML content
    dataset_file = "data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json"
    dataset_data = []
    if os.path.exists(dataset_file):
        try:
            with open(dataset_file, 'r') as f:
                dataset_data = json.load(f)
            print(f"✅ Dataset caricato: {len(dataset_data)} esempi totali")
        except Exception as e:
            print(f"⚠️ Errore caricamento dataset: {e}")

    # Seleziona esempi diversificati
    selected_indices = select_diverse_examples(dataset_data, num_examples)
    print(f"✅ Selezionati {len(selected_indices)} esempi diversificati")

    # Modelli trained che vogliamo includere
    target_models = ['GEMMA_T9', 'LLAMA_T8']

    # Inizializza esempi con i dati del dataset originale
    for idx in selected_indices:
        if idx < len(dataset_data):
            item = dataset_data[idx]
            examples.append({
                'id': idx,
                'xml_content': item.get('xml', '') or item.get('xml_content', ''),
                'ground_truth': item.get('caption', ''),
                'filename': item.get('filename', {}).get('file', f'example_{idx}'),
                'models': {}
            })

    # Cerca file di risultati con esempi
    for filename in os.listdir(results_dir):
        if 'results_' in filename and filename.endswith('.json'):
            filepath = os.path.join(results_dir, filename)
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)

                if 'results' in data and isinstance(data['results'], list):
                    model_name = data.get('model', filename.split('_')[0])

                    # Filtra solo i modelli target
                    if model_name not in target_models:
                        continue

                    print(f"✅ Caricando {model_name}: {len(data['results'])} risultati")

                    # Carica solo gli esempi selezionati
                    for example_idx, selected_idx in enumerate(selected_indices):
                        if selected_idx < len(data['results']) and example_idx < len(examples):
                            result = data['results'][selected_idx]

                            # Pulisci e normalizza la caption
                            caption = result.get('generated_caption', '') or result.get('prediction', '')
                            if caption:
                                # Se c'è duplicazione con \nmodel\n, prendi la parte dopo se più completa
                                if '\nmodel\n' in caption:
                                    parts = caption.split('\nmodel\n')
                                    if len(parts) > 1:
                                        # Prendi la parte più lunga e completa
                                        part1 = parts[0].strip()
                                        part2 = parts[1].strip()

                                        # Se la seconda parte è più lunga e completa, usala
                                        if len(part2) > len(part1) and part2.endswith('.'):
                                            caption = part2
                                        # Se la prima parte è completa, usala
                                        elif part1.endswith('.'):
                                            caption = part1
                                        # Altrimenti usa la più lunga
                                        else:
                                            caption = part1 if len(part1) > len(part2) else part2
                                    else:
                                        caption = parts[0].strip()

                                # Normalizza spazi e newline
                                caption = ' '.join(caption.split())

                                # Se la caption sembra troncata (non finisce con punteggiatura), segnala
                                if caption and not caption[-1] in '.!?':
                                    # Se è molto breve, potrebbe essere troncata
                                    if len(caption) < 50:
                                        caption = ""  # Rimuovi caption troppo brevi e incomplete

                            examples[example_idx]['models'][model_name] = {
                                'caption': caption,
                                'prediction': caption
                            }
            except Exception as e:
                print(f"⚠️ Errore caricamento {filename}: {e}")
                continue

    # Carica i baseline dai file specifici
    baseline_files = {
        'BLIP-2': 'evaluation_results/blip2_FINAL_400/blip2_results.json',
        'Florence-2': 'evaluation_results/baseline_COMPLETE_400_CPU/florence2_results_20250724_162226.json',
        'Idefics3': 'experiments/baseline_results/idefics3_hf_official/idefics3_results.json'
    }

    for model_name, filepath in baseline_files.items():
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)

                # Gestisci diversi formati
                if isinstance(data, list):
                    results = data
                elif 'results' in data:
                    results = data['results']
                else:
                    continue

                print(f"✅ Caricando baseline {model_name}: {len(results)} risultati")

                # Carica solo gli esempi selezionati
                for example_idx, selected_idx in enumerate(selected_indices):
                    if selected_idx < len(results) and example_idx < len(examples):
                        result = results[selected_idx]

                        # Pulisci caption da tag HTML/XML e normalizza
                        caption = result.get('generated_caption', '') or result.get('prediction', '')
                        if caption:
                            # Rimuovi tag </s><s> da Florence-2
                            caption = caption.replace('</s><s>', '').replace('</s>', '').replace('<s>', '').strip()
                            # Normalizza spazi e newline
                            caption = ' '.join(caption.split())
                            # Assicurati che non sia troncata
                            if len(caption) > 1000:
                                # Se troppo lunga, taglia in modo intelligente
                                sentences = caption.split('. ')
                                caption = '. '.join(sentences[:3]) + '.'

                        examples[example_idx]['models'][model_name] = {
                            'caption': caption,
                            'prediction': caption
                        }
            except Exception as e:
                print(f"⚠️ Errore caricamento {model_name}: {e}")
                continue

    # Verifica autenticità dei dati prima di restituire
    verify_data_authenticity(examples[:num_examples])

    return examples[:num_examples]

def create_perfect_radar_chart(data, model_name, output_file, title_suffix=""):
    """Crea grafico radar perfetto per singolo modello"""

    # Metriche complete con tutti i BLEU
    metrics = ['CLIP Score', 'BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'ROUGE-L', 'METEOR', 'CIDEr']

    # Estrai valori (normalizzati 0-100)
    values = [
        data.get('real_clip_score', {}).get('mean', 0),  # CLIP già in percentuale
        data.get('bleu_1', 0) * 100,
        data.get('bleu_2', 0) * 100,
        data.get('bleu_3', 0) * 100,
        data.get('bleu_4', 0) * 100,
        data.get('rouge_l', 0) * 100,
        data.get('meteor', 0) * 100,
        data.get('cider', 0) * 10  # CIDEr scala diversa
    ]
    
    # Chiudi il poligono
    values += values[:1]
    
    # Angoli per ogni metrica
    angles = [n / float(len(metrics)) * 2 * pi for n in range(len(metrics))]
    angles += angles[:1]
    
    # Crea figura con dimensioni perfette
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # Colori per modello
    colors = {
        'florence2': '#FF6B6B',
        'idefics3': '#4ECDC4', 
        'blip2': '#45B7D1',
        'gemma_t9': '#96CEB4',
        'llama_t8': '#FFEAA7'
    }
    
    color = colors.get(model_name.lower().replace('-', '_'), '#FF6B6B')
    
    # Disegna il poligono
    ax.plot(angles, values, 'o-', linewidth=3, label=model_name, color=color, markersize=8)
    ax.fill(angles, values, alpha=0.25, color=color)
    
    # Personalizza il grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics, fontsize=12, fontweight='bold')
    ax.set_ylim(0, 100)
    
    # Griglia personalizzata
    ax.set_yticks([20, 40, 60, 80, 100])
    ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'], fontsize=10, alpha=0.7)
    ax.grid(True, alpha=0.3)
    
    # Titolo grande e chiaro
    plt.title(f'🎯 {model_name} Performance{title_suffix}', 
              size=16, fontweight='bold', pad=30)
    
    # Legenda PICCOLA in alto a destra con tutti i valori
    legend_text = f'{model_name}\nCLIP: {values[0]:.1f}%\nBLEU-1: {values[1]:.1f}%\nBLEU-2: {values[2]:.1f}%\nBLEU-3: {values[3]:.1f}%\nBLEU-4: {values[4]:.1f}%\nROUGE-L: {values[5]:.1f}%\nMETEOR: {values[6]:.1f}%\nCIDEr: {values[7]:.1f}%'
    ax.text(1.15, 0.95, legend_text, transform=ax.transAxes, fontsize=8,
            verticalalignment='top', horizontalalignment='left',
            bbox=dict(boxstyle='round,pad=0.4', facecolor='white', alpha=0.95, edgecolor='gray', linewidth=1))
    
    # Aggiungi valori numerici sui punti
    for angle, value, metric in zip(angles[:-1], values[:-1], metrics):
        ax.annotate(f'{value:.1f}%', 
                   xy=(angle, value), 
                   xytext=(10, 10), 
                   textcoords='offset points',
                   fontsize=9, 
                   fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ Radar chart salvato: {output_file}")

def create_combined_radar_chart(all_data, output_file):
    """Crea grafico radar combinato con TUTTI i modelli"""

    metrics = ['CLIP Score', 'BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4', 'ROUGE-L', 'METEOR', 'CIDEr']
    angles = [n / float(len(metrics)) * 2 * pi for n in range(len(metrics))]
    angles += angles[:1]
    
    # Crea figura più grande per il combinato
    fig, ax = plt.subplots(figsize=(16, 14), subplot_kw=dict(projection='polar'))

    # Colori e stili per ogni modello (tutti i 5 modelli)
    model_styles = {
        'florence_2': {'color': '#E74C3C', 'linestyle': '-', 'marker': 'o', 'name': 'Florence-2'},
        'blip_2': {'color': '#3498DB', 'linestyle': '--', 'marker': 's', 'name': 'BLIP-2'},
        'gemma_t9': {'color': '#27AE60', 'linestyle': '-.', 'marker': '^', 'name': 'Gemma-T9'},
        'llama_t8': {'color': '#F39C12', 'linestyle': ':', 'marker': 'D', 'name': 'LLAMA-T8'},
        'idefics3': {'color': '#9B59B6', 'linestyle': '-', 'marker': 'v', 'name': 'Idefics3'}
    }
    
    # Prima raccogliamo tutti i valori per calcolare la scala dinamica
    all_values = []
    model_data = {}

    for model_key, data in all_data.items():
        if model_key not in model_styles:
            continue

        # Estrai valori normalizzati con tutti i BLEU
        values = [
            data['metrics'].get('real_clip_score', {}).get('mean', 0),
            data['metrics'].get('bleu_1', 0) * 100,
            data['metrics'].get('bleu_2', 0) * 100,
            data['metrics'].get('bleu_3', 0) * 100,
            data['metrics'].get('bleu_4', 0) * 100,
            data['metrics'].get('rouge_l', 0) * 100,
            data['metrics'].get('meteor', 0) * 100,
            data['metrics'].get('cider', 0) * 10
        ]
        model_data[model_key] = values
        all_values.extend(values)

    # Calcola scala dinamica
    max_value = max(all_values) if all_values else 100
    scale_max = min(100, max_value * 1.2)  # 20% di margine, max 100

    # Ora disegna ogni modello
    for model_key, values in model_data.items():
        style = model_styles[model_key]
        values += values[:1]  # Chiudi il poligono

        # Disegna linea e riempimento (più visibile)
        ax.plot(angles, values,
               linestyle=style['linestyle'],
               linewidth=4,
               label=style['name'],
               color=style['color'],
               marker=style['marker'],
               markersize=12,
               alpha=0.9,
               markeredgewidth=2,
               markeredgecolor='white')

        ax.fill(angles, values, alpha=0.15, color=style['color'])
    
    # Personalizza grafico combinato con scala dinamica
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics, fontsize=14, fontweight='bold')
    ax.set_ylim(0, scale_max)

    # Griglia dinamica più visibile
    num_ticks = 5
    tick_values = [scale_max * i / num_ticks for i in range(1, num_ticks + 1)]
    ax.set_yticks(tick_values)
    ax.set_yticklabels([f'{tick:.0f}%' for tick in tick_values], fontsize=12, alpha=0.8)
    ax.grid(True, alpha=0.5, linewidth=1.5)
    
    # Titolo principale
    plt.title('🏆 CONFRONTO COMPLETO MODELLI SVG CAPTIONING', 
              size=20, fontweight='bold', pad=40)
    
    # Crea legenda personalizzata con valori
    legend_lines = []
    legend_labels = []

    for model_key, values in model_data.items():
        if model_key in model_styles:
            style = model_styles[model_key]
            # Crea linea per la legenda
            line = plt.Line2D([0], [0], color=style['color'], linewidth=3,
                             linestyle=style['linestyle'], marker=style['marker'], markersize=8)
            legend_lines.append(line)

            # Label con valori principali (CLIP e BLEU-4)
            clip_val = values[0]
            bleu4_val = values[4]  # BLEU-4 è ora all'indice 4
            label = f"{style['name']}\nCLIP: {clip_val:.1f}% | BLEU-4: {bleu4_val:.1f}%"
            legend_labels.append(label)

    # Posiziona legenda fuori dal grafico
    ax.legend(legend_lines, legend_labels, loc='center left', bbox_to_anchor=(1.05, 0.5),
             fontsize=10, frameon=True, fancybox=True, shadow=True,
             borderaxespad=0, handlelength=2)
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ Radar chart combinato salvato: {output_file}")

def create_html_report(all_data, individual_charts, combined_chart, output_file, qualitative_examples=None):
    """Crea report HTML con tutti i grafici radar e esempi qualitativi"""
    
    # Ordina modelli per CLIP Score
    sorted_models = sorted(all_data.items(),
                          key=lambda x: x[1]['metrics'].get('real_clip_score', {}).get('mean', 0),
                          reverse=True)
    
    html_content = f"""
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 RADAR CHARTS COMPREHENSIVE - SVG Captioning</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 40px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.8em;
            font-weight: bold;
        }}
        .content {{
            padding: 40px;
        }}
        .section {{
            margin-bottom: 50px;
        }}
        .section h2 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
            font-size: 1.8em;
        }}
        .combined-chart {{
            text-align: center;
            margin: 40px 0;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
        }}
        .combined-chart img {{
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }}
        .individual-charts {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }}
        .chart-card {{
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }}
        .chart-card:hover {{
            transform: translateY(-5px);
        }}
        .chart-card img {{
            width: 100%;
            height: auto;
            border-radius: 10px;
        }}
        .chart-title {{
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            color: #2c3e50;
        }}
        .metrics-summary {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}
        .metric-card {{
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .metric-value {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .metric-name {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        .footer {{
            background: #2c3e50;
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .qualitative-grid {{
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            margin: 30px 0;
        }}
        .example-card {{
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }}
        .example-header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 10px 10px 0 0;
            text-align: center;
            margin-bottom: 20px;
        }}

        .example-header h3 {{
            color: white;
            margin: 0 0 8px 0;
            font-size: 1.3em;
        }}

        .file-info {{
            display: flex;
            justify-content: space-between;
            font-size: 0.85em;
            opacity: 0.9;
            flex-wrap: wrap;
            gap: 10px;
        }}

        .filename {{
            font-weight: bold;
        }}

        .stats {{
            font-style: italic;
        }}
        .example-content {{
            display: grid;
            grid-template-columns: 400px 1fr;
            gap: 30px;
            align-items: start;
        }}
        .image-section {{
            text-align: center;
        }}
        .svg-preview {{
            width: 400px;
            height: 300px;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            background: white;
            object-fit: contain;
        }}
        .no-image {{
            width: 400px;
            height: 300px;
            background: #e9ecef;
            border: 2px dashed #adb5bd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 1.1em;
        }}
        .captions-section {{
            display: flex;
            flex-direction: column;
            gap: 15px;
        }}
        .ground-truth {{
            background: #d4edda;
            border-left: 4px solid #28a745;
            padding: 15px;
            border-radius: 5px;
        }}
        .model-caption {{
            background: #e2e3e5;
            border-left: 4px solid #6c757d;
            padding: 15px;
            border-radius: 5px;
        }}
        .ground-truth p, .model-caption p {{
            margin: 8px 0 0 0;
            line-height: 1.6;
            font-size: 0.95em;
            text-align: justify;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 RADAR CHARTS COMPREHENSIVE</h1>
            <p>Analisi Visiva Completa Performance Modelli SVG Captioning</p>
            <p>Generato il {datetime.now().strftime('%d/%m/%Y alle %H:%M')}</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>🏆 Confronto Completo</h2>
                <div class="combined-chart">
                    <img src="{os.path.basename(combined_chart)}" alt="Radar Chart Combinato">
                    <p><strong>Tutti i modelli a confronto con metriche normalizzate 0-100%</strong></p>
                </div>
            </div>
            
            <div class="section">
                <h2>📊 Performance Individuali</h2>
                <div class="individual-charts">
"""
    
    # Aggiungi chart individuali
    for i, (model_key, data) in enumerate(sorted_models):
        model_name = model_key.replace('_', '-').title()
        chart_file = individual_charts.get(model_key, '')
        
        if chart_file and os.path.exists(chart_file):
            clip_score = data['metrics'].get('real_clip_score', {}).get('mean', 0)
            
            html_content += f"""
                    <div class="chart-card">
                        <div class="chart-title">
                            {['🥇', '🥈', '🥉', '🏅', '🏅'][i]} {model_name}
                        </div>
                        <img src="{os.path.basename(chart_file)}" alt="Radar {model_name}">
                        <div class="metrics-summary">
                            <div class="metric-card">
                                <div class="metric-value">{clip_score:.1f}%</div>
                                <div class="metric-name">CLIP Score</div>
                            </div>
                        </div>
                    </div>
"""
    
    html_content += """
                </div>
            </div>
        </div>
"""

    # Aggiungi sezione qualitativa se disponibile
    if qualitative_examples:
        html_content += """
        <div class="section">
            <h2>🔍 ESEMPI QUALITATIVI</h2>
            <p>Confronto visivo delle caption generate dai modelli con ground truth</p>

            <div class="qualitative-grid">
"""

        for i, example in enumerate(qualitative_examples[:25]):  # Mostra 25 esempi diversificati
            # Converti SVG in immagine
            image_b64 = convert_svg_to_png_base64(example['xml_content'])

            # Ottieni informazioni sul file
            filename = example.get('filename', f'example_{i+1}')

            html_content += f"""
                <div class="example-card">
                    <div class="example-header">
                        <h3>📝 Esempio {i+1}</h3>
                        <div class="file-info">
                            <span class="filename">📁 File: {filename}</span>
                            <span class="stats">📊 GT: {len(example['ground_truth'])} caratteri | XML: {len(example['xml_content'])} caratteri</span>
                        </div>
                    </div>

                    <div class="example-content">
                        <div class="image-section">
"""

            if image_b64:
                html_content += f'<img src="{image_b64}" alt="SVG {i+1}" class="svg-preview">'
            else:
                html_content += '<div class="no-image">🖼️ Immagine non disponibile</div>'

            html_content += f"""
                        </div>

                        <div class="captions-section">
                            <div class="ground-truth">
                                <strong>🎯 Ground Truth:</strong>
                                <p>{example['ground_truth']}</p>
                            </div>
"""

            # Aggiungi caption dei modelli (ordine specifico)
            model_order = ['GEMMA_T9', 'LLAMA_T8', 'Florence-2', 'BLIP-2', 'Idefics3']
            for model_name in model_order:
                if model_name in example['models']:
                    model_data = example['models'][model_name]
                    caption = model_data.get('caption') or model_data.get('prediction', '')
                    if caption:
                        # Emoji specifici per modello
                        emoji = {'GEMMA_T9': '🟢', 'LLAMA_T8': '🟠', 'Florence-2': '🔵', 'BLIP-2': '🟣', 'Idefics3': '🟡'}.get(model_name, '🤖')
                        html_content += f"""
                            <div class="model-caption">
                                <strong>{emoji} {model_name}:</strong>
                                <p>{caption}</p>
                            </div>
"""

            html_content += """
                        </div>
                    </div>
                </div>
"""

        html_content += """
            </div>
        </div>
"""

    html_content += """
        <div class="footer">
            <p>🚀 Grafici generati dal sistema di valutazione comprehensive SVG Captioning</p>
            <p>Radar charts con legende ottimizzate e metriche normalizzate</p>
        </div>
    </div>
</body>
</html>
"""
    
    # Salva HTML
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ Report HTML salvato: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Crea grafici radar perfetti")
    parser.add_argument("--results_dir", default="evaluation_results/comprehensive_metrics",
                       help="Directory con risultati comprehensive")
    parser.add_argument("--output_dir", default="evaluation_results/radar_charts_PERFECT",
                       help="Directory output grafici")
    
    args = parser.parse_args()
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    print("🎯 CREAZIONE GRAFICI RADAR PERFETTI")
    print("=" * 50)
    
    # Carica tutti i risultati (escludi Dataset-Test)
    all_data = {}

    for file in os.listdir(args.results_dir):
        if 'comprehensive_metrics' in file and file.endswith('.json'):
            # Estrai nome modello dal filename
            model_key = file.split('_comprehensive_metrics')[0].lower().replace('-', '_')

            # Escludi Dataset-Test
            if model_key == 'dataset_test':
                print(f"⏭️ Saltato: {model_key} (escluso)")
                continue

            file_path = os.path.join(args.results_dir, file)

            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    all_data[model_key] = data
                    print(f"✅ Caricato: {model_key}")
            except Exception as e:
                print(f"❌ Errore caricamento {file}: {e}")
    
    if not all_data:
        print("❌ Nessun dato trovato!")
        return
    
    # Crea grafici individuali
    individual_charts = {}
    
    for model_key, data in all_data.items():
        model_name = model_key.replace('_', '-').title()
        output_file = os.path.join(args.output_dir, f"{model_key}_radar_perfect.png")
        
        create_perfect_radar_chart(data['metrics'], model_name, output_file)
        individual_charts[model_key] = output_file
    
    # Crea grafico combinato
    combined_chart = os.path.join(args.output_dir, "ALL_MODELS_radar_combined_PERFECT.png")
    create_combined_radar_chart(all_data, combined_chart)

    # Carica esempi qualitativi dalla directory trained_models
    qualitative_examples = []
    trained_models_dir = "evaluation_results/trained_models"
    if os.path.exists(trained_models_dir):
        qualitative_examples = load_qualitative_examples(trained_models_dir, num_examples=25)
        print(f"✅ Caricati {len(qualitative_examples)} esempi qualitativi diversificati")

    # Crea report HTML
    html_report = os.path.join(args.output_dir, "RADAR_CHARTS_REPORT.html")
    create_html_report(all_data, individual_charts, combined_chart, html_report, qualitative_examples)
    
    print("=" * 50)
    print("✅ GRAFICI RADAR PERFETTI COMPLETATI!")
    print(f"📁 Directory: {args.output_dir}")
    print(f"📄 Report HTML: {html_report}")
    print("=" * 50)

if __name__ == "__main__":
    main()
