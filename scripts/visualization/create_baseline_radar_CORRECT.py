#!/usr/bin/env python3
"""
📊 RADAR CHART BASELINE CORRETTO
Genera radar chart per i risultati baseline con parametri corretti
"""

import os
import json
import argparse
import logging
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import seaborn as sns

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Configurazione plot
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_metrics(metrics_file):
    """Carica metriche da file JSON"""
    try:
        with open(metrics_file, 'r') as f:
            data = json.load(f)
        return data
    except Exception as e:
        logger.error(f"❌ Errore caricamento {metrics_file}: {e}")
        return {}

def create_radar_chart(metrics_data, output_path):
    """
    Crea radar chart per confronto baseline
    """
    logger.info("📊 Creazione radar chart baseline...")
    
    # <PERSON><PERSON>e da visualizzare (ordinate per importanza)
    metric_names = [
        'BLEU-1', 'BLEU-2', 'BLEU-3', 'BLEU-4',
        'METEOR', 'ROUGE-L', 'CIDEr', 'SPICE'
    ]
    
    # Mapping nomi metriche
    metric_mapping = {
        'BLEU-1': 'bleu_1',
        'BLEU-2': 'bleu_2', 
        'BLEU-3': 'bleu_3',
        'BLEU-4': 'bleu_4',
        'METEOR': 'meteor',
        'ROUGE-L': 'rouge_l',
        'CIDEr': 'cider',
        'SPICE': 'spice'
    }
    
    # Estrai dati per ogni modello
    models_data = {}
    for model_name, model_metrics in metrics_data.items():
        if 'error' in model_metrics:
            logger.warning(f"⚠️ Errore in {model_name}: {model_metrics['error']}")
            continue
            
        model_values = []
        for metric_display, metric_key in metric_mapping.items():
            value = model_metrics.get(metric_key, 0.0)
            # Normalizza valori per visualizzazione (0-1)
            if metric_key.startswith('bleu'):
                normalized_value = value  # BLEU già 0-1
            elif metric_key == 'meteor':
                normalized_value = value  # METEOR già 0-1
            elif metric_key == 'rouge_l':
                normalized_value = value  # ROUGE-L già 0-1
            elif metric_key == 'cider':
                normalized_value = min(value / 5.0, 1.0)  # CIDEr può essere >1
            elif metric_key == 'spice':
                normalized_value = value  # SPICE già 0-1
            else:
                normalized_value = value
            
            model_values.append(max(0.0, min(1.0, normalized_value)))
        
        models_data[model_name] = model_values
    
    if not models_data:
        logger.error("❌ Nessun dato valido per radar chart")
        return False
    
    # Setup radar chart
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # Angoli per ogni metrica
    angles = np.linspace(0, 2 * np.pi, len(metric_names), endpoint=False).tolist()
    angles += angles[:1]  # Chiudi il cerchio
    
    # Colori per modelli
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    
    # Plot per ogni modello
    for i, (model_name, values) in enumerate(models_data.items()):
        values += values[:1]  # Chiudi il cerchio
        
        ax.plot(angles, values, 'o-', linewidth=2, 
               label=model_name, color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    # Configurazione assi
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metric_names, fontsize=11)
    ax.set_ylim(0, 1)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=9)
    ax.grid(True)
    
    # Titolo e legenda
    plt.title('📊 Baseline Models Performance Comparison\n(SVG Captioning Task)', 
             size=16, fontweight='bold', pad=20)
    
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=10)
    
    # Aggiungi note
    note_text = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    note_text += f"Models: {', '.join(models_data.keys())}\n"
    note_text += "Metrics normalized to [0,1] range"
    
    plt.figtext(0.02, 0.02, note_text, fontsize=8, style='italic')
    
    # Salva
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"✅ Radar chart salvato: {output_path}")
    return True

def create_metrics_table(metrics_data, output_dir):
    """Crea tabella riassuntiva metriche"""
    
    # Crea tabella HTML
    html_content = """
    <html>
    <head>
        <title>Baseline Metrics Summary</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { border-collapse: collapse; width: 100%; margin: 20px 0; }
            th, td { border: 1px solid #ddd; padding: 12px; text-align: center; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .model-name { font-weight: bold; background-color: #f9f9f9; }
            .best-score { background-color: #d4edda; font-weight: bold; }
            .error { background-color: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <h1>📊 Baseline Models Metrics Summary</h1>
        <p><strong>Generated:</strong> """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</p>
        
        <table>
            <tr>
                <th>Model</th>
                <th>BLEU-1</th>
                <th>BLEU-2</th>
                <th>BLEU-3</th>
                <th>BLEU-4</th>
                <th>METEOR</th>
                <th>ROUGE-L</th>
                <th>CIDEr</th>
                <th>SPICE</th>
                <th>Examples</th>
            </tr>
    """
    
    # Trova migliori score per evidenziare
    all_metrics = {}
    for model_name, model_metrics in metrics_data.items():
        if 'error' not in model_metrics:
            for metric in ['bleu_1', 'bleu_2', 'bleu_3', 'bleu_4', 'meteor', 'rouge_l', 'cider', 'spice']:
                if metric not in all_metrics:
                    all_metrics[metric] = []
                all_metrics[metric].append(model_metrics.get(metric, 0.0))
    
    best_scores = {metric: max(scores) if scores else 0 for metric, scores in all_metrics.items()}
    
    # Aggiungi righe per ogni modello
    for model_name, model_metrics in metrics_data.items():
        html_content += f'<tr><td class="model-name">{model_name}</td>'
        
        if 'error' in model_metrics:
            html_content += f'<td colspan="8" class="error">Error: {model_metrics["error"]}</td>'
            html_content += f'<td>{model_metrics.get("total_examples", "N/A")}</td>'
        else:
            metrics = ['bleu_1', 'bleu_2', 'bleu_3', 'bleu_4', 'meteor', 'rouge_l', 'cider', 'spice']
            for metric in metrics:
                value = model_metrics.get(metric, 0.0)
                css_class = 'best-score' if abs(value - best_scores[metric]) < 1e-6 else ''
                html_content += f'<td class="{css_class}">{value:.4f}</td>'
            
            html_content += f'<td>{model_metrics.get("total_examples", "N/A")}</td>'
        
        html_content += '</tr>'
    
    html_content += """
        </table>
        
        <h2>📝 Notes</h2>
        <ul>
            <li><strong>BLEU (1-4):</strong> Bilingual Evaluation Understudy (higher is better, 0-1)</li>
            <li><strong>METEOR:</strong> Metric for Evaluation of Translation with Explicit ORdering (higher is better, 0-1)</li>
            <li><strong>ROUGE-L:</strong> Recall-Oriented Understudy for Gisting Evaluation - Longest Common Subsequence (higher is better, 0-1)</li>
            <li><strong>CIDEr:</strong> Consensus-based Image Description Evaluation (higher is better, can be >1)</li>
            <li><strong>SPICE:</strong> Semantic Propositional Image Caption Evaluation (higher is better, 0-1)</li>
        </ul>
        
        <p><em>Best scores are highlighted in green.</em></p>
    </body>
    </html>
    """
    
    # Salva tabella
    table_path = os.path.join(output_dir, f"baseline_metrics_table_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html")
    with open(table_path, 'w') as f:
        f.write(html_content)
    
    logger.info(f"✅ Tabella metriche salvata: {table_path}")
    return table_path

def main():
    parser = argparse.ArgumentParser(description="Crea radar chart per baseline corretti")
    parser.add_argument("--metrics_file", 
                       help="File JSON con metriche aggregate")
    parser.add_argument("--metrics_dir",
                       default="evaluation_results/baseline_CORRECT_FINAL",
                       help="Directory con file metriche")
    parser.add_argument("--output_dir",
                       default="evaluation_results/baseline_CORRECT_FINAL",
                       help="Directory di output")
    
    args = parser.parse_args()
    
    print("📊 CREAZIONE RADAR CHART BASELINE CORRETTO")
    print("=" * 50)
    
    # Carica metriche
    if args.metrics_file and os.path.exists(args.metrics_file):
        metrics_data = load_metrics(args.metrics_file)
    else:
        # Cerca file metriche aggregate nella directory
        metrics_files = []
        if os.path.exists(args.metrics_dir):
            for file in os.listdir(args.metrics_dir):
                if file.startswith('all_metrics_CORRECT_') and file.endswith('.json'):
                    metrics_files.append(os.path.join(args.metrics_dir, file))
        
        if not metrics_files:
            print(f"❌ Nessun file metriche trovato in {args.metrics_dir}")
            return
        
        # Usa il più recente
        latest_file = max(metrics_files, key=os.path.getmtime)
        print(f"📄 Usando file metriche: {os.path.basename(latest_file)}")
        metrics_data = load_metrics(latest_file)
    
    if not metrics_data:
        print("❌ Nessun dato metrica caricato")
        return
    
    print(f"🤖 Modelli trovati: {list(metrics_data.keys())}")
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Genera radar chart
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    radar_path = os.path.join(args.output_dir, f"baseline_radar_CORRECT_{timestamp}.png")
    
    success = create_radar_chart(metrics_data, radar_path)
    
    if success:
        # Crea anche tabella metriche
        table_path = create_metrics_table(metrics_data, args.output_dir)
        
        print("=" * 50)
        print("🎉 RADAR CHART E TABELLA CREATI!")
        print("=" * 50)
        print(f"📊 Radar chart: {os.path.basename(radar_path)}")
        print(f"📄 Tabella HTML: {os.path.basename(table_path)}")
        print(f"📁 Directory: {args.output_dir}")
    else:
        print("❌ Errore nella creazione del radar chart")

if __name__ == "__main__":
    main()
