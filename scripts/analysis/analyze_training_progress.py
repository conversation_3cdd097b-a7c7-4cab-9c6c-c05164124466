#!/usr/bin/env python3
"""
🔍 ANALISI PROGRESSO TRAINING
Analizza checkpoint e loss per decidere quando fermare il training
"""

import os
import json
import argparse
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def load_trainer_state(checkpoint_path):
    """Carica trainer_state.json da checkpoint"""
    trainer_state_path = os.path.join(checkpoint_path, "trainer_state.json")
    if os.path.exists(trainer_state_path):
        with open(trainer_state_path, 'r') as f:
            return json.load(f)
    return None

def analyze_model_progress(model_dir, model_name):
    """Analizza progresso di un modello"""
    print(f"\n🔍 ANALISI {model_name.upper()}")
    print("=" * 40)
    
    if not os.path.exists(model_dir):
        print(f"❌ Directory non trovata: {model_dir}")
        return None
    
    # Trova tutti i checkpoint
    checkpoints = []
    for item in os.listdir(model_dir):
        if item.startswith('checkpoint-') and os.path.isdir(os.path.join(model_dir, item)):
            checkpoint_num = int(item.split('-')[1])
            checkpoints.append((checkpoint_num, os.path.join(model_dir, item)))
    
    checkpoints.sort(key=lambda x: x[0])
    
    if not checkpoints:
        print("❌ Nessun checkpoint trovato")
        return None
    
    print(f"📊 Checkpoint trovati: {len(checkpoints)}")
    print(f"📈 Range: checkpoint-{checkpoints[0][0]} → checkpoint-{checkpoints[-1][0]}")
    
    # Analizza ultimo checkpoint
    last_checkpoint_num, last_checkpoint_path = checkpoints[-1]
    trainer_state = load_trainer_state(last_checkpoint_path)
    
    if not trainer_state:
        print("❌ Impossibile caricare trainer_state.json")
        return None
    
    # Estrai dati training
    log_history = trainer_state.get('log_history', [])
    current_step = trainer_state.get('global_step', 0)
    current_epoch = trainer_state.get('epoch', 0)
    max_steps = trainer_state.get('max_steps', 45000)
    
    print(f"📊 Step corrente: {current_step:,} / {max_steps:,} ({current_step/max_steps*100:.1f}%)")
    print(f"📊 Epoch corrente: {current_epoch:.3f}")
    
    # Analizza loss
    if log_history:
        # Ultimi 10 valori di loss
        recent_losses = [entry.get('loss', 0) for entry in log_history[-10:] if 'loss' in entry]
        if recent_losses:
            avg_recent_loss = np.mean(recent_losses)
            print(f"📊 Loss media ultimi 10 step: {avg_recent_loss:.4f}")
            
            # Confronta con loss iniziale
            initial_losses = [entry.get('loss', 0) for entry in log_history[:10] if 'loss' in entry]
            if initial_losses:
                avg_initial_loss = np.mean(initial_losses)
                improvement = ((avg_initial_loss - avg_recent_loss) / avg_initial_loss) * 100
                print(f"📊 Loss iniziale media: {avg_initial_loss:.4f}")
                print(f"📈 Miglioramento: {improvement:.1f}%")
        
        # Analizza trend loss
        all_losses = [entry.get('loss', 0) for entry in log_history if 'loss' in entry]
        if len(all_losses) > 50:
            # Ultimi 50 vs precedenti 50
            recent_50 = np.mean(all_losses[-50:])
            previous_50 = np.mean(all_losses[-100:-50]) if len(all_losses) > 100 else np.mean(all_losses[:-50])
            
            trend = "📈 MIGLIORANDO" if recent_50 < previous_50 else "📉 PEGGIORANDO" if recent_50 > previous_50 else "➡️ STABILE"
            print(f"📊 Trend recente: {trend}")
            print(f"   Ultimi 50 step: {recent_50:.4f}")
            print(f"   Precedenti 50 step: {previous_50:.4f}")
    
    # Raccomandazioni
    print(f"\n💡 RACCOMANDAZIONI:")
    
    progress_pct = current_step / max_steps * 100
    
    if progress_pct < 30:
        print("🟢 CONTINUA - Training ancora nelle fasi iniziali")
    elif progress_pct < 60:
        if recent_losses and len(recent_losses) > 5:
            if np.std(recent_losses) < 0.01:  # Loss molto stabile
                print("🟡 VALUTA - Loss molto stabile, potrebbe essere vicino alla convergenza")
            else:
                print("🟢 CONTINUA - Training in corso, loss ancora in movimento")
        else:
            print("🟢 CONTINUA - Training a metà percorso")
    else:
        if recent_losses and len(recent_losses) > 5:
            loss_std = np.std(recent_losses)
            if loss_std < 0.005:  # Loss molto stabile
                print("🔴 CONSIDERA STOP - Loss molto stabile, possibile convergenza")
            elif loss_std < 0.02:
                print("🟡 MONITORA - Loss abbastanza stabile, valuta stop presto")
            else:
                print("🟢 CONTINUA - Loss ancora in movimento")
        else:
            print("🟡 MONITORA - Training avanzato, controlla convergenza")
    
    return {
        'model_name': model_name,
        'current_step': current_step,
        'max_steps': max_steps,
        'progress_pct': progress_pct,
        'current_epoch': current_epoch,
        'recent_loss': avg_recent_loss if 'avg_recent_loss' in locals() else None,
        'improvement_pct': improvement if 'improvement' in locals() else None,
        'all_losses': all_losses if 'all_losses' in locals() else []
    }

def create_loss_plot(gemma_data, llama_data, output_path):
    """Crea grafico confronto loss"""
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Gemma plot
    if gemma_data and gemma_data['all_losses']:
        steps = range(len(gemma_data['all_losses']))
        ax1.plot(steps, gemma_data['all_losses'], 'b-', alpha=0.7, linewidth=1)
        ax1.set_title(f"📊 GEMMA T9 Training Loss\nStep: {gemma_data['current_step']:,} ({gemma_data['progress_pct']:.1f}%)")
        ax1.set_xlabel('Training Steps (×50)')
        ax1.set_ylabel('Loss')
        ax1.grid(True, alpha=0.3)
        
        # Media mobile
        if len(gemma_data['all_losses']) > 20:
            window = 20
            moving_avg = np.convolve(gemma_data['all_losses'], np.ones(window)/window, mode='valid')
            ax1.plot(range(window-1, len(gemma_data['all_losses'])), moving_avg, 'r-', linewidth=2, label='Media Mobile (20)')
            ax1.legend()
    
    # Llama plot
    if llama_data and llama_data['all_losses']:
        steps = range(len(llama_data['all_losses']))
        ax2.plot(steps, llama_data['all_losses'], 'g-', alpha=0.7, linewidth=1)
        ax2.set_title(f"📊 LLAMA T8 Training Loss\nStep: {llama_data['current_step']:,} ({llama_data['progress_pct']:.1f}%)")
        ax2.set_xlabel('Training Steps (×50)')
        ax2.set_ylabel('Loss')
        ax2.grid(True, alpha=0.3)
        
        # Media mobile
        if len(llama_data['all_losses']) > 20:
            window = 20
            moving_avg = np.convolve(llama_data['all_losses'], np.ones(window)/window, mode='valid')
            ax2.plot(range(window-1, len(llama_data['all_losses'])), moving_avg, 'r-', linewidth=2, label='Media Mobile (20)')
            ax2.legend()
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 Grafico loss salvato: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Analizza progresso training")
    parser.add_argument("--gemma_dir", 
                       default="experiments/xml_direct_input/outputs/gemma_t9_continue",
                       help="Directory checkpoint Gemma")
    parser.add_argument("--llama_dir",
                       default="experiments/xml_direct_input/outputs/llama_t8_continue", 
                       help="Directory checkpoint Llama")
    parser.add_argument("--output_dir",
                       default="analysis_results",
                       help="Directory output")
    
    args = parser.parse_args()
    
    print("🔍 ANALISI PROGRESSO TRAINING")
    print("=" * 60)
    print(f"📁 Gemma dir: {args.gemma_dir}")
    print(f"📁 Llama dir: {args.llama_dir}")
    print("=" * 60)
    
    # Analizza modelli
    gemma_data = analyze_model_progress(args.gemma_dir, "Gemma T9")
    llama_data = analyze_model_progress(args.llama_dir, "Llama T8")
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Crea grafico
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_path = os.path.join(args.output_dir, f"training_loss_analysis_{timestamp}.png")
    create_loss_plot(gemma_data, llama_data, plot_path)
    
    # Summary finale
    print("\n" + "=" * 60)
    print("🎯 SUMMARY FINALE")
    print("=" * 60)
    
    if gemma_data:
        print(f"🔵 GEMMA T9: Step {gemma_data['current_step']:,}/{gemma_data['max_steps']:,} ({gemma_data['progress_pct']:.1f}%)")
        if gemma_data['recent_loss']:
            print(f"   Loss recente: {gemma_data['recent_loss']:.4f}")
        if gemma_data['improvement_pct']:
            print(f"   Miglioramento: {gemma_data['improvement_pct']:.1f}%")
    
    if llama_data:
        print(f"🟢 LLAMA T8: Step {llama_data['current_step']:,}/{llama_data['max_steps']:,} ({llama_data['progress_pct']:.1f}%)")
        if llama_data['recent_loss']:
            print(f"   Loss recente: {llama_data['recent_loss']:.4f}")
        if llama_data['improvement_pct']:
            print(f"   Miglioramento: {llama_data['improvement_pct']:.1f}%")
    
    print(f"\n📊 Grafico: {plot_path}")
    print("=" * 60)

if __name__ == "__main__":
    main()
