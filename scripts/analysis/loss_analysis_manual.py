#!/usr/bin/env python3
"""
📊 ANALISI LOSS MANUALE
Analizza le loss di training da trainer_state.json
"""

import os
import json
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def load_trainer_state(checkpoint_path):
    """Carica trainer_state.json"""
    trainer_state_path = os.path.join(checkpoint_path, "trainer_state.json")
    
    if not os.path.exists(trainer_state_path):
        print(f"❌ File non trovato: {trainer_state_path}")
        return None
    
    with open(trainer_state_path, 'r') as f:
        return json.load(f)

def extract_loss_data(trainer_state):
    """Estrae dati di loss dal trainer_state"""
    if not trainer_state or 'log_history' not in trainer_state:
        return [], []
    
    steps = []
    losses = []
    
    for entry in trainer_state['log_history']:
        if 'loss' in entry and 'step' in entry:
            steps.append(entry['step'])
            losses.append(entry['loss'])
    
    return steps, losses

def analyze_loss_stats(steps, losses, model_name):
    """Analizza statistiche loss"""
    if not losses:
        print(f"❌ {model_name}: Nessuna loss trovata")
        return None
    
    print(f"\n📊 {model_name} LOSS ANALYSIS:")
    print("=" * 50)
    print(f"📈 Steps totali: {len(steps):,}")
    print(f"📈 Step range: {min(steps):,} → {max(steps):,}")
    print(f"📊 Loss iniziale: {losses[0]:.4f}")
    print(f"📊 Loss finale: {losses[-1]:.4f}")
    print(f"📊 Loss minima: {min(losses):.4f}")
    print(f"📊 Loss massima: {max(losses):.4f}")
    print(f"📊 Loss media: {np.mean(losses):.4f} (±{np.std(losses):.4f})")
    
    # Miglioramento
    improvement = ((losses[0] - losses[-1]) / losses[0]) * 100
    print(f"📈 Miglioramento: {improvement:.1f}%")
    
    # Ultimi 10 valori
    recent_losses = losses[-10:]
    print(f"📊 Loss media ultimi 10: {np.mean(recent_losses):.4f}")
    
    return {
        'model': model_name,
        'steps': steps,
        'losses': losses,
        'initial_loss': losses[0],
        'final_loss': losses[-1],
        'min_loss': min(losses),
        'max_loss': max(losses),
        'mean_loss': np.mean(losses),
        'std_loss': np.std(losses),
        'improvement_pct': improvement,
        'recent_loss': np.mean(recent_losses)
    }

def create_loss_comparison_plot(gemma_data, llama_data, output_path):
    """Crea grafico confronto loss"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
    
    # Gemma loss curve
    if gemma_data:
        ax1.plot(gemma_data['steps'], gemma_data['losses'], 'b-', alpha=0.7, linewidth=1)
        ax1.set_title(f"🔵 GEMMA T9 Training Loss\nStep: {max(gemma_data['steps']):,} | Final: {gemma_data['final_loss']:.4f}")
        ax1.set_xlabel('Training Steps')
        ax1.set_ylabel('Loss')
        ax1.grid(True, alpha=0.3)
        
        # Media mobile
        if len(gemma_data['losses']) > 50:
            window = 50
            moving_avg = np.convolve(gemma_data['losses'], np.ones(window)/window, mode='valid')
            ax1.plot(gemma_data['steps'][window-1:], moving_avg, 'r-', linewidth=2, label='Media Mobile (50)')
            ax1.legend()
    
    # Llama loss curve
    if llama_data:
        ax2.plot(llama_data['steps'], llama_data['losses'], 'g-', alpha=0.7, linewidth=1)
        ax2.set_title(f"🟢 LLAMA T8 Training Loss\nStep: {max(llama_data['steps']):,} | Final: {llama_data['final_loss']:.4f}")
        ax2.set_xlabel('Training Steps')
        ax2.set_ylabel('Loss')
        ax2.grid(True, alpha=0.3)
        
        # Media mobile
        if len(llama_data['losses']) > 50:
            window = 50
            moving_avg = np.convolve(llama_data['losses'], np.ones(window)/window, mode='valid')
            ax2.plot(llama_data['steps'][window-1:], moving_avg, 'r-', linewidth=2, label='Media Mobile (50)')
            ax2.legend()
    
    # Confronto diretto
    if gemma_data and llama_data:
        # Normalizza steps per confronto
        max_steps = min(max(gemma_data['steps']), max(llama_data['steps']))
        
        gemma_subset = [(s, l) for s, l in zip(gemma_data['steps'], gemma_data['losses']) if s <= max_steps]
        llama_subset = [(s, l) for s, l in zip(llama_data['steps'], llama_data['losses']) if s <= max_steps]
        
        if gemma_subset and llama_subset:
            g_steps, g_losses = zip(*gemma_subset)
            l_steps, l_losses = zip(*llama_subset)
            
            ax3.plot(g_steps, g_losses, 'b-', alpha=0.8, linewidth=2, label='GEMMA T9')
            ax3.plot(l_steps, l_losses, 'g-', alpha=0.8, linewidth=2, label='LLAMA T8')
            ax3.set_title(f"📊 CONFRONTO DIRETTO LOSS\n(fino a step {max_steps:,})")
            ax3.set_xlabel('Training Steps')
            ax3.set_ylabel('Loss')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
    
    # Statistiche comparative
    ax4.axis('off')
    
    stats_text = "📊 STATISTICHE COMPARATIVE\n"
    stats_text += "=" * 40 + "\n\n"
    
    if gemma_data:
        stats_text += f"🔵 GEMMA T9:\n"
        stats_text += f"   Steps: {max(gemma_data['steps']):,}\n"
        stats_text += f"   Loss iniziale: {gemma_data['initial_loss']:.4f}\n"
        stats_text += f"   Loss finale: {gemma_data['final_loss']:.4f}\n"
        stats_text += f"   Miglioramento: {gemma_data['improvement_pct']:.1f}%\n"
        stats_text += f"   Loss media: {gemma_data['mean_loss']:.4f}\n\n"
    
    if llama_data:
        stats_text += f"🟢 LLAMA T8:\n"
        stats_text += f"   Steps: {max(llama_data['steps']):,}\n"
        stats_text += f"   Loss iniziale: {llama_data['initial_loss']:.4f}\n"
        stats_text += f"   Loss finale: {llama_data['final_loss']:.4f}\n"
        stats_text += f"   Miglioramento: {llama_data['improvement_pct']:.1f}%\n"
        stats_text += f"   Loss media: {llama_data['mean_loss']:.4f}\n\n"
    
    if gemma_data and llama_data:
        stats_text += f"🏆 CONFRONTO:\n"
        if gemma_data['final_loss'] < llama_data['final_loss']:
            diff = ((llama_data['final_loss'] - gemma_data['final_loss']) / llama_data['final_loss']) * 100
            stats_text += f"   GEMMA ha loss finale {diff:.1f}% MIGLIORE\n"
        else:
            diff = ((gemma_data['final_loss'] - llama_data['final_loss']) / gemma_data['final_loss']) * 100
            stats_text += f"   LLAMA ha loss finale {diff:.1f}% MIGLIORE\n"
        
        step_diff = max(llama_data['steps']) - max(gemma_data['steps'])
        stats_text += f"   LLAMA ha {step_diff:,} step in più\n"
    
    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
    
    plt.suptitle('📊 ANALISI LOSS TRAINING - GEMMA T9 vs LLAMA T8', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 Grafico loss salvato: {output_path}")

def main():
    print("📊 ANALISI LOSS MANUALE")
    print("=" * 60)
    
    # Percorsi checkpoint
    gemma_checkpoint = "experiments/xml_direct_input/outputs/gemma_t9_continue/checkpoint-15500"
    llama_checkpoint = "experiments/xml_direct_input/outputs/llama_t8_continue/checkpoint-18750"
    
    # Carica trainer states
    gemma_state = load_trainer_state(gemma_checkpoint)
    llama_state = load_trainer_state(llama_checkpoint)
    
    # Estrai dati loss
    gemma_steps, gemma_losses = extract_loss_data(gemma_state)
    llama_steps, llama_losses = extract_loss_data(llama_state)
    
    # Analizza statistiche
    gemma_data = analyze_loss_stats(gemma_steps, gemma_losses, "GEMMA T9") if gemma_losses else None
    llama_data = analyze_loss_stats(llama_steps, llama_losses, "LLAMA T8") if llama_losses else None
    
    # Crea grafico
    os.makedirs("analysis_results", exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"analysis_results/loss_comparison_{timestamp}.png"
    
    create_loss_comparison_plot(gemma_data, llama_data, output_path)
    
    print("\n" + "=" * 60)
    print("🎯 ANALISI LOSS COMPLETATA")
    print("=" * 60)

if __name__ == "__main__":
    main()
