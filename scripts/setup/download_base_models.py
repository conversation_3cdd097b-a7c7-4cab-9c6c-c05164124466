#!/usr/bin/env python3
"""
🚀 DOWNLOAD MODELLI BASE
Scarica i modelli base necessari per quantizzazione
"""

import os
import sys
import logging
from datetime import datetime
from transformers import AutoModelForCausalLM, AutoTokenizer
from huggingface_hub import login

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def download_model(model_name, output_dir):
    """Scarica un modello e lo salva localmente"""
    
    try:
        logger.info(f"🚀 Download {model_name}...")
        
        # Crea directory output
        model_dir = os.path.join(output_dir, model_name.replace("/", "_"))
        os.makedirs(model_dir, exist_ok=True)
        
        # Download tokenizer
        logger.info("📝 Download tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        tokenizer.save_pretrained(model_dir)
        
        # Download model
        logger.info("🤖 Download modello...")
        model = AutoModelForCausalLM.from_pretrained(
            model_name,
            torch_dtype="auto",
            device_map="cpu",  # Salva su CPU per storage
            trust_remote_code=True
        )
        model.save_pretrained(model_dir)
        
        logger.info(f"✅ {model_name} salvato in: {model_dir}")
        return model_dir
        
    except Exception as e:
        logger.error(f"❌ Errore download {model_name}: {e}")
        return None

def main():
    # Token HuggingFace
    token = "*************************************"
    
    logger.info("🔑 Login HuggingFace...")
    try:
        login(token=token)
        logger.info("✅ Login completato!")
    except Exception as e:
        logger.error(f"❌ Errore login: {e}")
        return
    
    # Directory output
    output_dir = "models/base_models"
    os.makedirs(output_dir, exist_ok=True)
    
    # Modelli da scaricare
    models_to_download = [
        "google/gemma-2-9b-it",
        "meta-llama/Llama-3.1-8B-Instruct"
    ]
    
    logger.info("🚀 DOWNLOAD MODELLI BASE")
    logger.info("=" * 50)
    logger.info(f"📁 Output directory: {output_dir}")
    logger.info(f"🤖 Modelli: {models_to_download}")
    logger.info("=" * 50)
    
    results = {}
    
    for model_name in models_to_download:
        result = download_model(model_name, output_dir)
        results[model_name] = result
    
    logger.info("=" * 50)
    logger.info("✅ DOWNLOAD COMPLETATO!")
    
    # Riepilogo
    successful = sum(1 for r in results.values() if r is not None)
    total = len(results)
    logger.info(f"📊 Successi: {successful}/{total}")
    
    for model_name, path in results.items():
        if path:
            logger.info(f"   ✅ {model_name}: {path}")
        else:
            logger.info(f"   ❌ {model_name}: FALLITO")
    
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
