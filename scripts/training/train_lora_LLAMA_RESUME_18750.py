#!/usr/bin/env python3
"""
🟠 LLAMA-3.1-8B TRAINING RESUME DA CHECKPOINT 18750
Resume training da checkpoint 18750 (checkpoint 19000 era corrotto)
"""

import os
import sys
import torch
import logging
from datetime import datetime
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq
)
from peft import LoraConfig, get_peft_model, TaskType
from datasets import Dataset
import json
import wandb

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    logger.info("🟠 LLAMA-3.1-8B TRAINING RESUME DA CHECKPOINT 18750")
    logger.info("=" * 60)
    
    # Configurazione
    model_name = "meta-llama/Llama-3.1-8B-Instruct"
    resume_from_checkpoint = "experiments/xml_direct_input/outputs/llama_t8_continue/checkpoint-18750"
    output_dir = "experiments/xml_direct_input/outputs/llama_t8_continue"
    
    # Verifica checkpoint
    if not os.path.exists(resume_from_checkpoint):
        logger.error(f"❌ Checkpoint non trovato: {resume_from_checkpoint}")
        sys.exit(1)
    
    # Verifica che il checkpoint sia valido
    required_files = ['adapter_model.safetensors', 'trainer_state.json', 'training_args.bin']
    for file in required_files:
        if not os.path.exists(os.path.join(resume_from_checkpoint, file)):
            logger.error(f"❌ File mancante nel checkpoint: {file}")
            sys.exit(1)
    
    logger.info(f"📂 Resume da: {resume_from_checkpoint}")
    logger.info(f"📁 Output dir: {output_dir}")
    logger.info("✅ Checkpoint validato - tutti i file necessari presenti")
    
    # Setup device
    device_map = "auto"
    if torch.cuda.device_count() > 1:
        logger.info(f"🔥 Usando {torch.cuda.device_count()} GPU")
    
    # Load tokenizer
    logger.info("📝 Caricamento tokenizer...")
    tokenizer = AutoTokenizer.from_pretrained(model_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    # Load model
    logger.info("🤖 Caricamento modello...")
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float16,
        device_map=device_map,
        trust_remote_code=True
    )
    
    # LoRA configuration
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=16,
        lora_alpha=32,
        lora_dropout=0.1,
        target_modules=["q_proj", "v_proj", "k_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # Load dataset
    logger.info("📊 Caricamento dataset...")
    data_file = 'data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json'

    if not os.path.exists(data_file):
        logger.error(f"❌ File dataset non trovato: {data_file}")
        sys.exit(1)

    with open(data_file, 'r') as f:
        all_data = json.load(f)

    # Split in train/test (90% train, 10% test)
    split_idx = int(len(all_data) * 0.9)
    train_data = all_data[:split_idx]
    test_data = all_data[split_idx:]

    logger.info(f"📊 Dataset totale: {len(all_data)} esempi")
    logger.info(f"📊 Train: {len(train_data)} esempi")
    logger.info(f"📊 Test: {len(test_data)} esempi")
    
    # Prepare datasets
    def prepare_dataset(data):
        inputs = []
        targets = []

        for item in data:
            xml_content = item['xml']  # Campo corretto nel dataset
            caption = item['caption']

            # Format input
            input_text = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\nYou are an expert at analyzing SVG images and creating detailed, accurate captions. Generate a comprehensive caption that describes the visual elements, colors, shapes, text, and overall composition of the SVG image.\n\n<|eot_id|><|start_header_id|>user<|end_header_id|>\n\nAnalyze this SVG image and provide a detailed caption:\n\n{xml_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
            target_text = f"{caption}<|eot_id|>"

            inputs.append(input_text)
            targets.append(target_text)

        return Dataset.from_dict({
            'input_text': inputs,
            'target_text': targets
        })
    
    train_dataset = prepare_dataset(train_data)
    eval_dataset = prepare_dataset(test_data[:400])  # Subset per eval
    
    logger.info(f"📊 Train samples: {len(train_dataset)}")
    logger.info(f"📊 Eval samples: {len(eval_dataset)}")
    
    # Tokenization function
    def tokenize_function(examples):
        # Tokenize inputs and targets
        model_inputs = tokenizer(
            examples['input_text'],
            max_length=2048,
            truncation=True,
            padding=False
        )
        
        # Tokenize targets
        with tokenizer.as_target_tokenizer():
            labels = tokenizer(
                examples['target_text'],
                max_length=512,
                truncation=True,
                padding=False
            )
        
        # Combine input and target
        input_ids = []
        attention_masks = []
        labels_list = []
        
        for i in range(len(model_inputs['input_ids'])):
            # Combine input + target
            combined_ids = model_inputs['input_ids'][i] + labels['input_ids'][i]
            combined_attention = model_inputs['attention_mask'][i] + labels['attention_mask'][i]
            
            # Create labels (ignore input part)
            label_ids = [-100] * len(model_inputs['input_ids'][i]) + labels['input_ids'][i]
            
            # Truncate if too long
            max_length = 2048
            if len(combined_ids) > max_length:
                combined_ids = combined_ids[:max_length]
                combined_attention = combined_attention[:max_length]
                label_ids = label_ids[:max_length]
            
            input_ids.append(combined_ids)
            attention_masks.append(combined_attention)
            labels_list.append(label_ids)
        
        return {
            'input_ids': input_ids,
            'attention_mask': attention_masks,
            'labels': labels_list
        }
    
    # Tokenize datasets
    logger.info("🔤 Tokenizzazione dataset...")
    train_dataset = train_dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=train_dataset.column_names
    )
    
    eval_dataset = eval_dataset.map(
        tokenize_function,
        batched=True,
        remove_columns=eval_dataset.column_names
    )
    
    # Data collator
    data_collator = DataCollatorForSeq2Seq(
        tokenizer=tokenizer,
        model=model,
        padding=True,
        return_tensors="pt"
    )
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir=output_dir,
        num_train_epochs=3,
        per_device_train_batch_size=1,
        per_device_eval_batch_size=1,
        gradient_accumulation_steps=8,
        warmup_steps=100,
        learning_rate=5e-5,
        fp16=True,
        logging_steps=50,
        eval_steps=500,
        save_steps=250,
        save_total_limit=2,
        eval_strategy="steps",
        load_best_model_at_end=False,
        metric_for_best_model="eval_loss",
        greater_is_better=False,
        dataloader_num_workers=4,
        remove_unused_columns=False,
        report_to="wandb",
        run_name=f"llama_t8_resume_18750_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        ddp_find_unused_parameters=False,
        gradient_checkpointing=True
    )
    
    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        tokenizer=tokenizer,
        data_collator=data_collator
    )
    
    # Resume training
    logger.info("🚀 Ripresa training...")
    logger.info(f"📂 Resume da checkpoint: {resume_from_checkpoint}")
    logger.info("⚠️ Nota: Checkpoint 19000 era corrotto, riprendendo da 18750")
    
    trainer.train(resume_from_checkpoint=resume_from_checkpoint)
    
    # Save final model
    logger.info("💾 Salvataggio modello finale...")
    trainer.save_model()
    tokenizer.save_pretrained(output_dir)
    
    logger.info("✅ Training completato!")

if __name__ == "__main__":
    main()
