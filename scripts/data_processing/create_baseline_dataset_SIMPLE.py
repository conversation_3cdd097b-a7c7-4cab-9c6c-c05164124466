#!/usr/bin/env python3
"""
🎨 CREAZIONE DATASET BASELINE SEMPLICE E VELOCE
Basato su script che già funzionano
"""

import os
import json
import re
import random
from datetime import datetime
from tqdm import tqdm
import logging
import cairosvg

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def de_parser_correct(svg_data):
    """
    de_parser CORRETTO con gestione RGB e sfondo bianco
    Basato su script che già funzionano - VERSIONE CORRETTA
    """
    res = '<?xml version="1.0" encoding="utf-8"?>\n'
    res += '<svg viewBox="0 0 512 512" width="512" height="512" xmlns="http://www.w3.org/2000/svg">\n'
    res += '<rect width="512" height="512" fill="white" stroke="none"/>\n'

    # Regex per correggere colori RGB
    def replacer(match):
        colors = match.group(0).split(':')[1].split(',')
        if len(colors) == 3:
            r, g, b = [int(c) for c in colors]
            hex_color = f"#{r:02x}{g:02x}{b:02x}"
            return match.group(0).split(':')[0] + ':' + hex_color
        return match.group(0)

    # Parsing corretto
    svg_data = svg_data.replace("style=", "<path style=\"")
    svg_data = re.sub(r"stroke:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = re.sub(r"fill:([0-9]{1,3},?){3}", replacer, svg_data)

    # CORREZIONE: gestisci correttamente il separatore tra style e d
    svg_data = svg_data.replace("\td=", "\" d=\"")  # Corretto: \t seguito da d=
    svg_data = svg_data.replace("\n", "Z\" />\n")

    res += svg_data
    res += "</svg>"

    return res

def svg_to_png_cairosvg(svg_content, output_path, size=512):
    """
    Conversione SVG→PNG con CairoSVG (qualità ottimale)
    Garantisce sfondo bianco e rendering perfetto
    """
    try:
        # Conversione con CairoSVG - parametri ottimizzati
        cairosvg.svg2png(
            bytestring=svg_content.encode('utf-8'),
            write_to=output_path,
            output_width=size,
            output_height=size,
            background_color='white',  # Sfondo bianco garantito
            dpi=150  # DPI alta per qualità ottimale
        )
        
        return os.path.exists(output_path)
        
    except Exception as e:
        logger.warning(f"❌ Errore CairoSVG: {e}")
        return False

def create_baseline_dataset_simple(source_dataset, output_dir, num_examples=400, size=512):
    """
    Crea dataset baseline SEMPLICE e VELOCE
    Basato su script che già funzionano
    """
    logger.info(f"🎨 CREAZIONE DATASET BASELINE SEMPLICE")
    logger.info(f"📊 Sorgente: {source_dataset}")
    logger.info(f"📁 Output: {output_dir}")
    logger.info(f"🖼️ Esempi: {num_examples}")
    logger.info(f"📐 Dimensione: {size}x{size}")
    
    # Carica dataset sorgente
    with open(source_dataset, 'r') as f:
        source_data = json.load(f)
    
    logger.info(f"📊 Dataset sorgente caricato: {len(source_data)} esempi")
    
    # Seleziona esempi casuali ma riproducibili
    random.seed(42)  # Seed fisso per riproducibilità
    if len(source_data) > num_examples:
        selected_indices = random.sample(range(len(source_data)), num_examples)
        selected_data = [source_data[i] for i in selected_indices]
    else:
        selected_indices = list(range(len(source_data)))
        selected_data = source_data
    
    logger.info(f"📊 Selezionati {len(selected_data)} esempi")
    
    # Crea directory
    os.makedirs(output_dir, exist_ok=True)
    images_dir = os.path.join(output_dir, "images")
    os.makedirs(images_dir, exist_ok=True)
    
    # Processa esempi
    baseline_dataset = []
    successful = 0
    failed = 0
    
    for i, example in enumerate(tqdm(selected_data, desc="🎨 Creando baseline dataset")):
        try:
            # Ottieni contenuto XML (il dataset usa 'xml' come chiave)
            xml_content = example.get('xml', '') or example.get('xml_content', '')
            caption = example.get('caption', '')
            
            if not xml_content or not caption:
                logger.warning(f"⚠️ Esempio {i} mancante xml/caption")
                failed += 1
                continue
            
            # Converte XML in SVG con colori corretti e sfondo bianco
            svg_content = de_parser_correct(xml_content)
            
            # Path PNG
            png_filename = f"baseline_{i:04d}.png"
            png_path = os.path.join(images_dir, png_filename)
            
            # Conversione SVG→PNG
            success = svg_to_png_cairosvg(svg_content, png_path, size)
            
            if success:
                # Crea entry per baseline dataset
                baseline_entry = {
                    'id': i,
                    'original_id': selected_indices[i] if i < len(selected_indices) else i,
                    'image_path': png_path,
                    'filename': png_filename,
                    'caption': caption,
                    'xml_content': xml_content,
                    'svg_content': svg_content,
                    'colors_fixed': True,
                    'white_background': True,
                    'size': size
                }
                
                baseline_dataset.append(baseline_entry)
                successful += 1
                
                if (i + 1) % 50 == 0:
                    logger.info(f"   ✅ Processati {i+1}/{len(selected_data)} esempi")
            else:
                failed += 1
                logger.warning(f"   ❌ Fallito esempio {i}")
                
        except Exception as e:
            failed += 1
            logger.error(f"   ❌ Errore esempio {i}: {e}")
    
    # Salva dataset baseline
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    baseline_file = os.path.join(output_dir, f"baseline_dataset_SIMPLE_{timestamp}.json")
    
    with open(baseline_file, 'w') as f:
        json.dump(baseline_dataset, f, indent=2)
    
    # Crea anche versione semplificata per valutazione
    simple_baseline = []
    for entry in baseline_dataset:
        simple_entry = {
            'image_path': entry['image_path'],
            'caption': entry['caption'],
            'xml_content': entry['xml_content']
        }
        simple_baseline.append(simple_entry)
    
    simple_file = os.path.join(output_dir, f"baseline_simple_{timestamp}.json")
    with open(simple_file, 'w') as f:
        json.dump(simple_baseline, f, indent=2)
    
    # Statistiche finali
    logger.info("=" * 80)
    logger.info("🎉 DATASET BASELINE SEMPLICE COMPLETATO!")
    logger.info("=" * 80)
    logger.info(f"✅ Successi: {successful}/{len(selected_data)} ({successful/len(selected_data)*100:.1f}%)")
    logger.info(f"❌ Fallimenti: {failed}/{len(selected_data)} ({failed/len(selected_data)*100:.1f}%)")
    logger.info(f"📁 Dataset completo: {baseline_file}")
    logger.info(f"📁 Dataset semplice: {simple_file}")
    logger.info(f"🖼️ Immagini: {images_dir}")
    logger.info("=" * 80)
    
    return baseline_file, simple_file

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Crea dataset baseline semplice")
    parser.add_argument("--source", required=True, help="Dataset sorgente JSON")
    parser.add_argument("--output", required=True, help="Directory output")
    parser.add_argument("--num_examples", type=int, default=400, help="Numero esempi")
    parser.add_argument("--size", type=int, default=512, help="Dimensione immagini")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.source):
        logger.error(f"❌ Dataset sorgente non trovato: {args.source}")
        return
    
    # Crea dataset baseline semplice
    baseline_file, simple_file = create_baseline_dataset_simple(
        args.source,
        args.output,
        args.num_examples,
        args.size
    )
    
    logger.info("🎉 CREAZIONE DATASET BASELINE SEMPLICE COMPLETATA!")

if __name__ == "__main__":
    main()
