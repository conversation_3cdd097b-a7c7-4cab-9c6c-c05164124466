#!/usr/bin/env python3
"""
🎯 EVALUATION CHECKPOINT TRAINED MODELS
Inference e evaluation dei checkpoint Gemma e Llama più recenti
"""

import os
import json
import argparse
import logging
import torch
from datetime import datetime
from PIL import Image
from transformers import (
    AutoTokenizer, AutoModelForCausalLM,
    BitsAndBytesConfig
)
from peft import PeftModel
import re

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def replacer(match):
    """Converte colori da formato numerico a RGB"""
    numbers = re.findall(r'\d+', match.group())
    if len(numbers) >= 3:
        return f"{match.group().split(':')[0]}:rgb({numbers[0]},{numbers[1]},{numbers[2]})"
    return match.group()

def de_parser_correct(svg_data):
    """de_parser CORRETTO con gestione RGB e sfondo bianco"""
    res = '<?xml version="1.0" encoding="utf-8"?>\n'
    res += '<svg viewBox="0 0 512 512" width="512" height="512" xmlns="http://www.w3.org/2000/svg">\n'
    res += '<rect width="512" height="512" fill="white" stroke="none"/>\n'

    svg_data = svg_data.replace("style=", "<path style=\"")
    svg_data = re.sub(r"stroke:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = re.sub(r"fill:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = svg_data.replace("\t", "\" d=\"")
    svg_data = svg_data.replace("\n", "Z\" />\n")

    res += svg_data
    res += "</svg>"
    return res

def svg_to_png_cairosvg(svg_content, output_path, size=512):
    """Conversione SVG→PNG con CairoSVG"""
    try:
        import cairosvg
        cairosvg.svg2png(
            bytestring=svg_content.encode('utf-8'),
            write_to=output_path,
            output_width=size,
            output_height=size,
            background_color='white',
            dpi=150
        )
        return os.path.exists(output_path)
    except Exception as e:
        logger.error(f"❌ Errore CairoSVG: {e}")
        return False

def load_trained_model(checkpoint_path, model_name):
    """Carica modello trained con LoRA adapter"""
    logger.info(f"🔄 Caricamento {model_name} da {checkpoint_path}...")

    # Determina base model
    if "gemma" in model_name.lower():
        base_model_name = "google/gemma-2-9b-it"
    elif "llama" in model_name.lower():
        base_model_name = "meta-llama/Llama-3.1-8B-Instruct"
    else:
        raise ValueError(f"Modello non riconosciuto: {model_name}")

    # Configurazione quantizzazione
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
        bnb_4bit_use_double_quant=True,
    )

    # Carica tokenizer
    tokenizer = AutoTokenizer.from_pretrained(base_model_name)
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token

    # Carica base model
    base_model = AutoModelForCausalLM.from_pretrained(
        base_model_name,
        quantization_config=bnb_config,
        device_map="auto",
        torch_dtype=torch.float16,
        trust_remote_code=True
    )

    # Carica LoRA adapter
    model = PeftModel.from_pretrained(base_model, checkpoint_path)
    model.eval()

    return tokenizer, model

def generate_caption(tokenizer, model, xml_content, model_name):
    """Genera caption da XML content"""
    try:
        # Prepara prompt
        if "gemma" in model_name.lower():
            prompt = f"<start_of_turn>user\nDescribe this SVG image:\n{xml_content}<end_of_turn>\n<start_of_turn>model\n"
        elif "llama" in model_name.lower():
            prompt = f"<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n\nDescribe this SVG image:\n{xml_content}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"
        else:
            prompt = f"Describe this SVG image:\n{xml_content}\n\nDescription:"

        # Tokenize
        inputs = tokenizer(
            prompt,
            return_tensors="pt",
            truncation=True,
            max_length=2048,
            padding=True
        )

        inputs = {k: v.to(model.device) for k, v in inputs.items()}

        # Generate
        with torch.no_grad():
            generated_ids = model.generate(
                **inputs,
                max_new_tokens=200,
                num_beams=3,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                repetition_penalty=1.1,
                pad_token_id=tokenizer.eos_token_id,
                early_stopping=True
            )

        # Decode
        generated_text = tokenizer.decode(generated_ids[0], skip_special_tokens=True)

        # Estrai solo la risposta
        if "gemma" in model_name.lower():
            if "<start_of_turn>model" in generated_text:
                caption = generated_text.split("<start_of_turn>model")[-1].strip()
            else:
                caption = generated_text[len(prompt):].strip()
        elif "llama" in model_name.lower():
            if "<|start_header_id|>assistant<|end_header_id|>" in generated_text:
                caption = generated_text.split("<|start_header_id|>assistant<|end_header_id|>")[-1].strip()
            else:
                caption = generated_text[len(prompt):].strip()
        else:
            caption = generated_text[len(prompt):].strip()

        # Pulizia finale
        caption = caption.replace("<|eot_id|>", "").replace("<end_of_turn>", "").strip()

        return caption

    except Exception as e:
        logger.error(f"❌ Errore generazione: {e}")
        return ""

def evaluate_checkpoint(checkpoint_path, model_name, dataset_file, output_dir, max_examples=100):
    """Evalua un checkpoint su dataset"""
    logger.info(f"🎯 EVALUATION {model_name.upper()}")
    logger.info(f"📁 Checkpoint: {checkpoint_path}")
    logger.info(f"📊 Dataset: {dataset_file}")

    # Carica dataset
    with open(dataset_file, 'r') as f:
        dataset = json.load(f)

    # Limita esempi se richiesto
    if max_examples and len(dataset) > max_examples:
        dataset = dataset[:max_examples]
        logger.info(f"📊 Limitato a {max_examples} esempi")

    # Carica modello
    tokenizer, model = load_trained_model(checkpoint_path, model_name)

    # Risultati
    results = {
        "model": model_name.upper(),
        "checkpoint": os.path.basename(checkpoint_path),
        "dataset": os.path.basename(dataset_file),
        "timestamp": datetime.now().strftime("%Y%m%d_%H%M%S"),
        "total_examples": len(dataset),
        "successful_examples": 0,
        "results": []
    }

    # Elabora esempi
    for i, example in enumerate(dataset):
        logger.info(f"📈 [{i+1}/{len(dataset)}] Processando esempio {i}...")

        try:
            xml_content = example.get('xml', '')
            ground_truth = example.get('caption', '')

            # Genera caption
            generated_caption = generate_caption(tokenizer, model, xml_content, model_name)

            # Salva risultato
            result = {
                "example_id": i,
                "xml_content": xml_content,
                "ground_truth": ground_truth,
                "generated_caption": generated_caption,
                "image_path": example.get("image_path", "")
            }

            results["results"].append(result)
            results["successful_examples"] += 1

        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")

    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    checkpoint_name = os.path.basename(checkpoint_path)
    output_filename = f"{model_name}_{checkpoint_name}_results_{timestamp}.json"
    output_path = os.path.join(output_dir, output_filename)

    os.makedirs(output_dir, exist_ok=True)
    with open(output_path, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"✅ Evaluation completata!")
    logger.info(f"📊 Risultati salvati: {output_path}")
    logger.info(f"📊 Esempi processati: {results['successful_examples']}/{len(dataset)}")

    return output_path

def main():
    parser = argparse.ArgumentParser(description="Evaluation checkpoint trained models")
    parser.add_argument("--checkpoint", required=True,
                       help="Path al checkpoint da valutare")
    parser.add_argument("--model_name", required=True,
                       choices=["gemma_t9", "llama_t8"],
                       help="Nome del modello")
    parser.add_argument("--dataset",
                       default="data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json",
                       help="Dataset per evaluation")
    parser.add_argument("--output_dir",
                       default="evaluation_results/trained_models",
                       help="Directory di output")
    parser.add_argument("--max_examples", type=int, default=100,
                       help="Numero massimo di esempi da processare")

    args = parser.parse_args()

    print(f"🎯 EVALUATION CHECKPOINT TRAINED")
    print("=" * 50)
    print(f"🤖 Modello: {args.model_name.upper()}")
    print(f"📁 Checkpoint: {args.checkpoint}")
    print(f"📊 Dataset: {os.path.basename(args.dataset)}")
    print(f"📈 Max esempi: {args.max_examples}")
    print("=" * 50)

    if not os.path.exists(args.checkpoint):
        print(f"❌ Checkpoint non trovato: {args.checkpoint}")
        return

    if not os.path.exists(args.dataset):
        print(f"❌ Dataset non trovato: {args.dataset}")
        return

    # Esegui evaluation
    output_path = evaluate_checkpoint(
        args.checkpoint,
        args.model_name,
        args.dataset,
        args.output_dir,
        args.max_examples
    )

    print("=" * 50)
    print("🎉 EVALUATION COMPLETATA!")
    print("=" * 50)
    print(f"📄 Risultati: {os.path.basename(output_path)}")
    print(f"📁 Directory: {args.output_dir}")
    print("")
    print("🎯 PROSSIMI PASSI:")
    print("1. Calcola metriche con: scripts/evaluation/calculate_metrics_CORRECT.py")
    print("2. Confronta con baseline")
    print("3. Genera radar chart")

if __name__ == "__main__":
    main()