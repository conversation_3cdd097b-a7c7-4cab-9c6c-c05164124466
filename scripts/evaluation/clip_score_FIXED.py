#!/usr/bin/env python3
"""
🔧 CLIP SCORE FIXED
Versione che installa dipendenze e fixa CairoSVG con timeout
"""

import os
import json
import argparse
import logging
import re
import tempfile
import subprocess
import threading
import time
from datetime import datetime
from PIL import Image
import torch
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_dependencies():
    """Installa dipendenze mancanti"""
    logger.info("🔧 Installazione dipendenze...")
    
    try:
        # Prova a installare librsvg-tools (contiene rsvg-convert)
        subprocess.run(['sudo', 'apt-get', 'update'], check=False, capture_output=True)
        subprocess.run(['sudo', 'apt-get', 'install', '-y', 'librsvg2-bin'], check=False, capture_output=True)
        logger.info("✅ librsvg2-bin installato")
    except:
        logger.warning("❌ Non riesco a installare librsvg2-bin")
    
    try:
        # Prova a installare inkscape
        subprocess.run(['sudo', 'apt-get', 'install', '-y', 'inkscape'], check=False, capture_output=True)
        logger.info("✅ inkscape installato")
    except:
        logger.warning("❌ Non riesco a installare inkscape")

def replacer(match):
    """Converte colori da formato numerico a RGB"""
    numbers = re.findall(r'\d+', match.group())
    if len(numbers) >= 3:
        return f"{match.group().split(':')[0]}:rgb({numbers[0]},{numbers[1]},{numbers[2]})"
    return match.group()

def de_parser_correct(svg_data):
    """de_parser CORRETTO con gestione RGB e sfondo bianco"""
    res = '<?xml version="1.0" encoding="utf-8"?>\n'
    res += '<svg viewBox="0 0 512 512" width="512" height="512" xmlns="http://www.w3.org/2000/svg">\n'
    res += '<rect width="512" height="512" fill="white" stroke="none"/>\n'
    
    svg_data = svg_data.replace("style=", "<path style=\"")
    svg_data = re.sub(r"stroke:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = re.sub(r"fill:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = svg_data.replace("\t", "\" d=\"")
    svg_data = svg_data.replace("\n", "Z\" />\n")
    
    res += svg_data
    res += "</svg>"
    return res

class TimeoutError(Exception):
    pass

def timeout_function(func, args, timeout):
    """Esegue funzione con timeout usando threading"""
    result = [None]
    exception = [None]
    
    def target():
        try:
            result[0] = func(*args)
        except Exception as e:
            exception[0] = e
    
    thread = threading.Thread(target=target)
    thread.daemon = True
    thread.start()
    thread.join(timeout)
    
    if thread.is_alive():
        # Thread ancora attivo = timeout
        raise TimeoutError(f"Funzione timeout dopo {timeout}s")
    
    if exception[0]:
        raise exception[0]
    
    return result[0]

def svg_to_png_cairosvg_fixed(svg_content, output_path, size=224, timeout_sec=10):
    """CairoSVG con timeout FORZATO usando threading"""
    def _convert():
        import cairosvg
        cairosvg.svg2png(
            bytestring=svg_content.encode('utf-8'),
            write_to=output_path,
            output_width=size,
            output_height=size,
            background_color='white',
            dpi=72
        )
        return os.path.exists(output_path)
    
    try:
        result = timeout_function(_convert, (), timeout_sec)
        if result:
            logger.debug(f"✅ cairosvg: {output_path}")
            return True
        return False
    except TimeoutError:
        logger.debug(f"⏰ cairosvg timeout ({timeout_sec}s)")
        return False
    except Exception as e:
        logger.debug(f"❌ cairosvg error: {e}")
        return False

def svg_to_png_subprocess(svg_content, output_path, size=224):
    """Conversione con subprocess (rsvg-convert o inkscape)"""
    # Salva SVG temporaneo
    with tempfile.NamedTemporaryFile(mode='w', suffix='.svg', delete=False) as f:
        f.write(svg_content)
        svg_temp = f.name
    
    try:
        # Prova rsvg-convert
        cmd = ['rsvg-convert', '--width', str(size), '--height', str(size), 
               '--background-color', 'white', '--output', output_path, svg_temp]
        
        result = subprocess.run(cmd, timeout=20, capture_output=True)
        if result.returncode == 0 and os.path.exists(output_path):
            logger.debug(f"✅ rsvg-convert: {output_path}")
            os.unlink(svg_temp)
            return True
    except:
        pass
    
    try:
        # Prova inkscape
        cmd = ['inkscape', '--export-type=png', f'--export-width={size}', 
               f'--export-height={size}', f'--export-filename={output_path}', svg_temp]
        
        result = subprocess.run(cmd, timeout=20, capture_output=True)
        if result.returncode == 0 and os.path.exists(output_path):
            logger.debug(f"✅ inkscape: {output_path}")
            os.unlink(svg_temp)
            return True
    except:
        pass
    
    # Pulizia
    try:
        os.unlink(svg_temp)
    except:
        pass
    
    return False

def svg_to_png_FIXED(svg_content, output_path, size=224):
    """Conversione SVG→PNG FIXED con tutti i metodi"""
    
    # Metodo 1: Subprocess (rsvg-convert/inkscape)
    if svg_to_png_subprocess(svg_content, output_path, size):
        return True
    
    # Metodo 2: CairoSVG con timeout forzato
    if svg_to_png_cairosvg_fixed(svg_content, output_path, size, 10):
        return True
    
    logger.error(f"❌ Tutti i metodi falliti per {output_path}")
    return False

def setup_clip_transformers():
    """Setup CLIP usando transformers"""
    try:
        from transformers import CLIPProcessor, CLIPModel
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"🔧 CLIP su device: {device}")
        
        model_name = "openai/clip-vit-base-patch32"
        model = CLIPModel.from_pretrained(model_name).to(device)
        processor = CLIPProcessor.from_pretrained(model_name)
        
        return model, processor, device
    except Exception as e:
        logger.error(f"❌ Errore setup CLIP: {e}")
        return None, None, None

def calculate_clip_score_real(image_path, text, model, processor, device):
    """Calcola vero CLIP Score"""
    try:
        image = Image.open(image_path).convert('RGB')
        
        inputs = processor(text=[text], images=image, return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = model(**inputs)
            logits_per_image = outputs.logits_per_image
            clip_score = torch.diagonal(logits_per_image).cpu().numpy()[0]
            normalized_score = 1 / (1 + np.exp(-clip_score))
            
            return float(normalized_score)
            
    except Exception as e:
        logger.warning(f"Errore CLIP Score: {e}")
        return 0.0

def calculate_clip_scores_FIXED(results_file, model_name, max_examples=50):
    """Calcola CLIP Scores FIXED"""
    logger.info(f"🔧 Calcolo CLIP Scores FIXED per {model_name} (max {max_examples} esempi)")
    
    # Installa dipendenze
    install_dependencies()
    
    # Carica risultati
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    results = data.get('results', [])
    
    if len(results) > max_examples:
        results = results[:max_examples]
        logger.info(f"📊 Limitato a {max_examples} esempi")
    
    # Setup CLIP
    model, processor, device = setup_clip_transformers()
    if model is None:
        return []
    
    # Directory temporanea
    temp_dir = tempfile.mkdtemp(prefix="clip_fixed_")
    
    clip_scores = []
    successful = 0
    
    for i, result in enumerate(results):
        logger.info(f"📈 Processando esempio {i+1}/{len(results)}...")
        
        try:
            xml_content = result.get('xml_content', '') or result.get('xml', '')
            generated_caption = result.get('generated_caption', '')
            
            if not xml_content or not generated_caption:
                clip_scores.append(0.0)
                continue
            
            # Converte XML in SVG
            svg_content = de_parser_correct(xml_content)
            
            # Genera immagine PNG con metodo FIXED
            img_path = os.path.join(temp_dir, f"fixed_{i}.png")
            
            if svg_to_png_FIXED(svg_content, img_path, size=224):
                # Calcola CLIP Score
                score = calculate_clip_score_real(img_path, generated_caption, model, processor, device)
                clip_scores.append(score)
                successful += 1
                
                logger.info(f"✅ Esempio {i}: CLIP Score = {score:.4f}")
                
                # Rimuovi immagine
                try:
                    os.remove(img_path)
                except:
                    pass
            else:
                clip_scores.append(0.0)
                logger.warning(f"❌ Esempio {i}: Conversione fallita")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            clip_scores.append(0.0)
    
    # Pulizia
    try:
        import shutil
        shutil.rmtree(temp_dir)
    except:
        pass
    
    logger.info(f"✅ CLIP Scores FIXED calcolati: {successful}/{len(results)} successi")
    
    return clip_scores

def main():
    parser = argparse.ArgumentParser(description="Calcola CLIP Score FIXED")
    parser.add_argument("--results_file", required=True, help="File JSON risultati")
    parser.add_argument("--model_name", required=True, help="Nome modello")
    parser.add_argument("--max_examples", type=int, default=50, help="Max esempi")
    parser.add_argument("--output_dir", default="evaluation_results/trained_models", help="Directory output")
    
    args = parser.parse_args()
    
    print(f"🔧 CLIP SCORE FIXED")
    print("=" * 50)
    print(f"🤖 Modello: {args.model_name}")
    print(f"📁 File: {os.path.basename(args.results_file)}")
    print(f"📊 Max esempi: {args.max_examples}")
    print(f"🛠️ Metodi: subprocess + cairosvg timeout")
    print("=" * 50)
    
    # Calcola CLIP Scores FIXED
    clip_scores = calculate_clip_scores_FIXED(
        args.results_file,
        args.model_name,
        args.max_examples
    )
    
    if not clip_scores:
        print("❌ Nessun CLIP Score calcolato")
        return
    
    # Calcola statistiche
    valid_scores = [s for s in clip_scores if s > 0]
    
    if valid_scores:
        clip_stats = {
            'mean': float(np.mean(valid_scores)),
            'std': float(np.std(valid_scores)),
            'min': float(np.min(valid_scores)),
            'max': float(np.max(valid_scores)),
            'valid_count': len(valid_scores),
            'total_count': len(clip_scores)
        }
    else:
        clip_stats = {
            'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0,
            'valid_count': 0, 'total_count': len(clip_scores)
        }
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(args.output_dir, f"{args.model_name}_FIXED_clip_{timestamp}.json")
    
    results = {
        'model': args.model_name,
        'timestamp': timestamp,
        'fixed_clip_scores': clip_scores,
        'statistics': clip_stats,
        'examples_processed': len(clip_scores),
        'method': 'fixed_with_dependencies_and_timeout'
    }
    
    os.makedirs(args.output_dir, exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Stampa risultati
    print("=" * 50)
    print("🎉 CLIP SCORES FIXED CALCOLATI!")
    print("=" * 50)
    print(f"📊 CLIP Score medio: {clip_stats['mean']:.4f} (±{clip_stats['std']:.4f})")
    print(f"📊 Range: {clip_stats['min']:.4f} - {clip_stats['max']:.4f}")
    print(f"📊 Scores validi: {clip_stats['valid_count']}/{clip_stats['total_count']}")
    print(f"📄 Risultati: {os.path.basename(output_file)}")
    print("=" * 50)
    print("✅ CLIP Score FIXED con installazione dipendenze e timeout!")

if __name__ == "__main__":
    main()
