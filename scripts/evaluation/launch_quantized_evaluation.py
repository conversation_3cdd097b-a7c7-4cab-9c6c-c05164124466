#!/usr/bin/env python3
"""
🚀 LAUNCHER PER EVALUATION MODELLI QUANTIZZATI
Lancia automaticamente le valutazioni quando i modelli sono pronti
"""

import os
import subprocess
import time
import json
import logging
from datetime import datetime
import argparse

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_model_ready(checkpoint_dir, min_steps=1000):
    """Controlla se un modello è pronto per l'evaluation"""
    if not os.path.exists(checkpoint_dir):
        return False, "Directory non trovata"
    
    # Cerca checkpoint
    checkpoints = []
    for item in os.listdir(checkpoint_dir):
        if item.startswith('checkpoint-') and os.path.isdir(os.path.join(checkpoint_dir, item)):
            try:
                step = int(item.split('-')[1])
                checkpoints.append((step, item))
            except:
                continue
    
    if not checkpoints:
        return False, "Nessun checkpoint trovato"
    
    # Prendi il checkpoint più recente
    latest_step, latest_checkpoint = max(checkpoints)
    
    if latest_step < min_steps:
        return False, f"Checkpoint troppo recente: {latest_step} < {min_steps}"
    
    # Controlla se il checkpoint è completo
    checkpoint_path = os.path.join(checkpoint_dir, latest_checkpoint)
    required_files = ['adapter_config.json', 'adapter_model.safetensors']
    
    for file in required_files:
        if not os.path.exists(os.path.join(checkpoint_path, file)):
            return False, f"File mancante: {file}"
    
    return True, latest_checkpoint

def submit_slurm_job(script_path):
    """Sottomette un job SLURM"""
    try:
        result = subprocess.run(['sbatch', script_path], capture_output=True, text=True)
        if result.returncode == 0:
            # Estrai job ID
            job_id = result.stdout.strip().split()[-1]
            logger.info(f"✅ Job sottomesso: {job_id}")
            return job_id
        else:
            logger.error(f"❌ Errore sottomissione: {result.stderr}")
            return None
    except Exception as e:
        logger.error(f"❌ Errore sbatch: {e}")
        return None

def check_job_status(job_id):
    """Controlla lo stato di un job SLURM"""
    try:
        result = subprocess.run(['squeue', '-j', job_id], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            if len(lines) > 1:  # Header + job line
                return "RUNNING"
            else:
                return "COMPLETED"
        else:
            return "UNKNOWN"
    except:
        return "UNKNOWN"

def wait_for_completion(job_ids, max_wait_hours=12):
    """Aspetta il completamento dei job"""
    logger.info(f"⏳ Aspettando completamento job: {job_ids}")
    
    start_time = time.time()
    max_wait_seconds = max_wait_hours * 3600
    
    while time.time() - start_time < max_wait_seconds:
        all_completed = True
        
        for job_id in job_ids:
            status = check_job_status(job_id)
            if status == "RUNNING":
                all_completed = False
                break
        
        if all_completed:
            logger.info("✅ Tutti i job completati")
            return True
        
        logger.info(f"🔄 Job ancora in esecuzione... ({(time.time() - start_time)/3600:.1f}h)")
        time.sleep(300)  # Controlla ogni 5 minuti
    
    logger.warning(f"⏰ Timeout dopo {max_wait_hours}h")
    return False

def create_final_report(output_dir):
    """Crea report finale con tutti i risultati"""
    logger.info("📋 Creazione report finale...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = os.path.join(output_dir, f"quantized_evaluation_final_report_{timestamp}.json")
    
    # Cerca tutti i file risultati
    results_files = []
    if os.path.exists(output_dir):
        for file in os.listdir(output_dir):
            if file.endswith('_quantized_results_*.json'):
                results_files.append(os.path.join(output_dir, file))
    
    report_data = {
        'timestamp': timestamp,
        'evaluation_type': 'quantized_models',
        'models_evaluated': len(results_files),
        'results_files': results_files,
        'status': 'completed' if results_files else 'failed'
    }
    
    # Aggiungi summary per ogni modello
    for results_file in results_files:
        try:
            with open(results_file, 'r') as f:
                data = json.load(f)
            
            model_name = data.get('model', 'Unknown')
            report_data[f'{model_name}_summary'] = {
                'total_examples': data.get('total_examples', 0),
                'successful_examples': data.get('successful_examples', 0),
                'success_rate': data.get('successful_examples', 0) / max(data.get('total_examples', 1), 1),
                'quantization': data.get('quantization', 'unknown')
            }
        except Exception as e:
            logger.warning(f"⚠️ Errore lettura {results_file}: {e}")
    
    # Salva report
    os.makedirs(output_dir, exist_ok=True)
    with open(report_file, 'w') as f:
        json.dump(report_data, f, indent=2)
    
    logger.info(f"✅ Report finale: {report_file}")
    return report_file

def main():
    parser = argparse.ArgumentParser(description="Launcher evaluation modelli quantizzati")
    parser.add_argument("--gemma_dir", default="experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized", help="Directory Gemma quantizzato")
    parser.add_argument("--llama_dir", default="experiments/xml_direct_input/outputs/llama_t8_scratch_quantized", help="Directory Llama quantizzato")
    parser.add_argument("--output_dir", default="evaluation_results/quantized_models", help="Directory output")
    parser.add_argument("--min_steps", type=int, default=1000, help="Minimo steps per considerare pronto")
    parser.add_argument("--check_interval", type=int, default=1800, help="Intervallo controllo (secondi)")
    parser.add_argument("--max_wait_hours", type=int, default=24, help="Max ore di attesa")
    parser.add_argument("--auto_launch", action="store_true", help="Lancia automaticamente quando pronti")
    
    args = parser.parse_args()
    
    logger.info("🚀 LAUNCHER EVALUATION MODELLI QUANTIZZATI")
    logger.info("=" * 60)
    logger.info(f"📁 Gemma dir: {args.gemma_dir}")
    logger.info(f"📁 Llama dir: {args.llama_dir}")
    logger.info(f"📊 Min steps: {args.min_steps}")
    logger.info(f"⏱️ Check interval: {args.check_interval}s")
    logger.info("=" * 60)
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    submitted_jobs = []
    
    if args.auto_launch:
        logger.info("🔄 Modalità auto-launch attivata")
        
        start_time = time.time()
        max_wait_seconds = args.max_wait_hours * 3600
        
        while time.time() - start_time < max_wait_seconds:
            # Controlla Gemma
            gemma_ready, gemma_status = check_model_ready(args.gemma_dir, args.min_steps)
            logger.info(f"🟢 Gemma: {'PRONTO' if gemma_ready else 'NON PRONTO'} - {gemma_status}")
            
            # Controlla Llama
            llama_ready, llama_status = check_model_ready(args.llama_dir, args.min_steps)
            logger.info(f"🟠 Llama: {'PRONTO' if llama_ready else 'NON PRONTO'} - {llama_status}")
            
            # Lancia job se pronti
            if gemma_ready and 'GEMMA_QUANT_EVAL' not in [j[1] for j in submitted_jobs]:
                job_id = submit_slurm_job('scripts/slurm/evaluate_gemma_quantized.slurm')
                if job_id:
                    submitted_jobs.append((job_id, 'GEMMA_QUANT_EVAL'))
            
            if llama_ready and 'LLAMA_QUANT_EVAL' not in [j[1] for j in submitted_jobs]:
                job_id = submit_slurm_job('scripts/slurm/evaluate_llama_quantized.slurm')
                if job_id:
                    submitted_jobs.append((job_id, 'LLAMA_QUANT_EVAL'))
            
            # Se entrambi lanciati, aspetta completamento
            if len(submitted_jobs) >= 2:
                logger.info("🎯 Entrambi i job lanciati, aspettando completamento...")
                job_ids = [j[0] for j in submitted_jobs]
                if wait_for_completion(job_ids, args.max_wait_hours):
                    break
                else:
                    logger.warning("⏰ Timeout attesa job")
                    break
            
            # Aspetta prima del prossimo controllo
            if len(submitted_jobs) < 2:
                logger.info(f"⏳ Prossimo controllo in {args.check_interval}s...")
                time.sleep(args.check_interval)
    else:
        # Modalità check-only
        gemma_ready, gemma_status = check_model_ready(args.gemma_dir, args.min_steps)
        llama_ready, llama_status = check_model_ready(args.llama_dir, args.min_steps)
        
        logger.info(f"🟢 Gemma: {'PRONTO' if gemma_ready else 'NON PRONTO'} - {gemma_status}")
        logger.info(f"🟠 Llama: {'PRONTO' if llama_ready else 'NON PRONTO'} - {llama_status}")
        
        if gemma_ready or llama_ready:
            logger.info("💡 Per lanciare automaticamente: --auto_launch")
    
    # Crea report finale
    final_report = create_final_report(args.output_dir)
    
    logger.info("=" * 60)
    logger.info("🎉 LAUNCHER COMPLETATO!")
    logger.info(f"📋 Report: {final_report}")
    logger.info(f"🎯 Job lanciati: {len(submitted_jobs)}")
    logger.info("=" * 60)
    
    return 0

if __name__ == "__main__":
    exit(main())
