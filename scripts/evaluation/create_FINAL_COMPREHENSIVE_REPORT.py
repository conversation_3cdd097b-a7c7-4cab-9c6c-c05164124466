#!/usr/bin/env python3
"""
🏆 REPORT FINALE COMPREHENSIVE
Tutti i modelli: Florence-2, Idefics3, BLIP-2, Gemma-T9 con REAL CLIP
"""

import os
import sys
import json
import argparse
import logging
from datetime import datetime
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_final_html_report(all_results, output_file):
    """Crea report HTML finale con tutti i modelli"""
    
    # Ordina per CLIPScore
    sorted_results = sorted(all_results.items(), 
                          key=lambda x: x[1]['metrics']['clip_score']['mean'], 
                          reverse=True)
    
    html_content = f"""
<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏆 REPORT FINALE COMPREHENSIVE - SVG Captioning</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            color: white;
            padding: 40px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            font-weight: bold;
        }}
        .header p {{
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }}
        .content {{
            padding: 40px;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }}
        .models-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .model-card {{
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
        }}
        .model-card:hover {{
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }}
        .model-card.winner {{
            border-color: #f39c12;
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        }}
        .model-card.second {{
            border-color: #95a5a6;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        }}
        .model-card.third {{
            border-color: #e67e22;
            background: linear-gradient(135deg, #fdf2e9, #fdebd0);
        }}
        .model-name {{
            font-size: 1.4em;
            font-weight: bold;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }}
        .medal {{
            font-size: 1.5em;
            margin-right: 10px;
        }}
        .metric {{
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }}
        .metric-name {{
            font-weight: 500;
        }}
        .metric-value {{
            font-weight: bold;
            color: #2980b9;
        }}
        .chart-container {{
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }}
        .summary-stats {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }}
        .stat-card {{
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }}
        .stat-number {{
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }}
        .stat-label {{
            font-size: 0.9em;
            opacity: 0.9;
        }}
        .footer {{
            background: #2c3e50;
            color: white;
            padding: 20px;
            text-align: center;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }}
        th, td {{
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background: #3498db;
            color: white;
            font-weight: bold;
        }}
        tr:hover {{
            background: #f5f5f5;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 REPORT FINALE COMPREHENSIVE</h1>
            <p>Valutazione Completa Modelli SVG Captioning con REAL CLIP</p>
            <p>Generato il {datetime.now().strftime('%d/%m/%Y alle %H:%M')}</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 Classifica Finale</h2>
                <div class="models-grid">
"""
    
    # Aggiungi card per ogni modello
    medals = ["🥇", "🥈", "🥉", "🏅"]
    card_classes = ["winner", "second", "third", ""]
    
    for i, (model_name, results) in enumerate(sorted_results):
        medal = medals[i] if i < len(medals) else "🏅"
        card_class = card_classes[i] if i < len(card_classes) else ""
        
        metrics = results['metrics']
        
        html_content += f"""
                    <div class="model-card {card_class}">
                        <div class="model-name">
                            <span class="medal">{medal}</span>
                            {model_name}
                        </div>
                        <div class="metric">
                            <span class="metric-name">CLIP Score</span>
                            <span class="metric-value">{metrics['clip_score']['mean']:.2f} ± {metrics['clip_score']['std']:.2f}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">BLEU-4</span>
                            <span class="metric-value">{metrics['bleu']['bleu-4']:.3f}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">ROUGE-L</span>
                            <span class="metric-value">{metrics['rouge']['rouge-l']['f']:.3f}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">BERTScore</span>
                            <span class="metric-value">{metrics['bertscore']['f1']:.3f}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">METEOR</span>
                            <span class="metric-value">{metrics['meteor']:.3f}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-name">CIDEr</span>
                            <span class="metric-value">{metrics['cider']:.3f}</span>
                        </div>
                    </div>
"""
    
    html_content += """
                </div>
            </div>
            
            <div class="section">
                <h2>📈 Grafici Comparativi</h2>
                <div class="chart-container">
                    <canvas id="metricsChart" width="400" height="200"></canvas>
                </div>
            </div>
            
            <div class="section">
                <h2>📋 Tabella Dettagliata</h2>
                <table>
                    <thead>
                        <tr>
                            <th>Posizione</th>
                            <th>Modello</th>
                            <th>CLIP Score</th>
                            <th>BLEU-4</th>
                            <th>ROUGE-L</th>
                            <th>BERTScore</th>
                            <th>METEOR</th>
                            <th>CIDEr</th>
                        </tr>
                    </thead>
                    <tbody>
"""
    
    # Aggiungi righe tabella
    for i, (model_name, results) in enumerate(sorted_results):
        metrics = results['metrics']
        position = f"{i+1}°"
        
        html_content += f"""
                        <tr>
                            <td>{position}</td>
                            <td><strong>{model_name}</strong></td>
                            <td>{metrics['clip_score']['mean']:.2f} ± {metrics['clip_score']['std']:.2f}</td>
                            <td>{metrics['bleu']['bleu-4']:.3f}</td>
                            <td>{metrics['rouge']['rouge-l']['f']:.3f}</td>
                            <td>{metrics['bertscore']['f1']:.3f}</td>
                            <td>{metrics['meteor']:.3f}</td>
                            <td>{metrics['cider']:.3f}</td>
                        </tr>
"""
    
    # Prepara dati per grafico
    model_names = [name for name, _ in sorted_results]
    clip_scores = [results['metrics']['clip_score']['mean'] for _, results in sorted_results]
    bleu_scores = [results['metrics']['bleu']['bleu-4'] * 100 for _, results in sorted_results]  # Scale per visualizzazione
    
    html_content += f"""
                    </tbody>
                </table>
            </div>
            
            <div class="section">
                <h2>🎯 Statistiche Generali</h2>
                <div class="summary-stats">
                    <div class="stat-card">
                        <div class="stat-number">{len(sorted_results)}</div>
                        <div class="stat-label">Modelli Valutati</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{max(clip_scores):.1f}</div>
                        <div class="stat-label">Miglior CLIP Score</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{(max(clip_scores) - min(clip_scores)):.1f}</div>
                        <div class="stat-label">Range CLIP Score</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{np.mean(clip_scores):.1f}</div>
                        <div class="stat-label">CLIP Score Medio</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>🚀 Report generato dal sistema di valutazione comprehensive SVG Captioning</p>
            <p>Utilizzando REAL CLIP scores per valutazione accurata</p>
        </div>
    </div>
    
    <script>
        // Grafico comparativo
        const ctx = document.getElementById('metricsChart').getContext('2d');
        const chart = new Chart(ctx, {{
            type: 'bar',
            data: {{
                labels: {json.dumps(model_names)},
                datasets: [{{
                    label: 'CLIP Score',
                    data: {json.dumps(clip_scores)},
                    backgroundColor: 'rgba(52, 152, 219, 0.8)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 2
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: 'Confronto CLIP Scores',
                        font: {{
                            size: 16,
                            weight: 'bold'
                        }}
                    }},
                    legend: {{
                        display: false
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true,
                        title: {{
                            display: true,
                            text: 'CLIP Score'
                        }}
                    }},
                    x: {{
                        title: {{
                            display: true,
                            text: 'Modelli'
                        }}
                    }}
                }}
            }}
        }});
    </script>
</body>
</html>
"""
    
    # Salva file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"📄 Report HTML salvato: {output_file}")

def main():
    parser = argparse.ArgumentParser(description="Crea report finale comprehensive")
    parser.add_argument("--results_dir", default="evaluation_results/comprehensive_metrics", 
                       help="Directory con risultati comprehensive")
    parser.add_argument("--output_file", help="File output HTML")
    
    args = parser.parse_args()
    
    if not args.output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output_file = f"evaluation_results/reports/FINAL_COMPREHENSIVE_REPORT_{timestamp}.html"
    
    # Crea directory output
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    logger.info("🏆 CREAZIONE REPORT FINALE COMPREHENSIVE")
    logger.info("=" * 50)
    logger.info(f"Directory risultati: {args.results_dir}")
    logger.info(f"Output: {args.output_file}")
    
    # Carica tutti i risultati comprehensive
    all_results = {}
    
    for file in os.listdir(args.results_dir):
        if file.endswith('_comprehensive_metrics.json'):
            model_name = file.replace('_comprehensive_metrics.json', '')
            file_path = os.path.join(args.results_dir, file)
            
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                    all_results[model_name] = data
                    logger.info(f"✅ Caricato: {model_name}")
            except Exception as e:
                logger.error(f"❌ Errore caricamento {file}: {e}")
    
    if not all_results:
        logger.error("❌ Nessun risultato comprehensive trovato!")
        return
    
    # Crea report
    create_final_html_report(all_results, args.output_file)
    
    logger.info("=" * 50)
    logger.info("✅ REPORT FINALE COMPLETATO!")
    logger.info(f"📄 File: {args.output_file}")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
