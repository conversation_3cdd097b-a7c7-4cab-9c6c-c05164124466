#!/usr/bin/env python3
"""
🎯 ULTRA SIMPLE CLIP SCORE
Calcola pseudo-CLIP Score usando solo caratteristiche base
"""

import os
import json
import argparse
import logging
import re
from datetime import datetime
from PIL import Image
import cairosvg
import io
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_xml_to_svg_FIXED(xml_content):
    """Parser SVG MIGLIORATO che fixa tutti i problemi"""
    
    # Inizia SVG con header corretto
    svg_parts = []
    svg_parts.append('<?xml version="1.0" encoding="UTF-8"?>')
    svg_parts.append('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">')
    svg_parts.append('<rect width="512" height="512" fill="white"/>')  # Sfondo bianco
    
    # Split per linee
    lines = xml_content.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Cerca pattern style= e d=
        style_match = re.search(r'style=([^\\t]+)', line)
        d_match = re.search(r'd=(.+)', line)
        
        if style_match and d_match:
            style_content = style_match.group(1)
            d_content = d_match.group(1)
            
            # Fixa colori RGB
            style_content = re.sub(r'rgb\((\d+),(\d+),(\d+)\)', r'rgb(\1,\2,\3)', style_content)
            
            # Fixa formato colori: fill:0,0,0 -> fill:#000000
            def fix_color_format(match):
                r, g, b = match.groups()
                return f'fill:#{int(r):02x}{int(g):02x}{int(b):02x}'
            
            style_content = re.sub(r'fill:(\d+),(\d+),(\d+)', fix_color_format, style_content)
            style_content = re.sub(r'stroke:(\d+),(\d+),(\d+)', 
                                 lambda m: f'stroke:#{int(m.group(1)):02x}{int(m.group(2)):02x}{int(m.group(3)):02x}', 
                                 style_content)
            
            # Crea path element
            path_element = f'<path style="{style_content}" d="{d_content}" />'
            svg_parts.append(path_element)
    
    svg_parts.append('</svg>')
    return '\n'.join(svg_parts)

def svg_to_image(xml_content):
    """Converte SVG XML a PIL Image"""
    try:
        # Converti a SVG completo
        svg_content = parse_xml_to_svg_FIXED(xml_content)
        
        # Converti SVG a PNG usando CairoSVG
        png_data = cairosvg.svg2png(bytestring=svg_content.encode('utf-8'))
        
        # Crea PIL Image
        image = Image.open(io.BytesIO(png_data))
        
        # Converti a RGB se necessario
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        return image
        
    except Exception as e:
        logger.warning(f"⚠️ Errore conversione SVG: {e}")
        return None

def calculate_pseudo_clip_score(image, caption):
    """Calcola pseudo-CLIP Score usando caratteristiche base"""
    try:
        # Caratteristiche immagine
        img_array = np.array(image)

        # Caratteristiche visive base
        mean_color = np.mean(img_array, axis=(0, 1))
        brightness = np.mean(img_array)
        contrast = np.std(img_array)

        # Caratteristiche testo base
        caption_lower = caption.lower()
        text_length = len(caption.split())

        # Parole chiave colore
        color_words = ['black', 'white', 'red', 'blue', 'green', 'yellow', 'gray', 'dark', 'light', 'bright']
        color_mentions = sum(1 for word in color_words if word in caption_lower)

        # Parole chiave forma
        shape_words = ['circle', 'square', 'rectangle', 'triangle', 'line', 'curve', 'round', 'straight']
        shape_mentions = sum(1 for word in shape_words if word in caption_lower)

        # Parole descrittive
        desc_words = ['simple', 'complex', 'detailed', 'minimalist', 'abstract', 'geometric']
        desc_mentions = sum(1 for word in desc_words if word in caption_lower)

        # Score base (0-100)
        base_score = 30  # Score base

        # Bonus per lunghezza appropriata (50-200 parole)
        if 10 <= text_length <= 50:
            base_score += 15
        elif text_length > 5:
            base_score += 5

        # Bonus per menzioni colore (se immagine ha contrasto)
        if contrast > 50 and color_mentions > 0:
            base_score += min(color_mentions * 10, 20)

        # Bonus per menzioni forma
        base_score += min(shape_mentions * 8, 15)

        # Bonus per descrizioni
        base_score += min(desc_mentions * 5, 10)

        # Penalità per caption troppo generiche
        generic_phrases = ['the image', 'this image', 'the picture']
        generic_count = sum(1 for phrase in generic_phrases if phrase in caption_lower)
        if generic_count > 2:
            base_score -= 10

        # Normalizza a 0-100
        clip_score = max(0, min(100, base_score))

        return clip_score

    except Exception as e:
        logger.error(f"❌ Errore calcolo pseudo-CLIP score: {e}")
        return 0.0

def process_results_file(results_file, model_name, max_examples=None):
    """Processa file risultati e calcola CLIP scores"""
    logger.info(f"🎯 Calcolo CLIP Scores per {model_name}")
    
    # Carica risultati
    with open(results_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    results = data.get('results', [])
    if max_examples:
        results = results[:max_examples]
    
    logger.info(f"📊 Processando {len(results)} esempi")
    
    # Calcola CLIP scores
    clip_scores = []
    successful_conversions = 0
    
    for i, result in enumerate(results):
        try:
            # Estrai dati
            xml_content = result.get('xml_content', '')
            caption = result.get('generated_caption', '')
            
            # Pulisci caption se necessario (per Gemma)
            if 'model\\n' in caption:
                caption = caption.split('model\\n')[1].strip()
            
            # Converti SVG a immagine
            image = svg_to_image(xml_content)
            if image is None:
                clip_scores.append(0.0)
                continue
            
            # Calcola pseudo-CLIP score
            clip_score = calculate_pseudo_clip_score(image, caption)
            clip_scores.append(clip_score)
            successful_conversions += 1
            
            # Progress ogni 50 esempi
            if (i + 1) % 50 == 0:
                avg_score = np.mean(clip_scores) if clip_scores else 0
                logger.info(f"📊 Progress: {i+1}/{len(results)} - Avg CLIP: {avg_score:.2f}")
        
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            clip_scores.append(0.0)
    
    # Statistiche finali
    if clip_scores:
        avg_clip = np.mean(clip_scores)
        std_clip = np.std(clip_scores)
        min_clip = np.min(clip_scores)
        max_clip = np.max(clip_scores)
    else:
        avg_clip = std_clip = min_clip = max_clip = 0.0
    
    logger.info(f"✅ CLIP Score completato per {model_name}")
    logger.info(f"📊 Conversioni riuscite: {successful_conversions}/{len(results)}")
    logger.info(f"📊 CLIP Score medio: {avg_clip:.2f} ± {std_clip:.2f}")
    logger.info(f"📊 Range: {min_clip:.2f} - {max_clip:.2f}")
    
    return {
        'model': model_name,
        'total_examples': int(len(results)),
        'successful_conversions': int(successful_conversions),
        'clip_scores': [float(score) for score in clip_scores],
        'avg_clip_score': float(avg_clip),
        'std_clip_score': float(std_clip),
        'min_clip_score': float(min_clip),
        'max_clip_score': float(max_clip),
        'timestamp': datetime.now().isoformat()
    }

def main():
    parser = argparse.ArgumentParser(description='Simple CLIP Score Calculator')
    parser.add_argument('--results_file', required=True, help='File JSON risultati')
    parser.add_argument('--model_name', required=True, help='Nome modello')
    parser.add_argument('--max_examples', type=int, help='Max esempi da processare')
    parser.add_argument('--output_dir', default='evaluation_results/clip_scores', help='Directory output')
    
    args = parser.parse_args()
    
    logger.info("🎯 SIMPLE CLIP SCORE CALCULATOR")
    logger.info("=" * 50)
    logger.info(f"🤖 Modello: {args.model_name}")
    logger.info(f"📁 File: {os.path.basename(args.results_file)}")
    logger.info(f"📊 Max esempi: {args.max_examples or 'Tutti'}")
    logger.info("=" * 50)
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Processa file
    results = process_results_file(args.results_file, args.model_name, args.max_examples)
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(args.output_dir, f'{args.model_name.lower()}_clip_scores_{timestamp}.json')
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"💾 Risultati salvati: {output_file}")
    logger.info("🎉 CLIP Score calculation completato!")

if __name__ == "__main__":
    main()
