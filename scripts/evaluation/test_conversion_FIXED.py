#!/usr/bin/env python3
"""
🧪 TEST CONVERSIONE PNG FIXED
Test rapido per verificare che la conversione XML→PNG funzioni
"""

import os
import json
import logging
import tempfile
import sys

# Setup logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_conversion():
    """Test conversione XML→PNG"""
    logger.info("🧪 Test conversione XML→PNG...")
    
    try:
        # Importa funzioni dal script Gemma
        sys.path.append('/work/tesi_ediluzio/scripts/evaluation')
        from gemma_evaluation_COMPLETE_400_CPU import convert_xml_to_png_FIXED
        
        # Carica un esempio dal dataset
        dataset_file = 'data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json'
        
        with open(dataset_file, 'r') as f:
            data = json.load(f)
        
        # Prendi primo esempio
        example = data[0]
        xml_content = example.get('xml_content', '')
        
        logger.info(f"📊 XML content length: {len(xml_content)} chars")
        logger.info(f"📊 XML preview: {xml_content[:200]}...")
        
        # Test conversione
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            png_path = f.name
        
        logger.info(f"🔧 Convertendo in: {png_path}")
        
        success = convert_xml_to_png_FIXED(xml_content, png_path, size=224)
        
        if success:
            logger.info(f"✅ Conversione riuscita!")
            logger.info(f"📁 File PNG: {png_path}")
            logger.info(f"📊 File size: {os.path.getsize(png_path)} bytes")
            
            # Rimuovi file temporaneo
            try:
                os.remove(png_path)
                logger.info("🗑️ File temporaneo rimosso")
            except:
                pass
            
            return True
        else:
            logger.error("❌ Conversione fallita!")
            return False
            
    except Exception as e:
        logger.error(f"❌ Test conversione FALLITO: {e}")
        import traceback
        logger.error(f"🔍 Traceback: {traceback.format_exc()}")
        return False

def test_multiple_examples():
    """Test conversione su più esempi"""
    logger.info("🧪 Test conversione multipla...")
    
    try:
        # Importa funzioni dal script Gemma
        sys.path.append('/work/tesi_ediluzio/scripts/evaluation')
        from gemma_evaluation_COMPLETE_400_CPU import convert_xml_to_png_FIXED
        
        # Carica dataset
        dataset_file = 'data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json'
        
        with open(dataset_file, 'r') as f:
            data = json.load(f)
        
        # Test primi 5 esempi
        success_count = 0
        total_count = min(5, len(data))
        
        for i in range(total_count):
            example = data[i]
            xml_content = example.get('xml_content', '')
            
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
                png_path = f.name
            
            logger.info(f"🔧 Test esempio {i}: {len(xml_content)} chars")
            
            success = convert_xml_to_png_FIXED(xml_content, png_path, size=224)
            
            if success:
                success_count += 1
                logger.info(f"✅ Esempio {i}: OK ({os.path.getsize(png_path)} bytes)")
            else:
                logger.error(f"❌ Esempio {i}: FALLITO")
            
            # Rimuovi file temporaneo
            try:
                os.remove(png_path)
            except:
                pass
        
        logger.info(f"📊 Risultato: {success_count}/{total_count} conversioni riuscite")
        return success_count == total_count
        
    except Exception as e:
        logger.error(f"❌ Test multiplo FALLITO: {e}")
        return False

def main():
    logger.info("🧪 INIZIO TEST CONVERSIONE PNG FIXED")
    logger.info("=" * 50)
    
    # Test 1: Conversione singola
    try:
        if test_conversion():
            logger.info("✅ Test 1 PASSATO: Conversione singola")
        else:
            logger.error("❌ Test 1 FALLITO: Conversione singola")
    except Exception as e:
        logger.error(f"❌ Test 1 ERRORE: {e}")
    
    # Test 2: Conversione multipla
    try:
        if test_multiple_examples():
            logger.info("✅ Test 2 PASSATO: Conversione multipla")
        else:
            logger.error("❌ Test 2 FALLITO: Conversione multipla")
    except Exception as e:
        logger.error(f"❌ Test 2 ERRORE: {e}")
    
    logger.info("=" * 50)
    logger.info("🎉 TEST CONVERSIONE COMPLETATI!")
    
    logger.info("🚀 Se i test sono passati, Gemma dovrebbe funzionare")

if __name__ == "__main__":
    main()
