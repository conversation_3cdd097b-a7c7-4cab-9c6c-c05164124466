#!/usr/bin/env python3
"""
🔍 QUALITATIVE ANALYSIS GENERATOR
Crea analisi qualitativa con esempi HTML, immagini e caption
"""

import os
import json
import argparse
import logging
import base64
import tempfile
import shutil
from datetime import datetime
import random

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def convert_svg_to_png_base64(xml_content, size=224):
    """Converte SVG a PNG e restituisce base64"""
    try:
        import cairosvg
        from PIL import Image
        import io
        
        # Fix colori SVG
        if 'fill:' in xml_content and '#' not in xml_content:
            xml_content = xml_content.replace('fill:0,0,0', 'fill:#000000')
            xml_content = xml_content.replace('fill:255,255,255', 'fill:#ffffff')
        
        # Converti a PNG
        png_data = cairosvg.svg2png(
            bytestring=xml_content.encode('utf-8'),
            output_width=size,
            output_height=size,
            background_color='white'
        )
        
        # Converti a base64
        base64_str = base64.b64encode(png_data).decode('utf-8')
        return f"data:image/png;base64,{base64_str}"
        
    except Exception as e:
        logger.warning(f"❌ SVG conversion error: {e}")
        return None

def load_model_results(results_file):
    """Carica risultati di un modello"""
    try:
        with open(results_file, 'r') as f:
            data = json.load(f)
        
        # Gestisci diversi formati
        if isinstance(data, list):
            results = data
        else:
            results = data.get('results', [])
        
        return results
    except Exception as e:
        logger.error(f"❌ Errore caricamento {results_file}: {e}")
        return []

def select_examples(results, num_examples=10, strategy='diverse'):
    """Seleziona esempi rappresentativi"""
    if len(results) <= num_examples:
        return results
    
    if strategy == 'diverse':
        # Seleziona esempi diversi per lunghezza caption
        results_with_len = [(r, len(r.get('generated_caption', ''))) for r in results]
        results_with_len.sort(key=lambda x: x[1])
        
        # Prendi esempi da diverse fasce di lunghezza
        selected = []
        step = len(results_with_len) // num_examples
        for i in range(0, len(results_with_len), step):
            if len(selected) < num_examples:
                selected.append(results_with_len[i][0])
        
        return selected[:num_examples]
    
    elif strategy == 'random':
        return random.sample(results, num_examples)
    
    else:  # 'first'
        return results[:num_examples]

def calculate_example_metrics(generated, ground_truth):
    """Calcola metriche per singolo esempio"""
    try:
        import nltk
        from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
        from rouge_score import rouge_scorer
        
        # BLEU-4
        ref_tokens = [nltk.word_tokenize(ground_truth.lower())]
        gen_tokens = nltk.word_tokenize(generated.lower())
        smoothing = SmoothingFunction().method1
        
        bleu4 = sentence_bleu(ref_tokens, gen_tokens, weights=(0.25,0.25,0.25,0.25), smoothing_function=smoothing)
        
        # ROUGE-L
        scorer = rouge_scorer.RougeScorer(['rougeL'], use_stemmer=True)
        rouge_scores = scorer.score(ground_truth, generated)
        rouge_l = rouge_scores['rougeL'].fmeasure
        
        return {
            'bleu4': bleu4,
            'rouge_l': rouge_l,
            'gen_length': len(generated.split()),
            'ref_length': len(ground_truth.split())
        }
    except Exception as e:
        logger.warning(f"❌ Metrics error: {e}")
        return {'bleu4': 0.0, 'rouge_l': 0.0, 'gen_length': 0, 'ref_length': 0}

def create_qualitative_analysis(model_results_files, output_file, num_examples=10):
    """Crea analisi qualitativa HTML"""
    logger.info("🔍 Creazione analisi qualitativa...")
    
    # Carica risultati per tutti i modelli
    all_model_data = {}
    for model_name, results_file in model_results_files.items():
        results = load_model_results(results_file)
        if results:
            # Seleziona esempi rappresentativi
            selected_examples = select_examples(results, num_examples, 'diverse')
            all_model_data[model_name] = selected_examples
            logger.info(f"✅ {model_name}: {len(selected_examples)} esempi")
    
    if not all_model_data:
        logger.error("❌ Nessun dato caricato!")
        return
    
    # Trova esempi comuni (stesso indice/ID)
    common_examples = []
    base_model = list(all_model_data.keys())[0]
    base_examples = all_model_data[base_model]
    
    for i, base_example in enumerate(base_examples):
        example_data = {
            'index': i,
            'xml_content': base_example.get('xml_content', '') or base_example.get('xml', ''),
            'ground_truth': base_example.get('ground_truth', '') or base_example.get('caption', ''),
            'models': {}
        }
        
        # Raccogli caption di tutti i modelli per questo esempio
        for model_name, examples in all_model_data.items():
            if i < len(examples):
                generated = examples[i].get('generated_caption', '') or examples[i].get('prediction', '')
                if generated:
                    metrics = calculate_example_metrics(generated, example_data['ground_truth'])
                    example_data['models'][model_name] = {
                        'caption': generated,
                        'metrics': metrics
                    }
        
        # Solo se abbiamo almeno 2 modelli
        if len(example_data['models']) >= 2:
            common_examples.append(example_data)
    
    logger.info(f"📊 Trovati {len(common_examples)} esempi comuni")
    
    # Genera HTML
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>🔍 Qualitative Analysis - SVG Image Captioning</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f8f9fa; }}
        .container {{ max-width: 1400px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 4px 20px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; font-size: 2.5em; }}
        h2 {{ color: #34495e; border-bottom: 3px solid #3498db; padding-bottom: 10px; margin-top: 40px; }}
        
        .example {{ border: 2px solid #e9ecef; border-radius: 10px; margin: 30px 0; padding: 20px; background: #fdfdfd; }}
        .example-header {{ background: #3498db; color: white; padding: 15px; margin: -20px -20px 20px -20px; border-radius: 8px 8px 0 0; }}
        
        .image-section {{ text-align: center; margin: 20px 0; }}
        .svg-image {{ max-width: 300px; border: 2px solid #ddd; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        
        .ground-truth {{ background: #d4edda; padding: 15px; border-radius: 8px; margin: 15px 0; border-left: 5px solid #28a745; }}
        .ground-truth h4 {{ margin: 0 0 10px 0; color: #155724; }}
        
        .models-comparison {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin: 20px 0; }}
        .model-result {{ border: 2px solid #dee2e6; border-radius: 8px; padding: 15px; background: white; }}
        .model-result.best {{ border-color: #28a745; background: #f8fff9; }}
        .model-result.worst {{ border-color: #dc3545; background: #fff8f8; }}
        
        .model-name {{ font-weight: bold; color: #495057; margin-bottom: 10px; font-size: 1.1em; }}
        .model-caption {{ margin: 10px 0; line-height: 1.6; }}
        .model-metrics {{ display: flex; gap: 15px; margin-top: 10px; font-size: 0.9em; }}
        .metric {{ background: #e9ecef; padding: 5px 10px; border-radius: 15px; }}
        .metric.good {{ background: #d4edda; color: #155724; }}
        .metric.bad {{ background: #f8d7da; color: #721c24; }}
        
        .summary {{ background: #e8f4fd; padding: 20px; border-radius: 10px; margin: 30px 0; }}
        .timestamp {{ text-align: center; color: #6c757d; margin-top: 40px; font-style: italic; }}
        
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
        .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center; border: 1px solid #dee2e6; }}
        .stat-number {{ font-size: 2em; font-weight: bold; color: #3498db; }}
        .stat-label {{ color: #6c757d; margin-top: 5px; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 QUALITATIVE ANALYSIS</h1>
        <h2>SVG Image Captioning Models - Detailed Examples</h2>
        
        <div class="summary">
            <h3>📊 Analysis Overview</h3>
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">{len(common_examples)}</div>
                    <div class="stat-label">Examples Analyzed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{len(all_model_data)}</div>
                    <div class="stat-label">Models Compared</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">{datetime.now().strftime("%Y-%m-%d")}</div>
                    <div class="stat-label">Analysis Date</div>
                </div>
            </div>
            <p><strong>🎯 Purpose:</strong> Compare model performance on identical examples to understand strengths and weaknesses.</p>
            <p><strong>📏 Metrics:</strong> BLEU-4 (n-gram overlap), ROUGE-L (longest common subsequence), Caption Length</p>
        </div>
        
        <h2>🔍 DETAILED EXAMPLES</h2>
    """
    
    # Aggiungi esempi
    for i, example in enumerate(common_examples):
        # Converti immagine
        image_b64 = None
        if example['xml_content'] and example['xml_content'].startswith('<'):
            image_b64 = convert_svg_to_png_base64(example['xml_content'])
        
        # Trova migliore e peggiore modello per questo esempio
        model_scores = {}
        for model_name, data in example['models'].items():
            # Score combinato (BLEU + ROUGE)
            score = (data['metrics']['bleu4'] + data['metrics']['rouge_l']) / 2
            model_scores[model_name] = score
        
        best_model = max(model_scores.keys(), key=lambda x: model_scores[x]) if model_scores else None
        worst_model = min(model_scores.keys(), key=lambda x: model_scores[x]) if len(model_scores) > 1 else None
        
        html_content += f"""
        <div class="example">
            <div class="example-header">
                <h3>📝 Example {i+1}</h3>
            </div>
            
            <div class="image-section">
        """
        
        if image_b64:
            html_content += f'<img src="{image_b64}" alt="SVG Image {i+1}" class="svg-image">'
        else:
            html_content += '<div style="width: 300px; height: 200px; background: #f8f9fa; border: 2px dashed #dee2e6; display: flex; align-items: center; justify-content: center; margin: 0 auto; border-radius: 8px;">🖼️ Image not available</div>'
        
        html_content += f"""
            </div>
            
            <div class="ground-truth">
                <h4>🎯 Ground Truth Caption:</h4>
                <p>"{example['ground_truth']}"</p>
                <small>Length: {len(example['ground_truth'].split())} words</small>
            </div>
            
            <div class="models-comparison">
        """
        
        # Aggiungi risultati di ogni modello
        for model_name, data in example['models'].items():
            css_class = ""
            if model_name == best_model:
                css_class = "best"
            elif model_name == worst_model:
                css_class = "worst"
            
            metrics = data['metrics']
            
            html_content += f"""
                <div class="model-result {css_class}">
                    <div class="model-name">
                        {model_name}
                        {' 🏆' if model_name == best_model else ' 📉' if model_name == worst_model else ''}
                    </div>
                    <div class="model-caption">"{data['caption']}"</div>
                    <div class="model-metrics">
                        <span class="metric {'good' if metrics['bleu4'] > 0.1 else 'bad'}">
                            BLEU-4: {metrics['bleu4']:.3f}
                        </span>
                        <span class="metric {'good' if metrics['rouge_l'] > 0.3 else 'bad'}">
                            ROUGE-L: {metrics['rouge_l']:.3f}
                        </span>
                        <span class="metric">
                            Length: {metrics['gen_length']} words
                        </span>
                    </div>
                </div>
            """
        
        html_content += """
            </div>
        </div>
        """
    
    html_content += f"""
        <div class="timestamp">
            📅 Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}<br>
            🔧 Analysis: Qualitative comparison of {len(all_model_data)} models on {len(common_examples)} examples<br>
            ✅ Status: COMPLETE - Detailed qualitative analysis ready
        </div>
    </div>
</body>
</html>
    """
    
    # Salva file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"✅ Analisi qualitativa salvata: {output_file}")
    
    return len(common_examples)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Crea analisi qualitativa HTML")
    parser.add_argument("--results_files", nargs='+', required=True, help="File risultati: model_name:file_path")
    parser.add_argument("--output_file", required=True, help="File HTML output")
    parser.add_argument("--num_examples", type=int, default=10, help="Numero esempi da analizzare")

    args = parser.parse_args()

    # Parse model results files
    model_results_files = {}
    for item in args.results_files:
        if ':' in item:
            model_name, file_path = item.split(':', 1)
            model_results_files[model_name] = file_path
        else:
            logger.error(f"❌ Formato errato: {item}. Usa model_name:file_path")
            return

    logger.info(f"📊 Modelli da analizzare: {list(model_results_files.keys())}")

    # Crea directory output
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)

    # Crea analisi
    num_examples = create_qualitative_analysis(model_results_files, args.output_file, args.num_examples)

    print("\n" + "="*60)
    print("🔍 QUALITATIVE ANALYSIS GENERATED")
    print("="*60)
    print(f"📊 Models analyzed: {len(model_results_files)}")
    print(f"📝 Examples analyzed: {num_examples}")
    print(f"📁 HTML file: {args.output_file}")
    print("="*60)
    print("💡 Open the HTML file in your browser to view the analysis!")

if __name__ == "__main__":
    main()
