#!/usr/bin/env python3
"""
📊 CALCOLO METRICHE COMPREHENSIVE
Calcola BLEU-1,2,3,4, METEOR, ROUGE-L, CLIP Score per trained models
"""

import os
import json
import argparse
import logging
from datetime import datetime
import numpy as np
from PIL import Image
import torch

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_dependencies():
    """Verifica e installa dipendenze necessarie"""
    try:
        import nltk
        from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
        from nltk.translate.meteor_score import meteor_score
        from nltk.tokenize import word_tokenize
        
        # Download NLTK data
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')
        
        try:
            nltk.data.find('corpora/wordnet')
        except LookupError:
            nltk.download('wordnet')
            
        return True
    except ImportError:
        logger.error("❌ Installa nltk: pip install nltk")
        return False

def setup_clip_score():
    """Setup CLIP Score metric"""
    try:
        from torchmetrics.multimodal.clip_score import CLIPScore
        device = "cuda" if torch.cuda.is_available() else "cpu"
        clip_score_metric = CLIPScore(model_name_or_path="openai/clip-vit-base-patch16").to(device)
        return clip_score_metric, device
    except ImportError:
        logger.warning("❌ CLIP Score non disponibile. Installa: pip install torchmetrics[multimodal]")
        return None, None

def calculate_individual_bleu_scores(reference, candidate):
    """Calcola BLEU-1, BLEU-2, BLEU-3, BLEU-4 individuali"""
    try:
        from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
        from nltk.tokenize import word_tokenize
        
        ref_tokens = [word_tokenize(reference.lower())]
        cand_tokens = word_tokenize(candidate.lower())
        
        smoothing = SmoothingFunction().method1
        
        # Calcola BLEU per ogni n-gram
        bleu_scores = {}
        for n in range(1, 5):
            weights = [0] * 4
            weights[n-1] = 1.0  # Solo n-gram specifico
            
            bleu_score = sentence_bleu(
                ref_tokens, 
                cand_tokens, 
                weights=weights,
                smoothing_function=smoothing
            )
            bleu_scores[f'bleu_{n}'] = bleu_score
            
        return bleu_scores
    except Exception as e:
        logger.warning(f"Errore BLEU: {e}")
        return {f'bleu_{n}': 0.0 for n in range(1, 5)}

def calculate_meteor_score(reference, candidate):
    """Calcola METEOR score"""
    try:
        from nltk.translate.meteor_score import meteor_score
        from nltk.tokenize import word_tokenize
        
        ref_tokens = word_tokenize(reference.lower())
        cand_tokens = word_tokenize(candidate.lower())
        
        meteor = meteor_score([ref_tokens], cand_tokens)
        return meteor
    except Exception as e:
        logger.warning(f"Errore METEOR: {e}")
        return 0.0

def calculate_rouge_l(reference, candidate):
    """Calcola ROUGE-L score"""
    try:
        from nltk.tokenize import word_tokenize
        
        ref_tokens = word_tokenize(reference.lower())
        cand_tokens = word_tokenize(candidate.lower())
        
        def lcs_length(x, y):
            m, n = len(x), len(y)
            dp = [[0] * (n + 1) for _ in range(m + 1)]
            
            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if x[i-1] == y[j-1]:
                        dp[i][j] = dp[i-1][j-1] + 1
                    else:
                        dp[i][j] = max(dp[i-1][j], dp[i][j-1])
            return dp[m][n]
        
        lcs_len = lcs_length(ref_tokens, cand_tokens)
        
        if len(ref_tokens) == 0 or len(cand_tokens) == 0:
            return 0.0
            
        precision = lcs_len / len(cand_tokens) if len(cand_tokens) > 0 else 0
        recall = lcs_len / len(ref_tokens) if len(ref_tokens) > 0 else 0
        
        if precision + recall == 0:
            return 0.0
            
        f1 = 2 * precision * recall / (precision + recall)
        return f1
        
    except Exception as e:
        logger.warning(f"Errore ROUGE-L: {e}")
        return 0.0

def calculate_clip_score_batch(images, captions, clip_metric, device):
    """Calcola CLIP Score per batch di immagini e caption"""
    if clip_metric is None:
        return []
    
    try:
        clip_scores = []
        
        for img_path, caption in zip(images, captions):
            if not os.path.exists(img_path) or not caption.strip():
                clip_scores.append(0.0)
                continue
                
            # Carica immagine
            try:
                image = Image.open(img_path).convert('RGB')
                
                # Calcola CLIP Score
                score = clip_metric(image, caption)
                clip_scores.append(float(score.cpu()))
                
            except Exception as e:
                logger.warning(f"Errore CLIP per {img_path}: {e}")
                clip_scores.append(0.0)
        
        return clip_scores
        
    except Exception as e:
        logger.warning(f"Errore CLIP Score batch: {e}")
        return [0.0] * len(images)

def calculate_comprehensive_metrics(results_file, model_name, images_dir=None):
    """Calcola tutte le metriche comprehensive"""
    logger.info(f"📊 Calcolo metriche comprehensive per {model_name}")
    
    # Carica risultati
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    results = data.get('results', [])
    logger.info(f"📈 Processando {len(results)} esempi...")
    
    # Setup CLIP Score
    clip_metric, device = setup_clip_score()
    
    # Inizializza metriche
    all_metrics = {
        'bleu_1': [], 'bleu_2': [], 'bleu_3': [], 'bleu_4': [],
        'meteor': [], 'rouge_l': [], 'clip_score': []
    }
    
    # Prepara dati per CLIP Score
    image_paths = []
    captions = []
    
    for i, result in enumerate(results):
        if i % 10 == 0:
            logger.info(f"📈 Processando esempio {i+1}/{len(results)}...")
            
        ground_truth = result.get('ground_truth', '')
        generated = result.get('generated_caption', '')
        
        if not ground_truth or not generated:
            continue
        
        # BLEU scores individuali
        bleu_scores = calculate_individual_bleu_scores(ground_truth, generated)
        for key, value in bleu_scores.items():
            all_metrics[key].append(value)
        
        # METEOR
        meteor = calculate_meteor_score(ground_truth, generated)
        all_metrics['meteor'].append(meteor)
        
        # ROUGE-L
        rouge = calculate_rouge_l(ground_truth, generated)
        all_metrics['rouge_l'].append(rouge)
        
        # Prepara per CLIP Score
        img_path = result.get('image_path', '')
        if images_dir and img_path:
            full_img_path = os.path.join(images_dir, img_path)
            image_paths.append(full_img_path)
            captions.append(generated)
        else:
            image_paths.append('')
            captions.append(generated)
    
    # Calcola CLIP Scores
    logger.info("📊 Calcolando CLIP Scores...")
    clip_scores = calculate_clip_score_batch(image_paths, captions, clip_metric, device)
    all_metrics['clip_score'] = clip_scores
    
    # Calcola statistiche finali
    final_metrics = {
        'model': model_name,
        'total_examples': len(results),
        'processed_examples': len(all_metrics['bleu_1']),
        'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S")
    }
    
    for metric_name, values in all_metrics.items():
        if values:  # Solo se ci sono valori
            final_metrics[metric_name] = {
                'mean': float(np.mean(values)),
                'std': float(np.std(values)),
                'min': float(np.min(values)),
                'max': float(np.max(values))
            }
        else:
            final_metrics[metric_name] = {
                'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0
            }
    
    return final_metrics

def main():
    parser = argparse.ArgumentParser(description="Calcola metriche comprehensive")
    parser.add_argument("--results_file", required=True, help="File JSON risultati")
    parser.add_argument("--output_dir", default="evaluation_results/trained_models", help="Directory output")
    parser.add_argument("--model_name", required=True, help="Nome modello")
    parser.add_argument("--images_dir", help="Directory immagini per CLIP Score")
    
    args = parser.parse_args()
    
    print(f"📊 CALCOLO METRICHE COMPREHENSIVE")
    print("=" * 60)
    print(f"🤖 Modello: {args.model_name}")
    print(f"📁 Risultati: {os.path.basename(args.results_file)}")
    print("=" * 60)
    
    # Verifica dipendenze
    if not install_dependencies():
        return
    
    # Calcola metriche
    metrics = calculate_comprehensive_metrics(
        args.results_file, 
        args.model_name,
        args.images_dir
    )
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"{args.model_name}_comprehensive_metrics_{timestamp}.json"
    output_path = os.path.join(args.output_dir, output_filename)
    
    os.makedirs(args.output_dir, exist_ok=True)
    with open(output_path, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    # Stampa risultati
    print("=" * 60)
    print("🎉 METRICHE COMPREHENSIVE CALCOLATE!")
    print("=" * 60)
    print(f"📊 BLEU-1: {metrics['bleu_1']['mean']:.4f} (±{metrics['bleu_1']['std']:.4f})")
    print(f"📊 BLEU-2: {metrics['bleu_2']['mean']:.4f} (±{metrics['bleu_2']['std']:.4f})")
    print(f"📊 BLEU-3: {metrics['bleu_3']['mean']:.4f} (±{metrics['bleu_3']['std']:.4f})")
    print(f"📊 BLEU-4: {metrics['bleu_4']['mean']:.4f} (±{metrics['bleu_4']['std']:.4f})")
    print(f"📊 METEOR: {metrics['meteor']['mean']:.4f} (±{metrics['meteor']['std']:.4f})")
    print(f"📊 ROUGE-L: {metrics['rouge_l']['mean']:.4f} (±{metrics['rouge_l']['std']:.4f})")
    print(f"📊 CLIP Score: {metrics['clip_score']['mean']:.4f} (±{metrics['clip_score']['std']:.4f})")
    print(f"📄 Risultati: {output_filename}")
    print("=" * 60)

if __name__ == "__main__":
    main()
