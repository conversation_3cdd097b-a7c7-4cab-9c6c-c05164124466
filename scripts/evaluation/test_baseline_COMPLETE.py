#!/usr/bin/env python3
"""
🧪 TEST BASELINE EVALUATION COMPLETA
Test rapido con 5 esempi per verificare che tutto funzioni
"""

import os
import json
import logging
from datetime import datetime
from PIL import Image
import torch
from transformers import BlipProcessor, BlipForConditionalGeneration

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_dataset_loading():
    """Test caricamento dataset"""
    logger.info("🧪 Test caricamento dataset...")
    
    dataset_file = 'data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json'
    
    with open(dataset_file, 'r') as f:
        data = json.load(f)
    
    logger.info(f"✅ Dataset caricato: {len(data)} esempi")
    
    # Test primi 5 esempi
    test_data = data[:5]
    valid_count = 0
    
    for example in test_data:
        image_path = example.get('image_path', '')
        caption = example.get('caption', '')
        
        if os.path.exists(image_path) and caption:
            valid_count += 1
            logger.info(f"✅ Esempio {example['id']}: OK")
        else:
            logger.error(f"❌ Esempio {example['id']}: ERRORE")
    
    logger.info(f"✅ Esempi validi: {valid_count}/5")
    return test_data

def test_blip2_inference(test_data):
    """Test inference BLIP-2"""
    logger.info("🧪 Test inference BLIP-2...")
    
    try:
        # Carica modello BLIP-2
        processor = BlipProcessor.from_pretrained("Salesforce/blip2-opt-2.7b")
        model = BlipForConditionalGeneration.from_pretrained(
            "Salesforce/blip2-opt-2.7b",
            torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
            device_map="auto" if torch.cuda.is_available() else None
        )
        
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        if device == 'cpu':
            model = model.to(device)
        
        logger.info(f"✅ Modello BLIP-2 caricato su {device}")
        
        # Test su primo esempio
        example = test_data[0]
        image = Image.open(example['image_path']).convert('RGB')
        
        inputs = processor(image, return_tensors="pt").to(device)
        
        with torch.no_grad():
            generated_ids = model.generate(
                **inputs,
                max_length=50,  # Più corto per test
                num_beams=3,
                do_sample=False
            )
        
        generated_caption = processor.decode(generated_ids[0], skip_special_tokens=True)
        
        logger.info(f"✅ Caption generata: {generated_caption}")
        logger.info(f"✅ Ground truth: {example['caption'][:100]}...")
        
        # Pulizia
        del model, processor
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore test BLIP-2: {e}")
        return False

def test_image_quality():
    """Test qualità immagini"""
    logger.info("🧪 Test qualità immagini...")
    
    # Test alcune immagini random
    test_images = [
        'data/processed/baseline_dataset_COMPLETE/images/baseline_0000.png',
        'data/processed/baseline_dataset_COMPLETE/images/baseline_0100.png',
        'data/processed/baseline_dataset_COMPLETE/images/baseline_0200.png',
        'data/processed/baseline_dataset_COMPLETE/images/baseline_0399.png'
    ]
    
    for image_path in test_images:
        try:
            image = Image.open(image_path)
            
            # Verifica dimensioni
            width, height = image.size
            logger.info(f"✅ {os.path.basename(image_path)}: {width}x{height}, mode={image.mode}")
            
            # Verifica che non sia completamente nera o bianca
            import numpy as np
            img_array = np.array(image)
            
            # Calcola statistiche colore
            mean_color = np.mean(img_array, axis=(0, 1))
            std_color = np.std(img_array, axis=(0, 1))
            
            logger.info(f"   Mean RGB: {mean_color}")
            logger.info(f"   Std RGB: {std_color}")
            
            # Verifica che ci sia variazione (non tutto bianco/nero)
            if np.all(std_color > 10):
                logger.info(f"   ✅ Immagine ha variazione di colore")
            else:
                logger.warning(f"   ⚠️ Immagine potrebbe essere troppo uniforme")
        
        except Exception as e:
            logger.error(f"❌ Errore test immagine {image_path}: {e}")

def main():
    logger.info("🧪 INIZIO TEST BASELINE EVALUATION COMPLETA")
    logger.info("=" * 50)
    
    # Test 1: Caricamento dataset
    try:
        test_data = test_dataset_loading()
        logger.info("✅ Test 1 PASSATO: Caricamento dataset")
    except Exception as e:
        logger.error(f"❌ Test 1 FALLITO: {e}")
        return
    
    # Test 2: Qualità immagini
    try:
        test_image_quality()
        logger.info("✅ Test 2 PASSATO: Qualità immagini")
    except Exception as e:
        logger.error(f"❌ Test 2 FALLITO: {e}")
    
    # Test 3: Inference BLIP-2
    try:
        if test_blip2_inference(test_data):
            logger.info("✅ Test 3 PASSATO: Inference BLIP-2")
        else:
            logger.error("❌ Test 3 FALLITO: Inference BLIP-2")
    except Exception as e:
        logger.error(f"❌ Test 3 FALLITO: {e}")
    
    logger.info("=" * 50)
    logger.info("🎉 TEST COMPLETATI!")
    
    # Info sistema
    logger.info(f"🖥️ Device disponibile: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    if torch.cuda.is_available():
        logger.info(f"🖥️ GPU: {torch.cuda.get_device_name()}")
        logger.info(f"💾 VRAM: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB")

if __name__ == "__main__":
    main()
