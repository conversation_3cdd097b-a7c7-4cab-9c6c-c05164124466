#!/usr/bin/env python3
"""
🎯 EVALUATION PIPELINE PER MODELLI QUANTIZZATI
Valuta i modelli Gemma-2-9B e Llama-3.1-8B quantizzati quando pronti
"""

import os
import json
import argparse
import logging
from datetime import datetime
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, BitsAndBytesConfig
from peft import PeftModel
import gc

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_quantized_model(model_path, adapter_path=None):
    """Setup modello quantizzato con LoRA adapter"""
    logger.info(f"🔧 Caricamento modello quantizzato: {model_path}")
    
    # Configurazione quantizzazione 4-bit
    bnb_config = BitsAndBytesConfig(
        load_in_4bit=True,
        bnb_4bit_use_double_quant=True,
        bnb_4bit_quant_type="nf4",
        bnb_4bit_compute_dtype=torch.float16,
        llm_int8_enable_fp32_cpu_offload=True
    )
    
    try:
        # Carica tokenizer
        tokenizer = AutoTokenizer.from_pretrained(model_path)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        # Carica modello base quantizzato
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            quantization_config=bnb_config,
            device_map="auto",
            torch_dtype=torch.float16,
            trust_remote_code=True
        )
        
        # Carica adapter LoRA se specificato
        if adapter_path and os.path.exists(adapter_path):
            logger.info(f"🔗 Caricamento adapter LoRA: {adapter_path}")
            model = PeftModel.from_pretrained(model, adapter_path)
            model = model.merge_and_unload()
        
        logger.info("✅ Modello quantizzato caricato con successo")
        return model, tokenizer
        
    except Exception as e:
        logger.error(f"❌ Errore caricamento modello: {e}")
        return None, None

def run_inference_batch(model, tokenizer, dataset, batch_size=1, max_length=512):
    """Esegue inference su batch di esempi"""
    logger.info(f"🚀 Avvio inference su {len(dataset)} esempi")
    
    results = []
    model.eval()
    
    for i in range(0, len(dataset), batch_size):
        batch = dataset[i:i+batch_size]
        
        try:
            # Prepara prompt
            prompts = []
            for item in batch:
                # Gestisci diversi formati dataset
                xml_content = item.get('xml_content', '') or item.get('xml', '')
                prompt = f"Describe this SVG image in detail:\n{xml_content}\n\nDescription:"
                prompts.append(prompt)
            
            # Tokenizza
            inputs = tokenizer(
                prompts,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=max_length
            )
            
            # Sposta su device
            inputs = {k: v.to(model.device) for k, v in inputs.items()}
            
            # Genera
            with torch.no_grad():
                outputs = model.generate(
                    **inputs,
                    max_new_tokens=256,
                    do_sample=True,
                    temperature=0.7,
                    top_p=0.9,
                    pad_token_id=tokenizer.eos_token_id
                )
            
            # Decodifica
            for j, output in enumerate(outputs):
                generated_text = tokenizer.decode(output, skip_special_tokens=True)
                
                # Estrai solo la parte generata
                prompt_length = len(prompts[j])
                generated_caption = generated_text[prompt_length:].strip()
                
                result = {
                    'example_id': batch[j].get('id', i + j),
                    'xml_content': batch[j].get('xml_content', '') or batch[j].get('xml', ''),
                    'ground_truth': batch[j].get('caption', ''),
                    'generated_caption': generated_caption,
                    'success': True
                }
                results.append(result)
            
            # Cleanup periodico
            if (i + batch_size) % 10 == 0:
                gc.collect()
                torch.cuda.empty_cache()
                logger.info(f"📊 Processati {i + batch_size}/{len(dataset)} esempi")
                
        except Exception as e:
            logger.error(f"❌ Errore batch {i}: {e}")
            # Aggiungi risultati vuoti per mantenere consistenza
            for j in range(len(batch)):
                result = {
                    'example_id': batch[j].get('id', i + j),
                    'xml_content': batch[j].get('xml_content', '') or batch[j].get('xml', ''),
                    'ground_truth': batch[j].get('caption', ''),
                    'generated_caption': '',
                    'success': False
                }
                results.append(result)
    
    logger.info(f"✅ Inference completata: {len(results)} risultati")
    return results

def evaluate_quantized_model(model_name, model_path, adapter_path, dataset_file, output_dir, max_examples=400):
    """Valuta un singolo modello quantizzato"""
    logger.info(f"🎯 VALUTAZIONE MODELLO QUANTIZZATO: {model_name}")
    logger.info("=" * 60)
    
    # Carica dataset
    logger.info(f"📊 Caricamento dataset: {dataset_file}")
    with open(dataset_file, 'r') as f:
        dataset = json.load(f)
    
    if len(dataset) > max_examples:
        dataset = dataset[:max_examples]
        logger.info(f"📊 Limitato a {max_examples} esempi")
    
    # Setup modello
    model, tokenizer = setup_quantized_model(model_path, adapter_path)
    if model is None:
        logger.error("❌ Impossibile caricare il modello")
        return None
    
    # Esegui inference
    results = run_inference_batch(model, tokenizer, dataset)
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_data = {
        'model': model_name,
        'model_path': model_path,
        'adapter_path': adapter_path,
        'dataset': dataset_file,
        'total_examples': len(dataset),
        'successful_examples': sum(1 for r in results if r['success']),
        'timestamp': timestamp,
        'quantization': '4bit_ultra',
        'results': results
    }
    
    os.makedirs(output_dir, exist_ok=True)
    results_file = os.path.join(output_dir, f"{model_name}_quantized_results_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results_data, f, indent=2)
    
    logger.info(f"✅ Risultati salvati: {results_file}")
    
    # Cleanup
    del model
    del tokenizer
    gc.collect()
    torch.cuda.empty_cache()
    
    return results_file

def main():
    parser = argparse.ArgumentParser(description="Valuta modelli quantizzati")
    parser.add_argument("--model_name", required=True, help="Nome modello (GEMMA_QUANT o LLAMA_QUANT)")
    parser.add_argument("--model_path", required=True, help="Path al modello base")
    parser.add_argument("--adapter_path", help="Path all'adapter LoRA")
    parser.add_argument("--dataset", default="data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json", help="Dataset di test")
    parser.add_argument("--output_dir", default="evaluation_results/quantized_models", help="Directory output")
    parser.add_argument("--max_examples", type=int, default=400, help="Max esempi da valutare")
    
    args = parser.parse_args()
    
    logger.info("🎯 EVALUATION PIPELINE MODELLI QUANTIZZATI")
    logger.info("=" * 60)
    logger.info(f"🤖 Modello: {args.model_name}")
    logger.info(f"📁 Model path: {args.model_path}")
    logger.info(f"🔗 Adapter path: {args.adapter_path}")
    logger.info(f"📊 Dataset: {args.dataset}")
    logger.info(f"📈 Max esempi: {args.max_examples}")
    logger.info("=" * 60)
    
    # Valuta modello
    results_file = evaluate_quantized_model(
        args.model_name,
        args.model_path, 
        args.adapter_path,
        args.dataset,
        args.output_dir,
        args.max_examples
    )
    
    if results_file:
        logger.info("🎉 VALUTAZIONE COMPLETATA!")
        logger.info(f"📄 Risultati: {results_file}")
        return 0
    else:
        logger.error("❌ Valutazione fallita")
        return 1

if __name__ == "__main__":
    exit(main())
