#!/usr/bin/env python3
"""
🐛 GEMMA EVALUATION DEBUG
Versione debug con logging esteso e test su pochi esempi
"""

import os
import json
import argparse
import logging
import gc
import time
import tempfile
import subprocess
import threading
import re
from datetime import datetime
import torch
import numpy as np
from PIL import Image
from transformers import AutoTokenizer, AutoModelForCausalLM, CLIPProcessor, CLIPModel
from peft import PeftModel
import warnings
warnings.filterwarnings("ignore")

# Setup logging con DEBUG level
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """Pulisce memoria CPU e GPU"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()
    time.sleep(1)

def replacer(match):
    """Converte colori da formato numerico a RGB"""
    numbers = re.findall(r'\d+', match.group())
    if len(numbers) >= 3:
        return f"{match.group().split(':')[0]}:rgb({numbers[0]},{numbers[1]},{numbers[2]})"
    return match.group()

def de_parser_correct(svg_data):
    """de_parser CORRETTO con gestione RGB e sfondo bianco"""
    logger.debug(f"🔧 de_parser input: {len(svg_data)} chars")
    
    res = '<?xml version="1.0" encoding="utf-8"?>\n'
    res += '<svg viewBox="0 0 512 512" width="512" height="512" xmlns="http://www.w3.org/2000/svg">\n'
    res += '<rect width="512" height="512" fill="white" stroke="none"/>\n'
    
    svg_data = svg_data.replace("style=", "<path style=\"")
    svg_data = re.sub(r"stroke:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = re.sub(r"fill:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = svg_data.replace("\t", "\" d=\"")
    svg_data = svg_data.replace("\n", "Z\" />\n")
    
    res += svg_data
    res += "</svg>"
    
    logger.debug(f"🔧 de_parser output: {len(res)} chars")
    return res

def svg_to_png_cairosvg_WORKING(svg_content, output_path, size=224):
    """
    Conversione SVG→PNG con CairoSVG (metodo che funziona)
    """
    try:
        logger.debug(f"🔧 CairoSVG: Converting {len(svg_content)} chars to {output_path}")
        
        import cairosvg
        # Conversione con CairoSVG - parametri ottimizzati
        cairosvg.svg2png(
            bytestring=svg_content.encode('utf-8'),
            write_to=output_path,
            output_width=size,
            output_height=size,
            background_color='white',  # Sfondo bianco garantito
            dpi=150  # DPI alta per qualità ottimale
        )
        
        success = os.path.exists(output_path)
        if success:
            file_size = os.path.getsize(output_path)
            logger.debug(f"✅ CairoSVG success: {output_path} ({file_size} bytes)")
        else:
            logger.error(f"❌ CairoSVG: File not created: {output_path}")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ Errore CairoSVG: {e}")
        import traceback
        logger.debug(f"🔍 CairoSVG traceback: {traceback.format_exc()}")
        return False

def load_dataset_debug(dataset_file, max_examples=10):
    """Carica dataset con limite per debug"""
    logger.info(f"🐛 DEBUG: Caricamento dataset limitato a {max_examples} esempi")
    logger.info(f"📊 Dataset file: {dataset_file}")
    
    with open(dataset_file, 'r') as f:
        data = json.load(f)
    
    logger.info(f"✅ Dataset caricato: {len(data)} esempi totali")
    
    # Limita per debug
    data = data[:max_examples]
    logger.info(f"🐛 DEBUG: Limitato a {len(data)} esempi")
    
    # Verifica che i dati abbiano XML content
    valid_data = []
    missing_xml = 0
    
    for i, example in enumerate(data):
        # Prova diversi campi per XML content
        xml_content = example.get('xml_content', '') or example.get('xml', '')
        caption = example.get('caption', '')
        
        logger.debug(f"🔍 Esempio {i}: XML={len(xml_content)} chars, Caption={len(caption)} chars")
        
        if xml_content and caption:
            valid_data.append({
                'id': example.get('id', len(valid_data)),
                'xml': xml_content,
                'caption': caption,
                'filename': example.get('filename', f"example_{len(valid_data)}")
            })
            logger.debug(f"✅ Esempio {i}: Valido")
        else:
            missing_xml += 1
            logger.warning(f"❌ Esempio {i}: XML o caption mancante")
    
    logger.info(f"✅ Esempi validi: {len(valid_data)}")
    if missing_xml > 0:
        logger.warning(f"⚠️ Esempi con XML/caption mancanti: {missing_xml}")
    
    return valid_data

class GemmaEvaluatorDebug:
    """Valutatore DEBUG per modello Gemma fine-tuned"""
    
    def __init__(self, force_cpu=True):
        self.device = 'cpu' if force_cpu else ('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"🖥️ Usando device: {self.device}")
        
        if self.device == 'cpu':
            logger.info("🔧 Modalità CPU: Ottimizzazioni memoria attivate")
    
    def load_gemma_model(self, checkpoint_path):
        """Carica modello Gemma con LoRA adapter"""
        logger.info(f"📥 Caricamento Gemma da checkpoint: {checkpoint_path}")
        
        try:
            # Base model Gemma
            base_model_name = "google/gemma-2-9b-it"
            logger.debug(f"🔧 Base model: {base_model_name}")
            
            # Carica tokenizer
            logger.info("📥 Caricamento tokenizer...")
            tokenizer = AutoTokenizer.from_pretrained(base_model_name)
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            logger.debug(f"✅ Tokenizer: vocab_size={tokenizer.vocab_size}")
            
            # Carica base model con ottimizzazioni CPU
            logger.info("📥 Caricamento base model...")
            if self.device == 'cpu':
                base_model = AutoModelForCausalLM.from_pretrained(
                    base_model_name,
                    torch_dtype=torch.float32,  # CPU usa float32
                    device_map=None,
                    low_cpu_mem_usage=True,
                    trust_remote_code=True
                )
                base_model = base_model.to('cpu')
                logger.debug("✅ Base model caricato su CPU")
            else:
                base_model = AutoModelForCausalLM.from_pretrained(
                    base_model_name,
                    torch_dtype=torch.float16,
                    device_map="auto",
                    trust_remote_code=True
                )
                logger.debug("✅ Base model caricato su GPU")
            
            # Carica LoRA adapter
            logger.info("📥 Caricamento LoRA adapter...")
            model = PeftModel.from_pretrained(base_model, checkpoint_path)
            logger.debug("✅ LoRA adapter caricato")
            
            # Merge adapter per efficienza
            logger.info("🔧 Merge LoRA adapter...")
            model = model.merge_and_unload()
            logger.debug("✅ LoRA adapter merged")
            
            if self.device == 'cpu':
                model = model.to('cpu')
            
            logger.info(f"✅ Gemma caricato su {self.device}")
            return tokenizer, model
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento Gemma: {e}")
            import traceback
            logger.debug(f"🔍 Traceback: {traceback.format_exc()}")
            raise
    
    def generate_caption_debug(self, tokenizer, model, xml_content, example_id=0):
        """Genera caption per un SVG XML con debug esteso"""
        logger.debug(f"🎯 Generando caption per esempio {example_id}")
        logger.debug(f"🔍 XML input: {len(xml_content)} chars")
        logger.debug(f"🔍 XML preview: {xml_content[:100]}...")
        
        try:
            # Prompt Gemma format
            prompt = f"<bos><start_of_turn>user\nDescribe this SVG:\n{xml_content}<end_of_turn>\n<start_of_turn>model\n"
            logger.debug(f"🔧 Prompt length: {len(prompt)} chars")
            
            # Tokenize
            logger.debug("🔧 Tokenizing...")
            inputs = tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=1024,  # Ridotto per memoria
                padding=False
            ).to(self.device)
            
            input_ids_len = inputs['input_ids'].shape[1]
            logger.debug(f"🔧 Input tokens: {input_ids_len}")
            
            # Generate con parametri ottimizzati per memoria
            logger.debug("🔧 Generating...")
            start_time = time.time()
            
            with torch.no_grad():
                generated_ids = model.generate(
                    **inputs,
                    max_new_tokens=150,      # Ridotto per memoria
                    num_beams=2,             # Ridotto per memoria
                    do_sample=True,
                    temperature=0.7,
                    top_p=0.9,
                    repetition_penalty=1.1,
                    pad_token_id=tokenizer.eos_token_id,
                    early_stopping=True,
                    no_repeat_ngram_size=3
                )
            
            generation_time = time.time() - start_time
            logger.debug(f"🔧 Generation time: {generation_time:.2f}s")
            
            # Decode
            logger.debug("🔧 Decoding...")
            generated_text = tokenizer.decode(generated_ids[0], skip_special_tokens=True)
            logger.debug(f"🔧 Generated text length: {len(generated_text)} chars")
            
            # Estrai solo la caption (rimuovi prompt)
            if "<start_of_turn>model\n" in generated_text:
                caption = generated_text.split("<start_of_turn>model\n")[-1].strip()
            else:
                caption = generated_text.replace(prompt, "").strip()
            
            logger.debug(f"✅ Caption estratta: {len(caption)} chars")
            logger.info(f"📝 Caption esempio {example_id}: {caption[:100]}...")
            
            return caption
            
        except Exception as e:
            logger.error(f"❌ Errore generazione esempio {example_id}: {e}")
            import traceback
            logger.debug(f"🔍 Traceback: {traceback.format_exc()}")
            return f"ERROR: {str(e)}"
    
    def evaluate_gemma_debug(self, checkpoint_path, dataset, output_file):
        """Valuta Gemma su dataset limitato con debug esteso"""
        logger.info("🐛 DEBUG: Iniziando valutazione Gemma T9...")
        logger.info(f"🐛 DEBUG: {len(dataset)} esempi da processare")
        
        try:
            # Carica modello
            tokenizer, model = self.load_gemma_model(checkpoint_path)
            
            results = []
            start_time = time.time()
            
            for i, example in enumerate(dataset):
                logger.info(f"🔄 Processando esempio {i+1}/{len(dataset)}")
                
                try:
                    # Genera caption
                    generated_caption = self.generate_caption_debug(
                        tokenizer, model, example['xml'], i
                    )
                    
                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'xml_content': example['xml'],
                        'ground_truth': example['caption'],
                        'generated_caption': generated_caption
                    })
                    
                    # Progress dettagliato
                    elapsed = time.time() - start_time
                    avg_time = elapsed / (i + 1)
                    remaining = avg_time * (len(dataset) - i - 1)
                    
                    logger.info(f"📊 Progress: {i+1}/{len(dataset)} "
                              f"({(i+1)/len(dataset)*100:.1f}%) - "
                              f"Avg: {avg_time:.1f}s/esempio - "
                              f"ETA: {remaining/60:.1f}min")
                    
                    # Pulizia memoria ogni esempio (più frequente per debug)
                    clear_memory()
                
                except Exception as e:
                    logger.error(f"❌ Errore esempio {i}: {e}")
                    import traceback
                    logger.debug(f"🔍 Traceback: {traceback.format_exc()}")
                    
                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'xml_content': example['xml'],
                        'ground_truth': example['caption'],
                        'generated_caption': f"ERROR: {str(e)}"
                    })
            
            # Salva risultati
            checkpoint_name = os.path.basename(checkpoint_path)
            output_data = {
                'model': 'Gemma-T9-DEBUG',
                'checkpoint': checkpoint_name,
                'total_examples': len(dataset),
                'successful_examples': len([r for r in results if not r['generated_caption'].startswith('ERROR')]),
                'timestamp': datetime.now().isoformat(),
                'device': self.device,
                'debug_mode': True,
                'results': results
            }
            
            with open(output_file, 'w') as f:
                json.dump(output_data, f, indent=2)
            
            total_time = time.time() - start_time
            logger.info(f"✅ DEBUG: Gemma completato in {total_time/60:.1f}min")
            logger.info(f"💾 Risultati salvati: {output_file}")
            logger.info(f"📊 Successi: {output_data['successful_examples']}/{len(dataset)}")
            
            # Pulizia finale
            del model, tokenizer
            clear_memory()
            
            return output_data
            
        except Exception as e:
            logger.error(f"❌ Errore Gemma evaluation DEBUG: {e}")
            import traceback
            logger.debug(f"🔍 Traceback: {traceback.format_exc()}")
            return None

def main():
    parser = argparse.ArgumentParser(description='Gemma Evaluation DEBUG')
    parser.add_argument('--checkpoint', 
                       default='experiments/xml_direct_input/outputs/gemma_t9_continue/checkpoint-15500',
                       help='Path al checkpoint Gemma')
    parser.add_argument('--dataset', 
                       default='data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json',
                       help='Path al dataset JSON')
    parser.add_argument('--output_dir', 
                       default='evaluation_results/gemma_DEBUG',
                       help='Directory output')
    parser.add_argument('--max_examples', 
                       type=int,
                       default=5,
                       help='Numero massimo di esempi per debug')
    parser.add_argument('--force_cpu', 
                       action='store_true',
                       default=True,
                       help='Forza modalità CPU')
    
    args = parser.parse_args()
    
    logger.info("🐛 GEMMA EVALUATION DEBUG")
    logger.info("=" * 50)
    logger.info(f"🔧 Checkpoint: {args.checkpoint}")
    logger.info(f"📊 Dataset: {args.dataset}")
    logger.info(f"📁 Output: {args.output_dir}")
    logger.info(f"🐛 Max esempi: {args.max_examples}")
    logger.info(f"🖥️ Force CPU: {args.force_cpu}")
    logger.info("=" * 50)
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Carica dataset limitato
    dataset = load_dataset_debug(args.dataset, args.max_examples)
    if not dataset:
        logger.error("❌ Impossibile caricare il dataset")
        return
    
    # Inizializza evaluator
    evaluator = GemmaEvaluatorDebug(force_cpu=args.force_cpu)
    
    # Valuta Gemma
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    checkpoint_name = os.path.basename(args.checkpoint)
    output_file = os.path.join(args.output_dir, f'gemma_t9_DEBUG_{checkpoint_name}_{timestamp}.json')
    
    result = evaluator.evaluate_gemma_debug(args.checkpoint, dataset, output_file)
    
    if result:
        logger.info("🎉 DEBUG: Gemma evaluation completata!")
        logger.info(f"📊 Risultati: {result['successful_examples']}/{result['total_examples']} successi")
    else:
        logger.error("❌ DEBUG: Gemma evaluation fallita!")

if __name__ == "__main__":
    main()
