#!/usr/bin/env python3
"""
📊 CREAZIONE RADAR CHART COMPLETO - 400 ESEMPI
Crea grafici radar per confrontare i modelli baseline su dataset completo
"""

import os
import json
import argparse
import logging
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
from math import pi

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Configurazione matplotlib per font e stile
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.linewidth'] = 1.2

def load_metrics(metrics_file):
    """Carica le metriche da un file JSON"""
    with open(metrics_file, 'r') as f:
        return json.load(f)

def normalize_metrics(metrics_dict, metric_ranges):
    """Normalizza le metriche usando i range appropriati"""
    normalized = {}
    
    for model_name, metrics in metrics_dict.items():
        normalized[model_name] = {}
        
        for metric_name, value in metrics.items():
            if metric_name in metric_ranges:
                min_val, max_val = metric_ranges[metric_name]
                # Normalizza a percentuale (0-100)
                normalized_val = ((value - min_val) / (max_val - min_val)) * 100
                normalized_val = max(0, min(100, normalized_val))  # Clamp tra 0-100
                normalized[model_name][metric_name] = normalized_val
    
    return normalized

def create_radar_chart(metrics_dict, output_file, title="Confronto Modelli Baseline"):
    """Crea un grafico radar per confrontare i modelli"""
    logger.info(f"📊 Creazione radar chart: {title}")
    
    # Definisci metriche e range appropriati
    metric_info = {
        'BLEU-1': {'key': 'bleu_1', 'range': (0, 0.5), 'format': '{:.3f}'},
        'BLEU-2': {'key': 'bleu_2', 'range': (0, 0.3), 'format': '{:.3f}'},
        'BLEU-3': {'key': 'bleu_3', 'range': (0, 0.2), 'format': '{:.3f}'},
        'BLEU-4': {'key': 'bleu_4', 'range': (0, 0.1), 'format': '{:.3f}'},
        'METEOR': {'key': 'meteor', 'range': (0, 0.5), 'format': '{:.3f}'},
        'CIDEr': {'key': 'cider', 'range': (0, 2.0), 'format': '{:.3f}'},
        'CLIPScore': {'key': 'clip_score', 'range': (0, 100), 'format': '{:.1f}'}
    }
    
    # Estrai valori per ogni modello
    model_data = {}
    available_metrics = []
    
    for model_name, metrics in metrics_dict.items():
        model_data[model_name] = {}
        
        for metric_label, info in metric_info.items():
            metric_key = info['key']
            if metric_key in metrics:
                value = metrics[metric_key].get('mean', 0) if isinstance(metrics[metric_key], dict) else metrics[metric_key]
                model_data[model_name][metric_label] = value
                if metric_label not in available_metrics:
                    available_metrics.append(metric_label)
    
    if not available_metrics:
        logger.error("❌ Nessuna metrica disponibile per il radar chart")
        return
    
    # Normalizza i valori
    metric_ranges = {label: metric_info[label]['range'] for label in available_metrics}
    normalized_data = {}
    
    for model_name, metrics in model_data.items():
        normalized_data[model_name] = {}
        for metric_label in available_metrics:
            if metric_label in metrics:
                value = metrics[metric_label]
                min_val, max_val = metric_ranges[metric_label]
                normalized_val = ((value - min_val) / (max_val - min_val)) * 100
                normalized_val = max(0, min(100, normalized_val))
                normalized_data[model_name][metric_label] = normalized_val
    
    # Setup del grafico
    fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))
    
    # Angoli per ogni metrica
    angles = [n / float(len(available_metrics)) * 2 * pi for n in range(len(available_metrics))]
    angles += angles[:1]  # Chiudi il cerchio
    
    # Colori per i modelli
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']
    
    # Disegna ogni modello
    for i, (model_name, metrics) in enumerate(normalized_data.items()):
        values = []
        for metric_label in available_metrics:
            values.append(metrics.get(metric_label, 0))
        values += values[:1]  # Chiudi il cerchio
        
        # Disegna linea e area
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    # Personalizza il grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(available_metrics, fontsize=11)
    ax.set_ylim(0, 100)
    
    # Griglia radiale
    ax.set_yticks([20, 40, 60, 80, 100])
    ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'], fontsize=9)
    ax.grid(True, alpha=0.3)
    
    # Titolo e legenda
    plt.title(title, size=16, fontweight='bold', pad=20)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=10)
    
    # Aggiungi valori reali come testo
    info_text = "Valori Reali:\n"
    for model_name, metrics in model_data.items():
        info_text += f"\n{model_name}:\n"
        for metric_label in available_metrics:
            if metric_label in metrics:
                value = metrics[metric_label]
                format_str = metric_info[metric_label]['format']
                info_text += f"  {metric_label}: {format_str.format(value)}\n"
    
    # Aggiungi box con valori reali
    plt.figtext(0.02, 0.02, info_text, fontsize=8, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # Salva grafico
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    plt.savefig(output_file.replace('.png', '.pdf'), bbox_inches='tight', facecolor='white')
    plt.close()
    
    logger.info(f"✅ Radar chart salvato: {output_file}")
    logger.info(f"✅ Radar chart PDF salvato: {output_file.replace('.png', '.pdf')}")

def main():
    parser = argparse.ArgumentParser(description='Creazione Radar Chart Baseline Completo')
    parser.add_argument('--metrics_dir', 
                       default='evaluation_results/baseline_COMPLETE_400',
                       help='Directory con le metriche')
    parser.add_argument('--output_dir',
                       default='evaluation_results/baseline_COMPLETE_400',
                       help='Directory output per i grafici')
    parser.add_argument('--title',
                       default='CONFRONTO MODELLI BASELINE\n(Dataset Completo - 400 Esempi)',
                       help='Titolo del grafico')
    
    args = parser.parse_args()
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Trova tutti i file di metriche
    metrics_files = []
    for filename in os.listdir(args.metrics_dir):
        if filename.endswith('_metrics_*.json') and not filename.startswith('baseline_comparison'):
            metrics_files.append(os.path.join(args.metrics_dir, filename))
    
    if not metrics_files:
        logger.error(f"❌ Nessun file di metriche trovato in {args.metrics_dir}")
        return
    
    logger.info(f"📁 Trovati {len(metrics_files)} file di metriche")
    
    # Carica tutte le metriche
    all_metrics = {}
    
    for metrics_file in metrics_files:
        try:
            metrics = load_metrics(metrics_file)
            model_name = metrics.get('metadata', {}).get('model', 'Unknown')
            
            # Estrai solo i valori medi delle metriche
            model_metrics = {}
            for key, value in metrics.items():
                if key != 'metadata' and isinstance(value, dict) and 'mean' in value:
                    model_metrics[key] = value['mean']
            
            if model_metrics:
                all_metrics[model_name] = model_metrics
                logger.info(f"✅ Caricato {model_name}: {len(model_metrics)} metriche")
        
        except Exception as e:
            logger.error(f"❌ Errore caricamento {metrics_file}: {e}")
    
    if not all_metrics:
        logger.error("❌ Nessuna metrica caricata")
        return
    
    # Crea radar chart
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(args.output_dir, f'baseline_radar_complete_400_{timestamp}.png')
    
    create_radar_chart(all_metrics, output_file, args.title)
    
    logger.info(f"🎉 Radar chart creato per {len(all_metrics)} modelli!")

if __name__ == "__main__":
    main()
