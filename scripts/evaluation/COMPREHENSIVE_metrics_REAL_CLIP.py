#!/usr/bin/env python3
"""
🎯 COMPREHENSIVE METRICS WITH REAL CLIP
Calcola TUTTE le metriche con VERO CLIPScore (no pseudo-CLIP)
Include: BLEU, ROUGE, METEOR, BERTScore, CIDEr, REAL CLIP
"""

import os
import json
import argparse
import logging
import tempfile
import shutil
from datetime import datetime
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_dependencies():
    """Installa dipendenze necessarie"""
    try:
        import nltk
        from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
        from nltk.translate.meteor_score import meteor_score
        from rouge_score import rouge_scorer
        from bert_score import score as bert_score
        from pycocoevalcap.cider.cider import Cider
        
        # Download NLTK data
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')
        
        try:
            nltk.data.find('corpora/wordnet')
        except LookupError:
            nltk.download('wordnet')
            
        return True
    except ImportError as e:
        logger.error(f"❌ Dipendenze mancanti: {e}")
        logger.error("Installa: pip install nltk rouge-score bert-score pycocoevalcap")
        return False

def calculate_bleu_scores(predictions, references):
    """Calcola BLEU-1,2,3,4"""
    import nltk
    from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
    
    bleu_scores = {'bleu_1': [], 'bleu_2': [], 'bleu_3': [], 'bleu_4': []}
    smoothing = SmoothingFunction().method1
    
    for pred, ref in zip(predictions, references):
        pred_tokens = nltk.word_tokenize(pred.lower())
        ref_tokens = [nltk.word_tokenize(ref.lower())]
        
        bleu_scores['bleu_1'].append(sentence_bleu(ref_tokens, pred_tokens, weights=(1,0,0,0), smoothing_function=smoothing))
        bleu_scores['bleu_2'].append(sentence_bleu(ref_tokens, pred_tokens, weights=(0.5,0.5,0,0), smoothing_function=smoothing))
        bleu_scores['bleu_3'].append(sentence_bleu(ref_tokens, pred_tokens, weights=(0.33,0.33,0.33,0), smoothing_function=smoothing))
        bleu_scores['bleu_4'].append(sentence_bleu(ref_tokens, pred_tokens, weights=(0.25,0.25,0.25,0.25), smoothing_function=smoothing))
    
    return {k: np.mean(v) for k, v in bleu_scores.items()}

def calculate_rouge_scores(predictions, references):
    """Calcola ROUGE-L"""
    from rouge_score import rouge_scorer
    
    scorer = rouge_scorer.RougeScorer(['rougeL'], use_stemmer=True)
    rouge_scores = []
    
    for pred, ref in zip(predictions, references):
        scores = scorer.score(ref, pred)
        rouge_scores.append(scores['rougeL'].fmeasure)
    
    return np.mean(rouge_scores)

def calculate_meteor_scores(predictions, references):
    """Calcola METEOR"""
    import nltk
    from nltk.translate.meteor_score import meteor_score
    
    meteor_scores = []
    for pred, ref in zip(predictions, references):
        try:
            score = meteor_score([nltk.word_tokenize(ref.lower())], nltk.word_tokenize(pred.lower()))
            meteor_scores.append(score)
        except:
            meteor_scores.append(0.0)
    
    return np.mean(meteor_scores)

def calculate_bert_scores(predictions, references):
    """Calcola BERTScore"""
    try:
        from bert_score import score as bert_score
        
        P, R, F1 = bert_score(predictions, references, lang="en", verbose=False)
        
        return {
            'bert_precision': float(P.mean()),
            'bert_recall': float(R.mean()),
            'bert_f1': float(F1.mean())
        }
    except Exception as e:
        logger.warning(f"❌ BERTScore error: {e}")
        return {'bert_precision': 0.0, 'bert_recall': 0.0, 'bert_f1': 0.0}

def calculate_cider_scores(predictions, references):
    """Calcola CIDEr"""
    try:
        from pycocoevalcap.cider.cider import Cider
        
        # Formato richiesto da CIDEr: {id: [caption]}
        gts = {i: [ref] for i, ref in enumerate(references)}
        res = {i: [pred] for i, pred in enumerate(predictions)}
        
        cider_scorer = Cider()
        score, scores = cider_scorer.compute_score(gts, res)
        
        return float(score)
    except Exception as e:
        logger.warning(f"❌ CIDEr error: {e}")
        # Fallback: approssimazione basata su BLEU-4
        bleu_scores = calculate_bleu_scores(predictions, references)
        return bleu_scores.get('bleu_4', 0.0) * 2.0

def setup_clip_transformers():
    """Setup CLIP usando transformers"""
    try:
        from transformers import CLIPProcessor, CLIPModel
        import torch
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"🔧 CLIP su device: {device}")
        
        model_name = "openai/clip-vit-base-patch32"
        model = CLIPModel.from_pretrained(model_name).to(device)
        processor = CLIPProcessor.from_pretrained(model_name)
        logger.info(f"✅ Caricato {model_name}")
        
        return model, processor, device
    except Exception as e:
        logger.error(f"❌ Errore setup CLIP: {e}")
        return None, None, None

def calculate_real_clip_scores(image_paths, captions, model, processor, device):
    """Calcola VERO CLIP Score usando raw logits"""
    import torch
    from PIL import Image
    
    clip_scores = []
    
    for img_path, caption in zip(image_paths, captions):
        try:
            if not os.path.exists(img_path):
                clip_scores.append(0.0)
                continue
                
            image = Image.open(img_path).convert('RGB')
            
            # Pulisci caption
            clean_caption = caption.strip()
            if not clean_caption:
                clip_scores.append(0.0)
                continue
            
            # Calcola CLIP Score con truncation
            inputs = processor(text=[clean_caption], images=image, return_tensors="pt", padding=True, truncation=True, max_length=77)
            inputs = {k: v.to(device) for k, v in inputs.items()}
            
            with torch.no_grad():
                outputs = model(**inputs)
                logits_per_image = outputs.logits_per_image
                clip_score = torch.diagonal(logits_per_image).cpu().numpy()[0]
                
                # USA RAW LOGIT (no sigmoid)
                clip_scores.append(float(clip_score))
                
        except Exception as e:
            logger.warning(f"❌ CLIP error per {img_path}: {e}")
            clip_scores.append(0.0)
    
    return clip_scores

def convert_custom_xml_to_svg(xml_content):
    """Converte il nostro formato XML custom a SVG standard"""
    try:
        # Il nostro formato: style=fill:rgb(0,0,0);stroke:None;stroke-width:3;opacity:1\td=M488,177 C446,137...
        lines = xml_content.strip().split('\n')
        svg_paths = []

        for line in lines:
            if '\t' in line:
                style_part, d_part = line.split('\t', 1)

                # Estrai attributi style
                style_attrs = {}
                if 'fill:rgb(' in style_part:
                    # Converti rgb(r,g,b) a #rrggbb
                    import re
                    rgb_match = re.search(r'fill:rgb\((\d+),(\d+),(\d+)\)', style_part)
                    if rgb_match:
                        r, g, b = rgb_match.groups()
                        style_attrs['fill'] = f"#{int(r):02x}{int(g):02x}{int(b):02x}"

                if 'stroke:rgb(' in style_part:
                    rgb_match = re.search(r'stroke:rgb\((\d+),(\d+),(\d+)\)', style_part)
                    if rgb_match:
                        r, g, b = rgb_match.groups()
                        style_attrs['stroke'] = f"#{int(r):02x}{int(g):02x}{int(b):02x}"
                elif 'stroke:None' in style_part:
                    style_attrs['stroke'] = 'none'

                if 'stroke-width:' in style_part:
                    width_match = re.search(r'stroke-width:([^;]+)', style_part)
                    if width_match:
                        style_attrs['stroke-width'] = width_match.group(1)

                if 'opacity:' in style_part:
                    opacity_match = re.search(r'opacity:([^;]+)', style_part)
                    if opacity_match:
                        style_attrs['opacity'] = opacity_match.group(1)

                # Estrai path data
                if d_part.startswith('d='):
                    path_data = d_part[2:]  # Rimuovi 'd='

                    # Crea elemento path SVG
                    style_str = ';'.join([f"{k}:{v}" for k, v in style_attrs.items()])
                    svg_paths.append(f'<path d="{path_data}" style="{style_str}"/>')

        # Crea SVG completo
        svg_content = f'''<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
{chr(10).join(svg_paths)}
</svg>'''

        return svg_content

    except Exception as e:
        logger.warning(f"❌ XML to SVG conversion error: {e}")
        return None

def convert_single_svg_FIXED(xml_content, output_path, size=224):
    """Converte SVG a PNG usando CairoSVG"""
    try:
        import cairosvg

        # Se non è SVG standard, converti dal nostro formato
        if not xml_content.startswith('<svg'):
            xml_content = convert_custom_xml_to_svg(xml_content)
            if xml_content is None:
                return False

        # Fix colori SVG standard
        if 'fill:' in xml_content and '#' not in xml_content:
            xml_content = xml_content.replace('fill:0,0,0', 'fill:#000000')
            xml_content = xml_content.replace('fill:255,255,255', 'fill:#ffffff')

        cairosvg.svg2png(
            bytestring=xml_content.encode('utf-8'),
            write_to=output_path,
            output_width=size,
            output_height=size,
            background_color='white'
        )

        return os.path.exists(output_path)
    except Exception as e:
        logger.warning(f"❌ SVG conversion error: {e}")
        return False

def calculate_comprehensive_metrics(results_file, model_name, max_examples=400):
    """Calcola TUTTE le metriche comprehensive"""
    logger.info(f"🎯 Calcolo COMPREHENSIVE METRICS per {model_name}")
    
    # Verifica dipendenze
    if not install_dependencies():
        return None
    
    # Carica risultati
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    # Gestisci diversi formati
    if isinstance(data, list):
        results = data
    else:
        results = data.get('results', [])
    
    if len(results) > max_examples:
        results = results[:max_examples]
        logger.info(f"📊 Limitato a {max_examples} esempi")
    
    logger.info(f"📊 Processando {len(results)} esempi...")
    
    # Prepara dati
    predictions = []
    references = []
    image_paths = []
    
    # Directory temporanea
    temp_dir = tempfile.mkdtemp(prefix="comprehensive_metrics_")
    
    successful_conversions = 0
    
    for i, result in enumerate(results):
        try:
            # Gestisci diversi formati di risultati
            xml_content = result.get('xml_content', '') or result.get('xml', '')
            generated_caption = result.get('generated_caption', '') or result.get('prediction', '')
            ground_truth = result.get('ground_truth', '') or result.get('caption', '')

            # Per dataset senza generated_caption, usa ground_truth come prediction (per test)
            if not generated_caption and ground_truth:
                generated_caption = ground_truth
                logger.info(f"📊 Usando ground_truth come prediction per esempio {i}")

            if not generated_caption or not ground_truth:
                logger.warning(f"⚠️ Esempio {i} mancante dati: gen={bool(generated_caption)}, gt={bool(ground_truth)}")
                continue

            predictions.append(generated_caption)
            references.append(ground_truth)
            
            # Per CLIP: usa immagine esistente o genera da XML
            existing_image_path = result.get('image_path', '')
            example_id = result.get('example_id', i)

            # Prova prima immagine esistente
            if existing_image_path and os.path.exists(existing_image_path):
                # Usa immagine esistente (per baseline models)
                image_paths.append(existing_image_path)
                successful_conversions += 1
                logger.debug(f"✅ Usata immagine esistente per esempio {i}: {existing_image_path}")
            else:
                # Prova immagine pre-generata nella cartella baseline_images_FIXED
                baseline_img_path = f"evaluation_results/baseline_images_FIXED/baseline_image_{example_id:03d}.png"
                if os.path.exists(baseline_img_path):
                    image_paths.append(baseline_img_path)
                    successful_conversions += 1
                    logger.debug(f"✅ Usata immagine baseline per esempio {i}: {baseline_img_path}")
                elif xml_content and (xml_content.startswith('<') or 'style=' in xml_content):
                    # Genera immagine da XML (per trained models)
                    img_path = os.path.join(temp_dir, f"img_{i}.png")
                    if convert_single_svg_FIXED(xml_content, img_path):
                        image_paths.append(img_path)
                        successful_conversions += 1
                        logger.debug(f"✅ Convertito esempio {i}")
                    else:
                        image_paths.append("")
                        logger.debug(f"❌ Conversione fallita esempio {i}")
                else:
                    image_paths.append("")
                    logger.debug(f"⚠️ Nessun XML o immagine per esempio {i}")
                
        except Exception as e:
            logger.warning(f"❌ Errore esempio {i}: {e}")
            continue
    
    logger.info(f"📊 Dati preparati: {len(predictions)} esempi, {successful_conversions} immagini")
    
    if not predictions:
        logger.error("❌ Nessun dato valido trovato!")
        return None
    
    # Calcola metriche
    metrics = {}
    
    logger.info("📊 Calcolando BLEU scores...")
    bleu_scores = calculate_bleu_scores(predictions, references)
    metrics.update(bleu_scores)
    
    logger.info("📊 Calcolando ROUGE-L...")
    metrics['rouge_l'] = calculate_rouge_scores(predictions, references)
    
    logger.info("📊 Calcolando METEOR...")
    metrics['meteor'] = calculate_meteor_scores(predictions, references)
    
    logger.info("📊 Calcolando BERTScore...")
    bert_scores = calculate_bert_scores(predictions, references)
    metrics.update(bert_scores)
    
    logger.info("📊 Calcolando CIDEr...")
    metrics['cider'] = calculate_cider_scores(predictions, references)
    
    # VERO CLIP Score
    if successful_conversions > 0:
        logger.info("📊 Calcolando VERO CLIP Score...")
        model, processor, device = setup_clip_transformers()
        if model is not None:
            # Solo per esempi con immagini valide
            valid_images = [(img, cap) for img, cap in zip(image_paths, predictions) if img and os.path.exists(img)]
            if valid_images:
                valid_img_paths, valid_captions = zip(*valid_images)
                clip_scores = calculate_real_clip_scores(valid_img_paths, valid_captions, model, processor, device)
                
                valid_clip_scores = [s for s in clip_scores if s > 0]
                if valid_clip_scores:
                    metrics['real_clip_score'] = {
                        'mean': float(np.mean(valid_clip_scores)),
                        'std': float(np.std(valid_clip_scores)),
                        'min': float(np.min(valid_clip_scores)),
                        'max': float(np.max(valid_clip_scores)),
                        'count': len(valid_clip_scores)
                    }
                else:
                    metrics['real_clip_score'] = {'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0, 'count': 0}
            else:
                metrics['real_clip_score'] = {'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0, 'count': 0}
        else:
            logger.warning("❌ CLIP non disponibile")
            metrics['real_clip_score'] = {'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0, 'count': 0}
    else:
        logger.warning("❌ Nessuna immagine per CLIP")
        metrics['real_clip_score'] = {'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0, 'count': 0}
    
    # Pulizia
    try:
        shutil.rmtree(temp_dir)
    except:
        pass
    
    # Risultati finali
    final_metrics = {
        'model': model_name,
        'total_examples': len(results),
        'processed_examples': len(predictions),
        'successful_conversions': successful_conversions,
        'timestamp': datetime.now().isoformat(),
        'metrics': metrics
    }
    
    return final_metrics

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Calcola comprehensive metrics con VERO CLIP")
    parser.add_argument("--results_file", required=True, help="File risultati JSON")
    parser.add_argument("--model_name", required=True, help="Nome modello")
    parser.add_argument("--output_dir", default="evaluation_results/comprehensive_metrics", help="Directory output")
    parser.add_argument("--max_examples", type=int, default=400, help="Max esempi da processare")

    args = parser.parse_args()

    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)

    # Calcola metriche
    metrics = calculate_comprehensive_metrics(args.results_file, args.model_name, args.max_examples)

    if metrics is None:
        logger.error("❌ Calcolo metriche fallito!")
        return

    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(args.output_dir, f"{args.model_name}_comprehensive_metrics_{timestamp}.json")

    with open(output_file, 'w') as f:
        json.dump(metrics, f, indent=2)

    logger.info(f"✅ Metriche salvate: {output_file}")

    # Mostra riassunto
    print("\n" + "="*60)
    print(f"📊 COMPREHENSIVE METRICS - {args.model_name}")
    print("="*60)

    m = metrics['metrics']
    print(f"📈 BLEU-1: {m.get('bleu_1', 0):.4f}")
    print(f"📈 BLEU-2: {m.get('bleu_2', 0):.4f}")
    print(f"📈 BLEU-3: {m.get('bleu_3', 0):.4f}")
    print(f"📈 BLEU-4: {m.get('bleu_4', 0):.4f}")
    print(f"📈 ROUGE-L: {m.get('rouge_l', 0):.4f}")
    print(f"📈 METEOR: {m.get('meteor', 0):.4f}")
    print(f"📈 BERTScore F1: {m.get('bert_f1', 0):.4f}")
    print(f"📈 CIDEr: {m.get('cider', 0):.4f}")

    if 'real_clip_score' in m:
        clip_data = m['real_clip_score']
        print(f"📈 REAL CLIPScore: {clip_data.get('mean', 0):.2f} ± {clip_data.get('std', 0):.2f} ({clip_data.get('count', 0)} esempi)")

    print("="*60)
    print(f"📁 File: {output_file}")
    print(f"📊 Esempi processati: {metrics['processed_examples']}/{metrics['total_examples']}")
    print(f"🖼️ Conversioni riuscite: {metrics['successful_conversions']}")

if __name__ == "__main__":
    main()
