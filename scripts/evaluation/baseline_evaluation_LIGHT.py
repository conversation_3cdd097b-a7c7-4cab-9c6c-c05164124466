#!/usr/bin/env python3
"""
🎯 BASELINE EVALUATION LEGGERO
Usa modelli più piccoli e CPU per evitare problemi di memoria
"""

import os
import json
import argparse
import logging
import gc
from datetime import datetime
from PIL import Image
import torch
from transformers import BlipProcessor, BlipForConditionalGeneration

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def clear_memory():
    """Pulisce memoria"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()

def load_dataset_simple(dataset_file, max_examples=50):
    """Carica dataset semplice"""
    logger.info(f"📊 Caricamento dataset: {dataset_file}")
    
    with open(dataset_file, 'r') as f:
        data = json.load(f)
    
    # Limita esempi
    if max_examples and len(data) > max_examples:
        data = data[:max_examples]
    
    # Verifica che le immagini esistano
    valid_data = []
    for i, example in enumerate(data):
        image_path = example.get('image_path', '')
        caption = example.get('caption', '')
        
        if os.path.exists(image_path) and caption:
            valid_data.append({
                'id': i,
                'image_path': image_path,
                'caption': caption
            })
        else:
            logger.warning(f"⚠️ Esempio {i} non valido")
    
    logger.info(f"✅ Esempi validi: {len(valid_data)}")
    return valid_data

def run_blip_light(images_data, output_dir):
    """Esegue BLIP originale (più leggero) su CPU"""
    logger.info(f"🚀 BLIP Light: {len(images_data)} esempi")
    
    # Forza CPU per evitare problemi memoria GPU
    device = "cpu"
    logger.info(f"🖥️ Usando device: {device}")
    
    try:
        # Usa BLIP originale che è più leggero
        processor = BlipProcessor.from_pretrained("Salesforce/blip-image-captioning-base")
        model = BlipForConditionalGeneration.from_pretrained("Salesforce/blip-image-captioning-base")
        model.to(device)
        
        results = []
        
        for i, example in enumerate(images_data):
            try:
                # Carica immagine e ridimensiona per risparmiare memoria
                image = Image.open(example['image_path']).convert('RGB')
                image = image.resize((224, 224))  # Ridimensiona per memoria
                
                # Genera caption
                inputs = processor(image, return_tensors="pt").to(device)
                
                with torch.no_grad():
                    generated_ids = model.generate(
                        **inputs, 
                        max_length=50,
                        do_sample=False,
                        num_beams=1,
                        early_stopping=True
                    )
                
                generated_text = processor.decode(generated_ids[0], skip_special_tokens=True)
                
                result = {
                    'id': example['id'],
                    'image_path': example['image_path'],
                    'ground_truth': example['caption'],
                    'generated_caption': generated_text,
                    'model': 'blip_light'
                }
                
                results.append(result)
                
                # Log progresso ogni 5 esempi
                if (i + 1) % 5 == 0:
                    logger.info(f"   📈 BLIP Light: {i+1}/{len(images_data)} completati")
                    clear_memory()
                    
            except Exception as e:
                logger.error(f"❌ Errore BLIP Light esempio {example['id']}: {e}")
                clear_memory()
        
        # Salva risultati
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = os.path.join(output_dir, f"blip_light_results_{timestamp}.json")
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"✅ BLIP Light completato: {len(results)} risultati salvati in {results_file}")
        
        # Pulisci modello dalla memoria
        del model
        del processor
        clear_memory()
        
        return results_file
        
    except Exception as e:
        logger.error(f"❌ Errore caricamento BLIP Light: {e}")
        clear_memory()
        return None

def run_simple_baseline(images_data, output_dir):
    """Baseline semplice basato su regole"""
    logger.info(f"🚀 Simple Baseline: {len(images_data)} esempi")
    
    results = []
    
    for i, example in enumerate(images_data):
        try:
            # Baseline molto semplice: genera caption generica
            generated_text = "A simple black and white image with geometric shapes on a white background."
            
            result = {
                'id': example['id'],
                'image_path': example['image_path'],
                'ground_truth': example['caption'],
                'generated_caption': generated_text,
                'model': 'simple_baseline'
            }
            
            results.append(result)
            
            if (i + 1) % 10 == 0:
                logger.info(f"   📈 Simple Baseline: {i+1}/{len(images_data)} completati")
                
        except Exception as e:
            logger.error(f"❌ Errore Simple Baseline esempio {example['id']}: {e}")
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(output_dir, f"simple_baseline_results_{timestamp}.json")
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"✅ Simple Baseline completato: {len(results)} risultati salvati in {results_file}")
    return results_file

def main():
    parser = argparse.ArgumentParser(description="Baseline evaluation leggero")
    parser.add_argument("--dataset", required=True, help="Dataset JSON")
    parser.add_argument("--output_dir", required=True, help="Directory output")
    parser.add_argument("--model", required=True, choices=["blip_light", "simple"], help="Modello da usare")
    parser.add_argument("--max_examples", type=int, default=50, help="Numero massimo esempi")
    
    args = parser.parse_args()
    
    print("🎯 BASELINE EVALUATION LEGGERO")
    print("=" * 50)
    print(f"📊 Dataset: {args.dataset}")
    print(f"📁 Output: {args.output_dir}")
    print(f"🤖 Modello: {args.model}")
    print(f"📈 Max esempi: {args.max_examples}")
    print("=" * 50)
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Carica dataset
    images_data = load_dataset_simple(args.dataset, args.max_examples)
    
    if not images_data:
        logger.error("❌ Nessun dato valido trovato")
        return 1
    
    # Esegui modello
    if args.model == "blip_light":
        results_file = run_blip_light(images_data, args.output_dir)
    elif args.model == "simple":
        results_file = run_simple_baseline(images_data, args.output_dir)
    else:
        logger.error(f"❌ Modello non supportato: {args.model}")
        return 1
    
    if results_file:
        # Summary finale
        print("=" * 50)
        print("🎉 BASELINE EVALUATION LEGGERO COMPLETATO!")
        print("=" * 50)
        print(f"📊 Esempi processati: {len(images_data)}")
        print(f"📄 File risultati: {os.path.basename(results_file)}")
        print("=" * 50)
        
        return 0
    else:
        logger.error("❌ Valutazione fallita")
        return 1

if __name__ == "__main__":
    exit(main())
