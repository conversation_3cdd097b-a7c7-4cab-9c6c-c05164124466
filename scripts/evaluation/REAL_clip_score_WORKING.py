#!/usr/bin/env python3
"""
🎯 VERO CLIP SCORE CHE FUNZIONA
Usa il vero modello CLIP di OpenAI con caption troncate
"""

import os
import json
import argparse
import logging
import tempfile
import torch
from datetime import datetime
from PIL import Image
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_clip_transformers():
    """Setup CLIP usando transformers"""
    try:
        from transformers import CLIPProcessor, CLIPModel

        device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"🔧 CLIP su device: {device}")

        # Usa solo CLIP Base per evitare OOM
        model_names = ["openai/clip-vit-base-patch32"]

        for model_name in model_names:
            try:
                logger.info(f"🔄 Tentativo con {model_name}")
                model = CLIPModel.from_pretrained(model_name).to(device)
                processor = CLIPProcessor.from_pretrained(model_name)
                logger.info(f"✅ Caricato {model_name}")
                return model, processor, device
            except Exception as e:
                logger.warning(f"❌ Fallito {model_name}: {e}")
                continue

        return None, None, None
    except Exception as e:
        logger.error(f"❌ Errore setup CLIP: {e}")
        return None, None, None

def truncate_caption(caption, max_words=15):
    """Tronca caption per CLIP (max parole per stare sotto 77 token)"""
    words = caption.split()
    if len(words) <= max_words:
        return caption

    # Tronca a max_words parole
    truncated = ' '.join(words[:max_words])
    return truncated + "..."

def clean_gemma_caption(caption):
    """Pulisce caption di Gemma"""
    # Rimuovi prefissi
    if 'model\\n' in caption:
        caption = caption.split('model\\n')[1].strip()

    # Rimuovi suffissi comuni
    caption = caption.replace('\\n', ' ').strip()

    # Prendi solo la prima frase
    first_sentence = caption.split('.')[0]
    if first_sentence:
        caption = first_sentence + '.'

    # Tronca drasticamente a max 15 parole
    caption = truncate_caption(caption, max_words=15)

    return caption

def split_text_into_chunks(text, max_words=50):
    """Divide testo in chunk che CLIP può processare"""
    words = text.split()
    chunks = []

    for i in range(0, len(words), max_words):
        chunk = ' '.join(words[i:i + max_words])
        chunks.append(chunk)

    return chunks

def calculate_clip_score_real(image_path, text, model, processor, device):
    """Calcola vero CLIP Score su caption COMPLETA usando chunking"""
    try:
        image = Image.open(image_path).convert('RGB')

        # Pulisci testo ma NON troncare
        clean_text = text.replace('\\n', ' ').strip()
        if 'model\\n' in clean_text:
            clean_text = clean_text.split('model\\n')[1].strip()

        # Se testo troppo lungo, dividilo in chunk e calcola media
        words = clean_text.split()

        if len(words) <= 50:
            # Testo corto, calcolo diretto
            try:
                inputs = processor(text=[clean_text], images=image, return_tensors="pt", padding=True, truncation=True, max_length=77)
                inputs = {k: v.to(device) for k, v in inputs.items()}

                with torch.no_grad():
                    outputs = model(**inputs)
                    logits_per_image = outputs.logits_per_image
                    clip_score = torch.diagonal(logits_per_image).cpu().numpy()[0]

                    # USA IL RAW LOGIT COME NEGLI PAPER VERI
                    # Range tipico: -10 a +10, valori buoni ~2-4
                    return float(clip_score)
            except:
                # Fallback a chunk se fallisce
                pass

        # Testo lungo: dividi in chunk e calcola media
        chunks = split_text_into_chunks(clean_text, max_words=40)
        scores = []

        for chunk in chunks:
            try:
                inputs = processor(text=[chunk], images=image, return_tensors="pt", padding=True, truncation=True, max_length=77)
                inputs = {k: v.to(device) for k, v in inputs.items()}

                with torch.no_grad():
                    outputs = model(**inputs)
                    logits_per_image = outputs.logits_per_image
                    clip_score = torch.diagonal(logits_per_image).cpu().numpy()[0]

                    # USA IL RAW LOGIT COME NEGLI PAPER VERI
                    scores.append(float(clip_score))

            except Exception as e:
                logger.warning(f"Errore chunk: {e}")
                continue

        if scores:
            # Media pesata: primi chunk più importanti
            weights = [1.0] + [0.7] * (len(scores) - 1)
            weighted_avg = sum(s * w for s, w in zip(scores, weights)) / sum(weights)
            return float(weighted_avg)
        else:
            return 0.0

    except Exception as e:
        logger.warning(f"Errore CLIP Score: {e}")
        return 0.0

def parse_xml_to_svg_FIXED(xml_content):
    """Parser SVG MIGLIORATO che fixa tutti i problemi"""
    import re
    
    # Inizia SVG con header corretto
    svg_parts = []
    svg_parts.append('<?xml version="1.0" encoding="UTF-8"?>')
    svg_parts.append('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">')
    svg_parts.append('<rect width="512" height="512" fill="white"/>')  # Sfondo bianco
    
    # Split per linee
    lines = xml_content.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Cerca pattern style= e d=
        style_match = re.search(r'style=([^\\t]+)', line)
        d_match = re.search(r'd=(.+)', line)
        
        if style_match and d_match:
            style_content = style_match.group(1)
            d_content = d_match.group(1)
            
            # Fixa colori RGB
            style_content = re.sub(r'rgb\((\d+),(\d+),(\d+)\)', r'rgb(\1,\2,\3)', style_content)
            
            # Fixa formato colori: fill:0,0,0 -> fill:#000000
            def fix_color_format(match):
                r, g, b = match.groups()
                return f'fill:#{int(r):02x}{int(g):02x}{int(b):02x}'
            
            style_content = re.sub(r'fill:(\d+),(\d+),(\d+)', fix_color_format, style_content)
            
            # Crea path SVG
            svg_parts.append(f'<path style="{style_content}" d="{d_content}"/>')
    
    svg_parts.append('</svg>')
    return '\n'.join(svg_parts)

def convert_single_svg_FIXED(xml_content, output_path, size=224):
    """Converte singolo SVG in PNG usando metodo FIXED"""
    try:
        import cairosvg
        
        # Converti XML in SVG
        svg_content = parse_xml_to_svg_FIXED(xml_content)
        
        # Converti SVG in PNG
        cairosvg.svg2png(
            bytestring=svg_content.encode('utf-8'),
            write_to=output_path,
            output_width=size,
            output_height=size
        )
        
        # Verifica che il file esista
        if os.path.exists(output_path) and os.path.getsize(output_path) > 0:
            return True
        else:
            return False
            
    except Exception as e:
        logger.warning(f"Errore conversione SVG: {e}")
        return False

def calculate_real_clip_scores(results_file, model_name, max_examples=400):
    """Calcola REAL CLIP Scores con salvataggio progressivo"""
    logger.info(f"🎯 Calcolo REAL CLIP Scores per {model_name} (max {max_examples} esempi)")

    # Carica risultati
    with open(results_file, 'r') as f:
        data = json.load(f)

    # Gestisci diversi formati
    if isinstance(data, list):
        results = data
    else:
        results = data.get('results', [])

    if len(results) > max_examples:
        results = results[:max_examples]
        logger.info(f"📊 Limitato a {max_examples} esempi")

    # Setup CLIP
    model, processor, device = setup_clip_transformers()
    if model is None:
        return []

    # Directory temporanea
    temp_dir = tempfile.mkdtemp(prefix="real_clip_working_")

    clip_scores = []
    successful = 0

    # Salvataggio progressivo ogni 50 esempi
    batch_size = 50

    for i, result in enumerate(results):
        if (i + 1) % 10 == 0:
            logger.info(f"📈 Processando esempio {i+1}/{len(results)}...")

        try:
            # Gestisci diversi formati di risultati
            xml_content = result.get('xml_content', '') or result.get('xml', '') or result.get('ground_truth', '')
            generated_caption = result.get('generated_caption', '') or result.get('prediction', '')

            if not xml_content or not generated_caption:
                clip_scores.append(0.0)
                continue

            # Genera immagine PNG con metodo FIXED
            img_path = os.path.join(temp_dir, f"real_clip_{i}.png")

            if convert_single_svg_FIXED(xml_content, img_path, size=224):
                # Calcola REAL CLIP Score
                score = calculate_clip_score_real(img_path, generated_caption, model, processor, device)
                clip_scores.append(score)
                successful += 1

                # Rimuovi immagine subito per liberare spazio
                try:
                    os.remove(img_path)
                except:
                    pass
            else:
                clip_scores.append(0.0)

            # Salvataggio progressivo ogni batch_size esempi
            if (i + 1) % batch_size == 0:
                avg_score = np.mean([s for s in clip_scores if s > 0]) if clip_scores else 0
                logger.info(f"📊 Progress {i+1}/{len(results)} - Avg CLIP: {avg_score:.2f}")

                # Salva risultati parziali
                save_partial_results(clip_scores, model_name, i+1, len(results))

                # Forza garbage collection
                import gc
                gc.collect()

        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            clip_scores.append(0.0)

    # Pulizia
    try:
        import shutil
        shutil.rmtree(temp_dir)
    except:
        pass

    logger.info(f"✅ REAL CLIP Scores calcolati: {successful}/{len(results)} successi")

    return clip_scores

def save_partial_results(clip_scores, model_name, current, total):
    """Salva risultati parziali per evitare perdite"""
    try:
        output_dir = "evaluation_results/clip_scores"
        os.makedirs(output_dir, exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{model_name.lower().replace('-', '_')}_PARTIAL_{current}of{total}_{timestamp}.json"
        filepath = os.path.join(output_dir, filename)

        valid_scores = [s for s in clip_scores if s > 0]

        partial_results = {
            "model": model_name,
            "progress": f"{current}/{total}",
            "total_examples": len(clip_scores),
            "successful_conversions": len(valid_scores),
            "clip_scores": clip_scores,
            "avg_clip_score": float(np.mean(valid_scores)) if valid_scores else 0.0,
            "std_clip_score": float(np.std(valid_scores)) if valid_scores else 0.0,
            "timestamp": datetime.now().isoformat()
        }

        with open(filepath, 'w') as f:
            json.dump(partial_results, f, indent=2)

        logger.info(f"💾 Salvato parziale: {filename}")

    except Exception as e:
        logger.warning(f"❌ Errore salvataggio parziale: {e}")

def save_clip_results(clip_scores, model_name, output_dir="evaluation_results/clip_scores"):
    """Salva risultati CLIP"""
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{model_name.lower().replace('-', '_')}_REAL_CLIP_WORKING_{timestamp}.json"
    filepath = os.path.join(output_dir, filename)
    
    # Calcola statistiche
    valid_scores = [s for s in clip_scores if s > 0]
    
    results = {
        "model": model_name,
        "total_examples": len(clip_scores),
        "successful_conversions": len(valid_scores),
        "clip_scores": clip_scores,
        "avg_clip_score": float(np.mean(valid_scores)) if valid_scores else 0.0,
        "std_clip_score": float(np.std(valid_scores)) if valid_scores else 0.0,
        "min_clip_score": float(np.min(valid_scores)) if valid_scores else 0.0,
        "max_clip_score": float(np.max(valid_scores)) if valid_scores else 0.0,
        "timestamp": datetime.now().isoformat()
    }
    
    with open(filepath, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"💾 Risultati salvati: {filepath}")
    return filepath

def main():
    parser = argparse.ArgumentParser(description='REAL CLIP Score Calculator WORKING')
    parser.add_argument('--results_file', required=True, help='File JSON risultati')
    parser.add_argument('--model_name', required=True, help='Nome modello')
    parser.add_argument('--max_examples', type=int, default=400, help='Max esempi da processare')
    
    args = parser.parse_args()
    
    logger.info("🎯 REAL CLIP SCORE CALCULATOR WORKING")
    logger.info("=" * 50)
    logger.info(f"🤖 Modello: {args.model_name}")
    logger.info(f"📁 File: {os.path.basename(args.results_file)}")
    logger.info(f"📊 Max esempi: {args.max_examples}")
    logger.info("=" * 50)
    
    # Calcola CLIP scores
    clip_scores = calculate_real_clip_scores(args.results_file, args.model_name, args.max_examples)
    
    if not clip_scores:
        logger.error("❌ Nessun CLIP score calcolato!")
        return
    
    # Salva risultati
    filepath = save_clip_results(clip_scores, args.model_name)
    
    # Statistiche finali
    valid_scores = [s for s in clip_scores if s > 0]
    
    print("\n" + "=" * 50)
    print("🎉 REAL CLIP SCORES WORKING CALCOLATI!")
    print("=" * 50)
    print(f"📊 REAL CLIP Score medio: {np.mean(valid_scores):.4f} (±{np.std(valid_scores):.4f})")
    print(f"📊 Range: {np.min(valid_scores):.4f} - {np.max(valid_scores):.4f}")
    print(f"📊 Scores validi: {len(valid_scores)}/{len(clip_scores)}")
    print(f"📄 Risultati: {os.path.basename(filepath)}")
    print("=" * 50)
    print("✅ REAL CLIP Score WORKING completato!")

if __name__ == "__main__":
    main()
