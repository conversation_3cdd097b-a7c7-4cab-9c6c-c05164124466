#!/usr/bin/env python3
"""
📊 COMPREHENSIVE METRICS REPORT GENERATOR
Crea report finale con TUTTE le metriche e VERO CLIP Score
"""

import os
import json
import argparse
import logging
from datetime import datetime
import matplotlib.pyplot as plt
import numpy as np

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_comprehensive_metrics(metrics_dir):
    """Carica tutte le metriche comprehensive"""
    logger.info(f"📂 Caricamento metriche da: {metrics_dir}")
    
    all_metrics = {}
    
    for filename in os.listdir(metrics_dir):
        if 'comprehensive_metrics' in filename and filename.endswith('.json'):
            filepath = os.path.join(metrics_dir, filename)
            
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)
                
                model_name = data.get('model', 'unknown')
                all_metrics[model_name] = data
                logger.info(f"✅ Caricato: {model_name}")
                
            except Exception as e:
                logger.warning(f"❌ Errore caricamento {filename}: {e}")
    
    return all_metrics

def create_comparison_table(all_metrics):
    """Crea tabella di confronto"""
    logger.info("📊 Creazione tabella di confronto...")
    
    # Definisci metriche da confrontare
    metrics_to_compare = [
        ('bleu_1', 'BLEU-1', '{:.4f}'),
        ('bleu_2', 'BLEU-2', '{:.4f}'),
        ('bleu_3', 'BLEU-3', '{:.4f}'),
        ('bleu_4', 'BLEU-4', '{:.4f}'),
        ('rouge_l', 'ROUGE-L', '{:.4f}'),
        ('meteor', 'METEOR', '{:.4f}'),
        ('bert_f1', 'BERTScore F1', '{:.4f}'),
        ('cider', 'CIDEr', '{:.4f}'),
        ('real_clip_score', 'REAL CLIPScore', '{:.2f}')
    ]
    
    # Crea tabella
    table_data = []
    
    for model_name, data in all_metrics.items():
        row = {'Model': model_name}
        metrics = data.get('metrics', {})
        
        for metric_key, metric_name, format_str in metrics_to_compare:
            if metric_key == 'real_clip_score':
                # CLIP score ha struttura diversa
                clip_data = metrics.get(metric_key, {})
                if isinstance(clip_data, dict):
                    value = clip_data.get('mean', 0.0)
                else:
                    value = clip_data
            else:
                value = metrics.get(metric_key, 0.0)
            
            row[metric_name] = format_str.format(value)
        
        # Aggiungi info aggiuntive
        row['Examples'] = f"{data.get('processed_examples', 0)}/{data.get('total_examples', 0)}"
        row['Images'] = str(data.get('successful_conversions', 0))
        
        table_data.append(row)
    
    return table_data

def create_radar_chart(all_metrics, output_file):
    """Crea radar chart per confronto visivo"""
    logger.info("📊 Creazione radar chart...")
    
    # Metriche per radar (normalizzate 0-1)
    radar_metrics = [
        ('bleu_4', 'BLEU-4', 0.15),  # Max tipico
        ('rouge_l', 'ROUGE-L', 0.6),
        ('meteor', 'METEOR', 0.5),
        ('bert_f1', 'BERTScore', 1.0),
        ('cider', 'CIDEr', 3.0),
        ('real_clip_score', 'CLIP', 40.0)  # Max tipico per raw logits
    ]
    
    # Prepara dati
    models = list(all_metrics.keys())
    metrics_names = [name for _, name, _ in radar_metrics]
    
    # Calcola valori normalizzati
    model_values = {}
    for model_name, data in all_metrics.items():
        values = []
        metrics = data.get('metrics', {})
        
        for metric_key, _, max_val in radar_metrics:
            if metric_key == 'real_clip_score':
                clip_data = metrics.get(metric_key, {})
                if isinstance(clip_data, dict):
                    raw_value = clip_data.get('mean', 0.0)
                else:
                    raw_value = clip_data
            else:
                raw_value = metrics.get(metric_key, 0.0)
            
            # Normalizza 0-1
            normalized = min(raw_value / max_val, 1.0)
            values.append(normalized)
        
        model_values[model_name] = values
    
    # Crea radar chart
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # Angoli per ogni metrica
    angles = np.linspace(0, 2 * np.pi, len(metrics_names), endpoint=False).tolist()
    angles += angles[:1]  # Chiudi il cerchio
    
    # Colori per modelli
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
    
    for i, (model_name, values) in enumerate(model_values.items()):
        values += values[:1]  # Chiudi il cerchio
        ax.plot(angles, values, 'o-', linewidth=2, label=model_name, color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])
    
    # Personalizza grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics_names)
    ax.set_ylim(0, 1)
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])
    ax.grid(True)
    
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    plt.title('📊 Comprehensive Metrics Comparison\n(Normalized 0-100%)', size=16, pad=20)
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"✅ Radar chart salvato: {output_file}")

def create_comprehensive_report(all_metrics, output_file):
    """Crea report comprehensive completo"""
    logger.info("📋 Creazione report comprehensive...")
    
    # Crea tabella
    table_data = create_comparison_table(all_metrics)
    
    # Ordina per REAL CLIPScore
    def get_clip_score(row):
        try:
            return float(row['REAL CLIPScore'])
        except:
            return 0.0
    
    table_data.sort(key=get_clip_score, reverse=True)
    
    # Genera report HTML
    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>📊 Comprehensive Metrics Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
        .container {{ background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        h1 {{ color: #2c3e50; text-align: center; margin-bottom: 30px; }}
        h2 {{ color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        th, td {{ padding: 12px; text-align: center; border: 1px solid #ddd; }}
        th {{ background: #3498db; color: white; font-weight: bold; }}
        tr:nth-child(even) {{ background: #f9f9f9; }}
        tr:hover {{ background: #e8f4fd; }}
        .winner {{ background: #d4edda !important; font-weight: bold; }}
        .metric-desc {{ background: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0; }}
        .timestamp {{ text-align: center; color: #666; margin-top: 30px; }}
        .highlight {{ color: #e74c3c; font-weight: bold; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 COMPREHENSIVE METRICS REPORT</h1>
        <h2>🎯 SVG Image Captioning Models Evaluation</h2>
        
        <div class="metric-desc">
            <strong>🔍 Evaluation Details:</strong><br>
            • <strong>REAL CLIPScore</strong>: OpenAI CLIP-ViT-Base-Patch32 (raw logits, no sigmoid)<br>
            • <strong>BERTScore</strong>: Semantic similarity using BERT embeddings<br>
            • <strong>METEOR</strong>: Alignment-based metric with synonyms and paraphrases<br>
            • <strong>CIDEr</strong>: Consensus-based Image Description Evaluation<br>
            • <strong>BLEU/ROUGE</strong>: N-gram overlap metrics<br>
            • <strong>Date</strong>: {datetime.now().strftime("%Y-%m-%d %H:%M")}
        </div>
        
        <h2>🏆 FINAL RANKING</h2>
        <table>
            <thead>
                <tr>
                    <th>Rank</th>
                    <th>Model</th>
                    <th>REAL CLIPScore</th>
                    <th>BLEU-4</th>
                    <th>ROUGE-L</th>
                    <th>METEOR</th>
                    <th>BERTScore F1</th>
                    <th>CIDEr</th>
                    <th>Examples</th>
                    <th>Images</th>
                </tr>
            </thead>
            <tbody>
    """
    
    # Aggiungi righe tabella
    for i, row in enumerate(table_data):
        rank_emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][min(i, 4)]
        css_class = "winner" if i == 0 else ""
        
        html_content += f"""
                <tr class="{css_class}">
                    <td>{rank_emoji} {i+1}</td>
                    <td><strong>{row['Model']}</strong></td>
                    <td class="highlight">{row['REAL CLIPScore']}</td>
                    <td>{row['BLEU-4']}</td>
                    <td>{row['ROUGE-L']}</td>
                    <td>{row['METEOR']}</td>
                    <td>{row['BERTScore F1']}</td>
                    <td>{row['CIDEr']}</td>
                    <td>{row['Examples']}</td>
                    <td>{row['Images']}</td>
                </tr>
        """
    
    html_content += """
            </tbody>
        </table>
        
        <h2>📈 DETAILED ANALYSIS</h2>
        <div class="metric-desc">
    """
    
    # Analisi dettagliata per ogni modello
    for i, row in enumerate(table_data):
        rank_emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][min(i, 4)]
        model_name = row['Model']
        
        html_content += f"""
            <h3>{rank_emoji} {model_name}</h3>
            <ul>
                <li><strong>REAL CLIPScore:</strong> {row['REAL CLIPScore']} (Image-text alignment)</li>
                <li><strong>BLEU-4:</strong> {row['BLEU-4']} (N-gram precision)</li>
                <li><strong>ROUGE-L:</strong> {row['ROUGE-L']} (Longest common subsequence)</li>
                <li><strong>METEOR:</strong> {row['METEOR']} (Alignment with synonyms)</li>
                <li><strong>BERTScore:</strong> {row['BERTScore F1']} (Semantic similarity)</li>
                <li><strong>CIDEr:</strong> {row['CIDEr']} (Consensus-based evaluation)</li>
                <li><strong>Coverage:</strong> {row['Examples']} examples, {row['Images']} images</li>
            </ul>
        """
    
    html_content += f"""
        </div>
        
        <h2>🔍 KEY INSIGHTS</h2>
        <div class="metric-desc">
            <h3>🎯 CLIP Score Analysis</h3>
            <p><strong>CRITICAL:</strong> This evaluation uses <span class="highlight">REAL CLIPScore</span> with OpenAI CLIP model, 
            not pseudo-CLIP heuristics. Raw logits provide realistic scores (typically 15-40 range).</p>
            
            <h3>📊 Metric Interpretation</h3>
            <ul>
                <li><strong>Higher CLIP ≠ Better for humans:</strong> CLIP favors brevity over detail</li>
                <li><strong>BERTScore:</strong> Better captures semantic similarity than BLEU</li>
                <li><strong>METEOR:</strong> Accounts for synonyms and paraphrases</li>
                <li><strong>CIDEr:</strong> Consensus-based, good for image captioning</li>
            </ul>
            
            <h3>🏆 Winner Analysis</h3>
            <p>The top model excels in <span class="highlight">image-text alignment (CLIP)</span> while maintaining 
            good performance across all linguistic metrics.</p>
        </div>
        
        <div class="timestamp">
            📅 Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}<br>
            🔧 Evaluation: REAL CLIP Score + Comprehensive NLP Metrics<br>
            ✅ Status: COMPLETE - All models evaluated with realistic scores
        </div>
    </div>
</body>
</html>
    """
    
    # Salva report
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"✅ Report HTML salvato: {output_file}")
    
    return table_data

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Crea comprehensive report")
    parser.add_argument("--metrics_dir", default="evaluation_results/comprehensive_metrics", help="Directory metriche")
    parser.add_argument("--output_dir", default="evaluation_results/reports", help="Directory output")

    args = parser.parse_args()

    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)

    # Carica metriche
    all_metrics = load_comprehensive_metrics(args.metrics_dir)

    if not all_metrics:
        logger.error("❌ Nessuna metrica trovata!")
        return

    logger.info(f"📊 Trovati {len(all_metrics)} modelli")

    # Crea report
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Report HTML
    html_file = os.path.join(args.output_dir, f"comprehensive_report_{timestamp}.html")
    table_data = create_comprehensive_report(all_metrics, html_file)

    # Radar chart
    radar_file = os.path.join(args.output_dir, f"radar_chart_{timestamp}.png")
    create_radar_chart(all_metrics, radar_file)

    # Summary JSON
    summary_file = os.path.join(args.output_dir, f"summary_{timestamp}.json")
    summary = {
        'timestamp': datetime.now().isoformat(),
        'models_evaluated': len(all_metrics),
        'ranking': [row['Model'] for row in table_data],
        'best_model': table_data[0]['Model'] if table_data else None,
        'files': {
            'html_report': html_file,
            'radar_chart': radar_file,
            'summary': summary_file
        }
    }

    with open(summary_file, 'w') as f:
        json.dump(summary, f, indent=2)

    # Mostra risultati
    print("\n" + "="*60)
    print("📊 COMPREHENSIVE REPORT GENERATED")
    print("="*60)

    for i, row in enumerate(table_data):
        rank_emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][min(i, 4)]
        print(f"{rank_emoji} {i+1}. {row['Model']} - CLIP: {row['REAL CLIPScore']}")

    print("="*60)
    print(f"📁 HTML Report: {html_file}")
    print(f"📊 Radar Chart: {radar_file}")
    print(f"📋 Summary: {summary_file}")
    print("="*60)

if __name__ == "__main__":
    main()
