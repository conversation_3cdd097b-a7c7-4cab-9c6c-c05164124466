# 🎯 EVALUATION PIPELINE MODELLI QUANTIZZATI

Pipeline completa per valutare i modelli Gemma-2-9B e Llama-3.1-8B quantizzati quando saranno pronti.

## 📁 File Creati

### 🐍 Script Python
- **`evaluate_quantized_models.py`** - Esegue inference sui modelli quantizzati
- **`calculate_quantized_metrics.py`** - Calcola metriche comprehensive 
- **`launch_quantized_evaluation.py`** - Launcher automatico per le valutazioni

### 🖥️ Script SLURM
- **`evaluate_gemma_quantized.slurm`** - Job SLURM per Gemma-2-9B quantizzato
- **`evaluate_llama_quantized.slurm`** - Job SLURM per Llama-3.1-8B quantizzato

## 🚀 Come Usare

### Opzione 1: Launcher Automatico (CONSIGLIATO)

```bash
# Controlla se i modelli sono pronti
python scripts/evaluation/launch_quantized_evaluation.py

# Lancia automaticamente quando pronti
python scripts/evaluation/launch_quantized_evaluation.py --auto_launch
```

Il launcher:
- ✅ Controlla ogni 30 minuti se i modelli sono pronti
- ✅ Lancia automaticamente i job SLURM quando disponibili
- ✅ Aspetta il completamento e crea report finale

### Opzione 2: Manuale

```bash
# 1. Lancia Gemma quantizzato
sbatch scripts/slurm/evaluate_gemma_quantized.slurm

# 2. Lancia Llama quantizzato  
sbatch scripts/slurm/evaluate_llama_quantized.slurm

# 3. Calcola metriche (dopo completamento)
python scripts/evaluation/calculate_quantized_metrics.py \
    --results_files evaluation_results/quantized_models/GEMMA_QUANT_*.json \
                   evaluation_results/quantized_models/LLAMA_QUANT_*.json
```

### Opzione 3: Singolo Modello

```bash
# Solo Gemma
python scripts/evaluation/evaluate_quantized_models.py \
    --model_name GEMMA_QUANT \
    --model_path google/gemma-2-9b-it \
    --adapter_path experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized/checkpoint-XXXX \
    --dataset data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json

# Solo Llama
python scripts/evaluation/evaluate_quantized_models.py \
    --model_name LLAMA_QUANT \
    --model_path meta-llama/Llama-3.1-8B-Instruct \
    --adapter_path experiments/xml_direct_input/outputs/llama_t8_scratch_quantized/checkpoint-XXXX \
    --dataset data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json
```

## 📊 Output Generati

### 📄 File Risultati
```
evaluation_results/quantized_models/
├── GEMMA_QUANT_quantized_results_YYYYMMDD_HHMMSS.json
├── LLAMA_QUANT_quantized_results_YYYYMMDD_HHMMSS.json
└── quantized_evaluation_final_report_YYYYMMDD_HHMMSS.json
```

### 📈 Metriche Comprehensive
```
evaluation_results/comprehensive_metrics/
├── GEMMA_QUANT_comprehensive_metrics_YYYYMMDD_HHMMSS.json
└── LLAMA_QUANT_comprehensive_metrics_YYYYMMDD_HHMMSS.json
```

### 📊 Grafici Radar Aggiornati
```
evaluation_results/radar_charts_PERFECT/
├── ALL_MODELS_radar_combined_PERFECT.png  # Con i nuovi modelli
├── gemma_quant_radar_perfect.png          # Nuovo
├── llama_quant_radar_perfect.png          # Nuovo
└── RADAR_CHARTS_REPORT.html               # Aggiornato
```

## ⚙️ Configurazione

### 🔧 Parametri Modelli (DA AGGIORNARE)

Negli script SLURM, aggiorna questi path quando i modelli saranno pronti:

```bash
# Per Gemma
ADAPTER_PATH="experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized/checkpoint-FINAL"

# Per Llama  
ADAPTER_PATH="experiments/xml_direct_input/outputs/llama_t8_scratch_quantized/checkpoint-FINAL"
```

### 📊 Dataset di Test
- **File**: `data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json`
- **Esempi**: 400 (stesso usato per i baseline)
- **Formato**: XML content + ground truth caption

### 🖥️ Risorse SLURM
- **Partizione**: `all_usr_prod`
- **GPU**: 1x GPU (sufficiente per quantizzati)
- **RAM**: 32GB
- **CPU**: 8 cores
- **Tempo**: 6 ore (dovrebbe bastare)

## 🔍 Monitoraggio

### Controlla Job Status
```bash
squeue -u ediluzio
```

### Controlla Log
```bash
tail -f logs/GEMMA_QUANT_EVAL_*.out
tail -f logs/LLAMA_QUANT_EVAL_*.out
```

### Controlla Progresso Training
```bash
python scripts/evaluation/launch_quantized_evaluation.py
```

## 🎯 Metriche Calcolate

Per ogni modello quantizzato:
- **BLEU-1, BLEU-2, BLEU-3, BLEU-4** - Precisione n-gram
- **ROUGE-L** - Longest Common Subsequence  
- **METEOR** - Semantic similarity
- **CIDEr** - Consensus-based evaluation
- **CLIP Score** - Visual-semantic alignment (REALE)

## 📋 Checklist Pre-Evaluation

- [ ] Training modelli quantizzati completato
- [ ] Checkpoint finali disponibili
- [ ] Path aggiornati negli script SLURM
- [ ] Dataset di test presente
- [ ] Environment conda attivo
- [ ] GPU disponibili

## 🚨 Troubleshooting

### Errore "Adapter non trovato"
```bash
# Controlla checkpoint disponibili
ls experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized/
ls experiments/xml_direct_input/outputs/llama_t8_scratch_quantized/
```

### Errore memoria GPU
- I modelli quantizzati dovrebbero usare ~11GB
- Se problemi, riduci `batch_size` in `evaluate_quantized_models.py`

### Job in coda troppo a lungo
- Usa partizione `boost_usr_prod` se disponibile
- Riduci `--time` se necessario

## 🎉 Risultati Attesi

Dopo l'evaluation completa avrai:
- ✅ **7 modelli** nei grafici radar (5 attuali + 2 quantizzati)
- ✅ **Confronto completo** trained vs quantizzati vs baseline
- ✅ **Report HTML** aggiornato con tutti i modelli
- ✅ **Metriche comprehensive** per analisi dettagliata

---

**🚀 TUTTO PRONTO PER QUANDO I MODELLI QUANTIZZATI SARANNO COMPLETATI!** 🎯
