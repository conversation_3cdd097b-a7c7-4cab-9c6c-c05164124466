#!/usr/bin/env python3
"""
🎯 GEMMA EVALUATION COMPLETA - 400 ESEMPI (CPU OTTIMIZZATO)
Valuta Gemma T9 checkpoint sul dataset completo di 400 esempi con ottimizzazioni CPU
"""

import os
import json
import argparse
import logging
import gc
import time
import tempfile
import subprocess
import threading
import re
from datetime import datetime
import torch
import numpy as np
from PIL import Image
from transformers import AutoTokenizer, AutoModelForCausalLM, CLIPProcessor, CLIPModel
from peft import PeftModel
import warnings
warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """Pulisce memoria CPU e GPU"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()
    time.sleep(1)

def replacer(match):
    """Converte colori da formato numerico a RGB"""
    numbers = re.findall(r'\d+', match.group())
    if len(numbers) >= 3:
        return f"{match.group().split(':')[0]}:rgb({numbers[0]},{numbers[1]},{numbers[2]})"
    return match.group()

def de_parser_correct(svg_data):
    """de_parser CORRETTO con gestione RGB e sfondo bianco"""
    res = '<?xml version="1.0" encoding="utf-8"?>\n'
    res += '<svg viewBox="0 0 512 512" width="512" height="512" xmlns="http://www.w3.org/2000/svg">\n'
    res += '<rect width="512" height="512" fill="white" stroke="none"/>\n'

    svg_data = svg_data.replace("style=", "<path style=\"")
    svg_data = re.sub(r"stroke:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = re.sub(r"fill:([0-9]{1,3},?){3}", replacer, svg_data)
    svg_data = svg_data.replace("\t", "\" d=\"")
    svg_data = svg_data.replace("\n", "Z\" />\n")

    res += svg_data
    res += "</svg>"
    return res

def svg_to_png_cairosvg_WORKING(svg_content, output_path, size=224):
    """
    Conversione SVG→PNG con CairoSVG (metodo che funziona)
    Basato su create_baseline_dataset_SIMPLE.py
    """
    try:
        import cairosvg
        # Conversione con CairoSVG - parametri ottimizzati
        cairosvg.svg2png(
            bytestring=svg_content.encode('utf-8'),
            write_to=output_path,
            output_width=size,
            output_height=size,
            background_color='white',  # Sfondo bianco garantito
            dpi=150  # DPI alta per qualità ottimale
        )

        return os.path.exists(output_path)

    except Exception as e:
        logger.warning(f"❌ Errore CairoSVG: {e}")
        return False

def svg_to_png_subprocess_FALLBACK(svg_content, output_path, size=224):
    """Conversione fallback con subprocess se CairoSVG fallisce"""
    # Salva SVG temporaneo
    with tempfile.NamedTemporaryFile(mode='w', suffix='.svg', delete=False) as f:
        f.write(svg_content)
        svg_temp = f.name

    success = False

    try:
        # Prova rsvg-convert
        try:
            cmd = ['rsvg-convert', '--width', str(size), '--height', str(size),
                   '--background-color', 'white', '--output', output_path, svg_temp]
            result = subprocess.run(cmd, timeout=30, capture_output=True)
            if result.returncode == 0 and os.path.exists(output_path):
                success = True
        except (FileNotFoundError, subprocess.TimeoutExpired):
            pass

        # Prova inkscape se rsvg-convert fallisce
        if not success:
            try:
                cmd = ['inkscape', '--export-type=png', f'--export-width={size}',
                       f'--export-height={size}', f'--export-filename={output_path}', svg_temp]
                result = subprocess.run(cmd, timeout=30, capture_output=True)
                if result.returncode == 0 and os.path.exists(output_path):
                    success = True
            except (FileNotFoundError, subprocess.TimeoutExpired):
                pass

    finally:
        try:
            os.unlink(svg_temp)
        except:
            pass

    return success

def convert_xml_to_png_FIXED(xml_content, output_path, size=224):
    """Converte XML SVG → PNG con metodo che funziona"""
    try:
        # Parse XML in SVG valido
        svg_content = de_parser_correct(xml_content)

        # Metodo 1: CairoSVG (quello che funziona)
        if svg_to_png_cairosvg_WORKING(svg_content, output_path, size):
            return True

        # Metodo 2: Subprocess fallback
        if svg_to_png_subprocess_FALLBACK(svg_content, output_path, size):
            return True

        logger.error(f"❌ Tutti i metodi falliti per {output_path}")
        return False

    except Exception as e:
        logger.error(f"❌ Errore conversione XML→PNG: {e}")
        return False

def load_dataset_complete(dataset_file):
    """Carica il dataset completo di 400 esempi"""
    logger.info(f"📊 Caricamento dataset completo: {dataset_file}")
    
    with open(dataset_file, 'r') as f:
        data = json.load(f)
    
    logger.info(f"✅ Dataset caricato: {len(data)} esempi")
    
    # Verifica che i dati abbiano XML content
    valid_data = []
    missing_xml = 0
    
    for example in data:
        # Prova diversi campi per XML content
        xml_content = example.get('xml_content', '') or example.get('xml', '')
        caption = example.get('caption', '')

        if xml_content and caption:
            valid_data.append({
                'id': example.get('id', len(valid_data)),
                'xml': xml_content,
                'caption': caption,
                'filename': example.get('filename', f"example_{len(valid_data)}")
            })
        else:
            missing_xml += 1
            logger.warning(f"❌ XML o caption mancante per esempio {example.get('id', 'unknown')}")
    
    logger.info(f"✅ Esempi validi: {len(valid_data)}")
    if missing_xml > 0:
        logger.warning(f"⚠️ Esempi con XML/caption mancanti: {missing_xml}")
    
    return valid_data

def setup_clip_model(device='cpu'):
    """Setup CLIP model per CLIPScore"""
    try:
        logger.info(f"📥 Caricamento CLIP model su {device}...")
        model_name = "openai/clip-vit-base-patch32"
        model = CLIPModel.from_pretrained(model_name).to(device)
        processor = CLIPProcessor.from_pretrained(model_name)
        logger.info("✅ CLIP model caricato")
        return model, processor
    except Exception as e:
        logger.error(f"❌ Errore setup CLIP: {e}")
        return None, None

def calculate_clip_score(image_path, text, clip_model, clip_processor, device):
    """Calcola CLIPScore per immagine e testo"""
    try:
        image = Image.open(image_path).convert('RGB')

        inputs = clip_processor(text=[text], images=image, return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}

        with torch.no_grad():
            outputs = clip_model(**inputs)
            logits_per_image = outputs.logits_per_image
            clip_score = torch.diagonal(logits_per_image).cpu().numpy()[0]
            normalized_score = 1 / (1 + np.exp(-clip_score))

            return float(normalized_score)

    except Exception as e:
        logger.warning(f"❌ Errore CLIPScore: {e}")
        return 0.0

class GemmaEvaluator:
    """Valutatore per modello Gemma fine-tuned con CLIPScore"""

    def __init__(self, force_cpu=True, enable_clip=True):
        self.device = 'cpu' if force_cpu else ('cuda' if torch.cuda.is_available() else 'cpu')
        self.enable_clip = enable_clip
        logger.info(f"🖥️ Usando device: {self.device}")

        if self.device == 'cpu':
            logger.info("🔧 Modalità CPU: Ottimizzazioni memoria attivate")
        else:
            logger.info("🚀 Modalità GPU: Ottimizzazioni CUDA attivate")
            if torch.cuda.is_available():
                gpu_name = torch.cuda.get_device_name(0)
                gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
                logger.info(f"🖥️ GPU: {gpu_name} ({gpu_memory:.1f}GB)")

        # Setup CLIP se abilitato
        self.clip_model = None
        self.clip_processor = None
        if self.enable_clip:
            self.clip_model, self.clip_processor = setup_clip_model(self.device)
            if self.clip_model is None:
                logger.warning("⚠️ CLIP non disponibile, CLIPScore disabilitato")
                self.enable_clip = False
    
    def load_gemma_model(self, checkpoint_path):
        """Carica modello Gemma con LoRA adapter"""
        logger.info(f"📥 Caricamento Gemma da checkpoint: {checkpoint_path}")
        
        try:
            # Base model Gemma
            base_model_name = "google/gemma-2-9b-it"
            
            # Carica tokenizer
            logger.info("📥 Caricamento tokenizer...")
            tokenizer = AutoTokenizer.from_pretrained(base_model_name)
            if tokenizer.pad_token is None:
                tokenizer.pad_token = tokenizer.eos_token
            
            # Carica base model con ottimizzazioni device-specific
            logger.info("📥 Caricamento base model...")
            if self.device == 'cpu':
                base_model = AutoModelForCausalLM.from_pretrained(
                    base_model_name,
                    torch_dtype=torch.float32,  # CPU usa float32
                    device_map=None,
                    low_cpu_mem_usage=True,
                    trust_remote_code=True
                )
                base_model = base_model.to('cpu')
                logger.info("✅ Base model caricato su CPU")
            else:
                # GPU con ottimizzazioni memoria
                base_model = AutoModelForCausalLM.from_pretrained(
                    base_model_name,
                    torch_dtype=torch.float16,  # GPU usa float16
                    device_map="auto",
                    trust_remote_code=True,
                    low_cpu_mem_usage=True
                )
                logger.info("✅ Base model caricato su GPU")
            
            # Carica LoRA adapter
            logger.info("📥 Caricamento LoRA adapter...")
            model = PeftModel.from_pretrained(base_model, checkpoint_path)
            
            # Merge adapter per efficienza
            logger.info("🔧 Merge LoRA adapter...")
            model = model.merge_and_unload()

            if self.device == 'cpu':
                model = model.to('cpu')
            else:
                # GPU: il modello è già sul device corretto
                logger.info("🚀 Modello già su GPU dopo merge")

            logger.info(f"✅ Gemma caricato su {self.device}")

            # Log memoria GPU se disponibile
            if self.device == 'cuda' and torch.cuda.is_available():
                memory_allocated = torch.cuda.memory_allocated() / 1024**3
                memory_reserved = torch.cuda.memory_reserved() / 1024**3
                logger.info(f"🖥️ GPU Memory: {memory_allocated:.1f}GB allocated, {memory_reserved:.1f}GB reserved")

            return tokenizer, model
            
        except Exception as e:
            logger.error(f"❌ Errore caricamento Gemma: {e}")
            raise
    
    def generate_caption(self, tokenizer, model, xml_content):
        """Genera caption per un SVG XML"""
        try:
            # Prompt Gemma format
            prompt = f"<bos><start_of_turn>user\nDescribe this SVG:\n{xml_content}<end_of_turn>\n<start_of_turn>model\n"
            
            # Tokenize
            inputs = tokenizer(
                prompt,
                return_tensors="pt",
                truncation=True,
                max_length=1024,  # Ridotto per memoria
                padding=False
            ).to(self.device)
            
            # Generate con parametri ottimizzati per memoria
            with torch.no_grad():
                generated_ids = model.generate(
                    **inputs,
                    max_new_tokens=150,      # Ridotto per memoria
                    num_beams=2,             # Ridotto per memoria
                    do_sample=True,
                    temperature=0.7,
                    top_p=0.9,
                    repetition_penalty=1.1,
                    pad_token_id=tokenizer.eos_token_id,
                    early_stopping=True,
                    no_repeat_ngram_size=3
                )
            
            # Decode
            generated_text = tokenizer.decode(generated_ids[0], skip_special_tokens=True)
            
            # Estrai solo la caption (rimuovi prompt)
            if "<start_of_turn>model\n" in generated_text:
                caption = generated_text.split("<start_of_turn>model\n")[-1].strip()
            else:
                caption = generated_text.replace(prompt, "").strip()
            
            return caption
            
        except Exception as e:
            logger.error(f"❌ Errore generazione: {e}")
            return f"ERROR: {str(e)}"
    
    def evaluate_gemma(self, checkpoint_path, dataset, output_file):
        """Valuta Gemma su dataset completo con CLIPScore"""
        logger.info("🎯 Iniziando valutazione Gemma T9 con CLIPScore...")

        try:
            # Carica modello
            tokenizer, model = self.load_gemma_model(checkpoint_path)

            # Directory temporanea per immagini
            temp_dir = tempfile.mkdtemp(prefix="gemma_clip_")
            logger.info(f"📁 Directory temporanea: {temp_dir}")

            results = []
            clip_scores = []
            start_time = time.time()

            for i, example in enumerate(dataset):
                try:
                    # Genera caption
                    generated_caption = self.generate_caption(
                        tokenizer, model, example['xml']
                    )

                    # Calcola CLIPScore se abilitato
                    clip_score = 0.0
                    if self.enable_clip and not generated_caption.startswith('ERROR'):
                        try:
                            # Converte XML → PNG
                            img_path = os.path.join(temp_dir, f"gemma_{i}.png")
                            logger.debug(f"🔧 Convertendo esempio {i}: {len(example['xml'])} chars XML")

                            if convert_xml_to_png_FIXED(example['xml'], img_path, size=224):
                                logger.debug(f"✅ PNG creato: {img_path}")

                                # Calcola CLIPScore
                                clip_score = calculate_clip_score(
                                    img_path, generated_caption,
                                    self.clip_model, self.clip_processor, self.device
                                )
                                logger.debug(f"📊 CLIPScore esempio {i}: {clip_score:.4f}")

                                # Rimuovi immagine temporanea
                                try:
                                    os.remove(img_path)
                                except:
                                    pass
                            else:
                                logger.warning(f"⚠️ Conversione PNG fallita per esempio {i}")
                                logger.debug(f"🔍 XML content: {example['xml'][:200]}...")

                        except Exception as e:
                            logger.warning(f"⚠️ CLIPScore fallito per esempio {i}: {e}")
                            import traceback
                            logger.debug(f"🔍 Traceback: {traceback.format_exc()}")

                    clip_scores.append(clip_score)

                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'xml_content': example['xml'],
                        'ground_truth': example['caption'],
                        'generated_caption': generated_caption,
                        'clip_score': clip_score
                    })

                    # Progress
                    if (i + 1) % 10 == 0:
                        elapsed = time.time() - start_time
                        avg_time = elapsed / (i + 1)
                        remaining = avg_time * (len(dataset) - i - 1)
                        avg_clip = np.mean([s for s in clip_scores if s > 0]) if clip_scores else 0
                        logger.info(f"📊 Gemma Progress: {i+1}/{len(dataset)} "
                                  f"({(i+1)/len(dataset)*100:.1f}%) - "
                                  f"ETA: {remaining/60:.1f}min - "
                                  f"CLIPScore avg: {avg_clip:.3f}")

                    # Pulizia memoria ogni 25 esempi
                    if (i + 1) % 25 == 0:
                        clear_memory()

                except Exception as e:
                    logger.error(f"❌ Errore esempio {i}: {e}")
                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'xml_content': example['xml'],
                        'ground_truth': example['caption'],
                        'generated_caption': f"ERROR: {str(e)}",
                        'clip_score': 0.0
                    })
                    clip_scores.append(0.0)

            # Calcola statistiche CLIPScore
            valid_clip_scores = [s for s in clip_scores if s > 0]
            clip_stats = {
                'mean': float(np.mean(valid_clip_scores)) if valid_clip_scores else 0.0,
                'std': float(np.std(valid_clip_scores)) if valid_clip_scores else 0.0,
                'min': float(np.min(valid_clip_scores)) if valid_clip_scores else 0.0,
                'max': float(np.max(valid_clip_scores)) if valid_clip_scores else 0.0,
                'count': len(valid_clip_scores)
            }

            # Salva risultati
            checkpoint_name = os.path.basename(checkpoint_path)
            output_data = {
                'model': 'Gemma-T9',
                'checkpoint': checkpoint_name,
                'total_examples': len(dataset),
                'successful_examples': len([r for r in results if not r['generated_caption'].startswith('ERROR')]),
                'timestamp': datetime.now().isoformat(),
                'device': self.device,
                'clip_enabled': self.enable_clip,
                'clip_stats': clip_stats,
                'results': results
            }

            with open(output_file, 'w') as f:
                json.dump(output_data, f, indent=2)

            total_time = time.time() - start_time
            logger.info(f"✅ Gemma completato in {total_time/60:.1f}min")
            logger.info(f"💾 Risultati salvati: {output_file}")
            logger.info(f"📊 CLIPScore medio: {clip_stats['mean']:.4f} ({clip_stats['count']}/{len(dataset)} validi)")

            # Pulizia finale
            try:
                import shutil
                shutil.rmtree(temp_dir)
            except:
                pass

            del model, tokenizer
            if self.clip_model:
                del self.clip_model, self.clip_processor
            clear_memory()

            return output_data

        except Exception as e:
            logger.error(f"❌ Errore Gemma evaluation: {e}")
            return None

def main():
    parser = argparse.ArgumentParser(description='Gemma Evaluation Completa CPU')
    parser.add_argument('--checkpoint', 
                       default='experiments/xml_direct_input/outputs/gemma_t9_continue/checkpoint-15500',
                       help='Path al checkpoint Gemma')
    parser.add_argument('--dataset', 
                       default='data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json',
                       help='Path al dataset JSON')
    parser.add_argument('--output_dir', 
                       default='evaluation_results/gemma_COMPLETE_400_CPU',
                       help='Directory output')
    parser.add_argument('--force_cpu',
                       action='store_true',
                       default=False,
                       help='Forza modalità CPU')
    parser.add_argument('--use_gpu',
                       action='store_true',
                       default=False,
                       help='Usa GPU se disponibile')
    parser.add_argument('--enable_clip',
                       action='store_true',
                       default=True,
                       help='Abilita calcolo CLIPScore')
    parser.add_argument('--disable_clip',
                       action='store_true',
                       help='Disabilita calcolo CLIPScore')
    
    args = parser.parse_args()
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Carica dataset
    dataset = load_dataset_complete(args.dataset)
    if not dataset:
        logger.error("❌ Impossibile caricare il dataset")
        return
    
    # Determina se abilitare CLIPScore
    enable_clip = args.enable_clip and not args.disable_clip

    # Determina device (GPU ha priorità se richiesta)
    force_cpu = args.force_cpu and not args.use_gpu

    # Inizializza evaluator
    evaluator = GemmaEvaluator(force_cpu=force_cpu, enable_clip=enable_clip)

    logger.info(f"🎯 CLIPScore: {'✅ Abilitato' if enable_clip else '❌ Disabilitato'}")
    logger.info(f"🖥️ Device mode: {'CPU' if force_cpu else 'GPU (se disponibile)'}")
    
    # Valuta Gemma
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    checkpoint_name = os.path.basename(args.checkpoint)
    output_file = os.path.join(args.output_dir, f'gemma_t9_{checkpoint_name}_results_{timestamp}.json')
    
    evaluator.evaluate_gemma(args.checkpoint, dataset, output_file)
    
    logger.info("🎉 Gemma evaluation completa terminata!")

if __name__ == "__main__":
    main()
