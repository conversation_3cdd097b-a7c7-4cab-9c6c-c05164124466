#!/usr/bin/env python3
"""
🧪 TEST GEMMA EVALUATION FIXED
Test rapido per verificare che Gemma si carichi e funzioni
"""

import os
import json
import logging
import tempfile
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, CLIPProcessor, CLIPModel
from peft import PeftModel
from PIL import Image
import warnings
warnings.filterwarnings("ignore")

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gemma_loading():
    """Test caricamento Gemma con LoRA"""
    logger.info("🧪 Test caricamento Gemma T9...")
    
    try:
        checkpoint_path = "experiments/xml_direct_input/outputs/gemma_t9_continue/checkpoint-15500"
        
        if not os.path.exists(checkpoint_path):
            logger.error(f"❌ Checkpoint non trovato: {checkpoint_path}")
            return False
        
        # Base model
        base_model_name = "google/gemma-2-9b-it"
        
        # Carica tokenizer
        logger.info("📥 Caricamento tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(base_model_name)
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
        
        logger.info("✅ Tokenizer caricato")
        
        # Carica base model (CPU)
        logger.info("📥 Caricamento base model...")
        base_model = AutoModelForCausalLM.from_pretrained(
            base_model_name,
            torch_dtype=torch.float32,
            device_map=None,
            low_cpu_mem_usage=True,
            trust_remote_code=True
        )
        base_model = base_model.to('cpu')
        
        logger.info("✅ Base model caricato")
        
        # Carica LoRA adapter
        logger.info("📥 Caricamento LoRA adapter...")
        model = PeftModel.from_pretrained(base_model, checkpoint_path)
        
        logger.info("✅ LoRA adapter caricato")
        
        # Merge adapter
        logger.info("🔧 Merge LoRA adapter...")
        model = model.merge_and_unload()
        model = model.to('cpu')
        
        logger.info("✅ LoRA merged")
        
        # Test inference
        logger.info("🧪 Test inference...")
        test_xml = '<svg><rect x="10" y="10" width="50" height="30" fill="blue"/></svg>'
        prompt = f"<bos><start_of_turn>user\nDescribe this SVG:\n{test_xml}<end_of_turn>\n<start_of_turn>model\n"
        
        inputs = tokenizer(
            prompt,
            return_tensors="pt",
            truncation=True,
            max_length=512,
            padding=False
        ).to('cpu')
        
        with torch.no_grad():
            generated_ids = model.generate(
                **inputs,
                max_new_tokens=50,
                num_beams=2,
                do_sample=True,
                temperature=0.7,
                pad_token_id=tokenizer.eos_token_id,
                early_stopping=True
            )
        
        generated_text = tokenizer.decode(generated_ids[0], skip_special_tokens=True)
        
        # Estrai caption
        if "<start_of_turn>model\n" in generated_text:
            caption = generated_text.split("<start_of_turn>model\n")[-1].strip()
        else:
            caption = generated_text.replace(prompt, "").strip()
        
        logger.info(f"✅ Caption generata: {caption}")
        
        # Pulizia
        del model, tokenizer, base_model
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        logger.info("✅ Test Gemma PASSATO")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test Gemma FALLITO: {e}")
        return False

def test_dataset_loading():
    """Test caricamento dataset"""
    logger.info("🧪 Test caricamento dataset...")
    
    try:
        dataset_file = 'data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json'
        
        with open(dataset_file, 'r') as f:
            data = json.load(f)
        
        logger.info(f"✅ Dataset caricato: {len(data)} esempi")
        
        # Verifica primi 3 esempi
        valid_count = 0
        for i, example in enumerate(data[:3]):
            xml_content = example.get('xml', '')
            caption = example.get('caption', '')
            
            if xml_content and caption:
                valid_count += 1
                logger.info(f"✅ Esempio {i}: XML={len(xml_content)} chars, Caption={len(caption)} chars")
            else:
                logger.error(f"❌ Esempio {i}: XML o caption mancante")
        
        logger.info(f"✅ Esempi validi: {valid_count}/3")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test dataset FALLITO: {e}")
        return False

def test_clip_and_conversion():
    """Test conversione XML→PNG e CLIPScore"""
    logger.info("🧪 Test conversione XML→PNG e CLIPScore...")

    try:
        # Test XML
        test_xml = '''style=fill:255,0,0	M 100 100 L 200 100 L 200 200 L 100 200 Z
style=fill:0,255,0	M 250 100 L 350 100 L 350 200 L 250 200 Z'''

        # Test conversione (importa funzioni dal script principale)
        import sys
        sys.path.append('/work/tesi_ediluzio/scripts/evaluation')
        from gemma_evaluation_COMPLETE_400_CPU import convert_xml_to_png_FIXED, setup_clip_model, calculate_clip_score

        # Converte XML→PNG
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as f:
            png_path = f.name

        if convert_xml_to_png_FIXED(test_xml, png_path, size=224):
            logger.info("✅ Conversione XML→PNG riuscita")

            # Test CLIP
            clip_model, clip_processor = setup_clip_model('cpu')
            if clip_model is not None:
                test_caption = "A red rectangle and a green rectangle"
                clip_score = calculate_clip_score(png_path, test_caption, clip_model, clip_processor, 'cpu')
                logger.info(f"✅ CLIPScore calcolato: {clip_score:.4f}")

                # Pulizia
                del clip_model, clip_processor

                # Rimuovi file temporaneo
                try:
                    os.remove(png_path)
                except:
                    pass

                return True
            else:
                logger.warning("⚠️ CLIP non disponibile")
                return False
        else:
            logger.error("❌ Conversione XML→PNG fallita")
            return False

    except Exception as e:
        logger.error(f"❌ Test conversione/CLIP FALLITO: {e}")
        return False

def main():
    logger.info("🧪 INIZIO TEST GEMMA EVALUATION FIXED")
    logger.info("=" * 50)
    
    # Info sistema
    logger.info(f"🖥️ Device disponibile: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    logger.info(f"🐍 PyTorch: {torch.__version__}")
    
    # Test 1: Dataset
    try:
        if test_dataset_loading():
            logger.info("✅ Test 1 PASSATO: Dataset loading")
        else:
            logger.error("❌ Test 1 FALLITO: Dataset loading")
    except Exception as e:
        logger.error(f"❌ Test 1 ERRORE: {e}")
    
    # Test 2: Conversione e CLIP
    try:
        if test_clip_and_conversion():
            logger.info("✅ Test 2 PASSATO: Conversione XML→PNG e CLIPScore")
        else:
            logger.error("❌ Test 2 FALLITO: Conversione XML→PNG e CLIPScore")
    except Exception as e:
        logger.error(f"❌ Test 2 ERRORE: {e}")

    # Test 3: Gemma loading (opzionale - richiede molto tempo)
    logger.info("⏭️ Test 3 SALTATO: Gemma loading (troppo lento per test)")
    logger.info("   Per testare Gemma, eseguire il job SLURM completo")

    logger.info("=" * 50)
    logger.info("🎉 TEST GEMMA COMPLETATI!")

    logger.info("🚀 Se i test sono passati, il job SLURM dovrebbe funzionare correttamente")
    logger.info("🎯 Il sistema include ora:")
    logger.info("   - Conversione XML→PNG automatica")
    logger.info("   - CLIPScore integrato")
    logger.info("   - Evaluation completa Gemma T9")

if __name__ == "__main__":
    main()
