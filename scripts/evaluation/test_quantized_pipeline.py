#!/usr/bin/env python3
"""
🧪 TEST PIPELINE EVALUATION MODELLI QUANTIZZATI
Testa che tutti gli script funzionino correttamente
"""

import os
import sys
import json
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_imports():
    """Testa che tutti i moduli necessari siano importabili"""
    logger.info("🧪 Test imports...")
    
    try:
        import torch
        import transformers
        from peft import PeftModel
        import nltk
        logger.info("✅ Moduli base importati correttamente")

        # CLIP è opzionale per il test
        try:
            import clip
            logger.info("✅ CLIP disponibile")
        except ImportError:
            logger.warning("⚠️ CLIP non disponibile - sarà necessario per le metriche reali")

        return True
    except ImportError as e:
        logger.error(f"❌ Errore import moduli base: {e}")
        return False

def test_dataset_exists():
    """Testa che il dataset di test esista"""
    logger.info("🧪 Test dataset...")
    
    dataset_file = "data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json"
    
    if not os.path.exists(dataset_file):
        logger.error(f"❌ Dataset non trovato: {dataset_file}")
        return False
    
    try:
        with open(dataset_file, 'r') as f:
            data = json.load(f)
        
        if not isinstance(data, list) or len(data) == 0:
            logger.error("❌ Dataset vuoto o formato errato")
            return False
        
        # Controlla formato primo esempio
        first_item = data[0]

        # Il dataset può avere diversi formati
        if 'xml_content' in first_item and 'caption' in first_item:
            logger.info("✅ Formato dataset: xml_content + caption")
        elif 'xml' in first_item and 'caption' in first_item:
            logger.info("✅ Formato dataset: xml + caption")
        elif 'input' in first_item and 'output' in first_item:
            logger.info("✅ Formato dataset: input + output")
        else:
            logger.error(f"❌ Formato dataset non riconosciuto. Chiavi: {list(first_item.keys())}")
            return False
        
        logger.info(f"✅ Dataset OK: {len(data)} esempi")
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore lettura dataset: {e}")
        return False

def test_scripts_exist():
    """Testa che tutti gli script esistano"""
    logger.info("🧪 Test script esistenti...")
    
    required_scripts = [
        'scripts/evaluation/evaluate_quantized_models.py',
        'scripts/evaluation/calculate_quantized_metrics.py', 
        'scripts/evaluation/launch_quantized_evaluation.py',
        'scripts/slurm/evaluate_gemma_quantized.slurm',
        'scripts/slurm/evaluate_llama_quantized.slurm',
        'scripts/evaluation/COMPREHENSIVE_metrics_REAL_CLIP.py',
        'scripts/visualization/create_PERFECT_RADAR_CHARTS.py'
    ]
    
    missing_scripts = []
    
    for script in required_scripts:
        if not os.path.exists(script):
            missing_scripts.append(script)
    
    if missing_scripts:
        logger.error(f"❌ Script mancanti: {missing_scripts}")
        return False
    
    logger.info("✅ Tutti gli script presenti")
    return True

def test_directories():
    """Testa che le directory necessarie esistano o possano essere create"""
    logger.info("🧪 Test directory...")
    
    required_dirs = [
        'evaluation_results',
        'evaluation_results/quantized_models',
        'evaluation_results/comprehensive_metrics',
        'evaluation_results/radar_charts_PERFECT',
        'logs'
    ]
    
    for dir_path in required_dirs:
        try:
            os.makedirs(dir_path, exist_ok=True)
            logger.info(f"✅ Directory OK: {dir_path}")
        except Exception as e:
            logger.error(f"❌ Errore directory {dir_path}: {e}")
            return False
    
    return True

def test_launcher_logic():
    """Testa la logica del launcher senza eseguire job"""
    logger.info("🧪 Test launcher logic...")
    
    try:
        # Import launcher functions
        sys.path.append('scripts/evaluation')
        from launch_quantized_evaluation import check_model_ready
        
        # Test con directory inesistente
        ready, status = check_model_ready('/path/inesistente')
        if ready:
            logger.error("❌ Launcher dovrebbe riportare NOT READY per path inesistente")
            return False
        
        logger.info("✅ Launcher logic OK")
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore test launcher: {e}")
        return False

def test_gpu_availability():
    """Testa disponibilità GPU"""
    logger.info("🧪 Test GPU...")
    
    try:
        import torch
        
        if not torch.cuda.is_available():
            logger.warning("⚠️ CUDA non disponibile - evaluation richiederà CPU")
            return True  # Non è un errore fatale
        
        gpu_count = torch.cuda.device_count()
        logger.info(f"✅ GPU disponibili: {gpu_count}")
        
        # Test memoria GPU
        for i in range(gpu_count):
            props = torch.cuda.get_device_properties(i)
            memory_gb = props.total_memory / (1024**3)
            logger.info(f"  GPU {i}: {props.name} - {memory_gb:.1f}GB")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Errore test GPU: {e}")
        return False

def create_test_report():
    """Crea report di test"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"evaluation_results/test_report_{timestamp}.json"
    
    report_data = {
        'timestamp': timestamp,
        'test_type': 'quantized_evaluation_pipeline',
        'status': 'completed',
        'tests_run': [
            'imports',
            'dataset_exists', 
            'scripts_exist',
            'directories',
            'launcher_logic',
            'gpu_availability'
        ]
    }
    
    os.makedirs('evaluation_results', exist_ok=True)
    with open(report_file, 'w') as f:
        json.dump(report_data, f, indent=2)
    
    logger.info(f"📋 Report test: {report_file}")
    return report_file

def main():
    logger.info("🧪 TEST PIPELINE EVALUATION MODELLI QUANTIZZATI")
    logger.info("=" * 60)
    
    tests = [
        ("Imports", test_imports),
        ("Dataset", test_dataset_exists),
        ("Scripts", test_scripts_exist), 
        ("Directory", test_directories),
        ("Launcher Logic", test_launcher_logic),
        ("GPU", test_gpu_availability)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        logger.info(f"🔄 Esecuzione test: {test_name}")
        try:
            if test_func():
                passed += 1
                logger.info(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            logger.error(f"❌ {test_name}: ERROR - {e}")
        
        logger.info("-" * 40)
    
    # Summary
    logger.info("=" * 60)
    logger.info("🎯 RISULTATI TEST")
    logger.info("=" * 60)
    logger.info(f"✅ Test passati: {passed}")
    logger.info(f"❌ Test falliti: {failed}")
    logger.info(f"📊 Totale: {passed + failed}")
    
    if failed == 0:
        logger.info("🎉 TUTTI I TEST PASSATI! Pipeline pronta per l'uso")
        status = 0
    else:
        logger.error("⚠️ Alcuni test falliti - controlla i problemi prima dell'uso")
        status = 1
    
    # Crea report
    create_test_report()
    
    logger.info("=" * 60)
    return status

if __name__ == "__main__":
    exit(main())
