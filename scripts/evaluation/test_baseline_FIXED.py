#!/usr/bin/env python3
"""
🧪 TEST BASELINE EVALUATION FIXED
Test rapido con 3 esempi per verificare che i fix funzionino
"""

import os
import json
import logging
from datetime import datetime
from PIL import Image
import torch
from transformers import (
    BlipProcessor, BlipForConditionalGeneration,
    AutoProcessor, AutoModelForCausalLM
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_blip2_fixed():
    """Test BLIP-2 con fix CPU"""
    logger.info("🧪 Test BLIP-2 FIXED (CPU)...")
    
    try:
        # Carica dataset
        dataset_file = 'data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json'
        with open(dataset_file, 'r') as f:
            data = json.load(f)
        
        # Test primi 3 esempi
        test_data = data[:3]
        
        # Carica modello BLIP-2 in modalità CPU
        processor = BlipProcessor.from_pretrained("Salesforce/blip2-opt-2.7b")
        model = BlipForConditionalGeneration.from_pretrained(
            "Salesforce/blip2-opt-2.7b",
            torch_dtype=torch.float32,
            device_map=None
        )
        model = model.to('cpu')
        
        logger.info("✅ BLIP-2 caricato su CPU")
        
        # Test inference
        for i, example in enumerate(test_data):
            image = Image.open(example['image_path']).convert('RGB')
            inputs = processor(image, return_tensors="pt").to('cpu')
            
            with torch.no_grad():
                generated_ids = model.generate(
                    **inputs,
                    max_length=50,
                    num_beams=2,
                    do_sample=False,
                    early_stopping=True
                )
            
            generated_caption = processor.decode(generated_ids[0], skip_special_tokens=True)
            logger.info(f"✅ Esempio {i}: {generated_caption[:50]}...")
        
        logger.info("✅ BLIP-2 test PASSATO")
        
        # Pulizia
        del model, processor
        return True
        
    except Exception as e:
        logger.error(f"❌ BLIP-2 test FALLITO: {e}")
        return False

def test_florence2_fixed():
    """Test Florence-2 con fix mixed precision"""
    logger.info("🧪 Test Florence-2 FIXED (CPU)...")
    
    try:
        # Carica dataset
        dataset_file = 'data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json'
        with open(dataset_file, 'r') as f:
            data = json.load(f)
        
        # Test primi 2 esempi
        test_data = data[:2]
        
        # Carica modello Florence-2 con fix
        processor = AutoProcessor.from_pretrained("microsoft/Florence-2-large", trust_remote_code=True)
        model = AutoModelForCausalLM.from_pretrained(
            "microsoft/Florence-2-large",
            torch_dtype=torch.float32,  # Fix: usa float32 invece di float16
            trust_remote_code=True,
            device_map=None
        )
        model = model.to('cpu')
        
        logger.info("✅ Florence-2 caricato su CPU con float32")
        
        # Test inference
        for i, example in enumerate(test_data):
            image = Image.open(example['image_path']).convert('RGB')
            prompt = "<MORE_DETAILED_CAPTION>"
            
            inputs = processor(text=prompt, images=image, return_tensors="pt").to('cpu')
            
            with torch.no_grad():
                generated_ids = model.generate(
                    input_ids=inputs["input_ids"],
                    pixel_values=inputs["pixel_values"],
                    max_new_tokens=50,
                    num_beams=2,
                    do_sample=False,
                    early_stopping=True
                )
            
            generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
            generated_caption = generated_text.replace(prompt, "").strip()
            
            logger.info(f"✅ Esempio {i}: {generated_caption[:50]}...")
        
        logger.info("✅ Florence-2 test PASSATO")
        
        # Pulizia
        del model, processor
        return True
        
    except Exception as e:
        logger.error(f"❌ Florence-2 test FALLITO: {e}")
        return False

def main():
    logger.info("🧪 INIZIO TEST BASELINE EVALUATION FIXED")
    logger.info("=" * 50)
    
    # Info sistema
    logger.info(f"🖥️ Device disponibile: {'CUDA' if torch.cuda.is_available() else 'CPU'}")
    logger.info(f"💾 RAM disponibile: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f}GB" if torch.cuda.is_available() else "CPU mode")
    
    # Test 1: BLIP-2
    try:
        if test_blip2_fixed():
            logger.info("✅ Test 1 PASSATO: BLIP-2 FIXED")
        else:
            logger.error("❌ Test 1 FALLITO: BLIP-2 FIXED")
    except Exception as e:
        logger.error(f"❌ Test 1 ERRORE: {e}")
    
    # Test 2: Florence-2
    try:
        if test_florence2_fixed():
            logger.info("✅ Test 2 PASSATO: Florence-2 FIXED")
        else:
            logger.error("❌ Test 2 FALLITO: Florence-2 FIXED")
    except Exception as e:
        logger.error(f"❌ Test 2 ERRORE: {e}")
    
    logger.info("=" * 50)
    logger.info("🎉 TEST FIXED COMPLETATI!")
    
    logger.info("🚀 Se i test sono passati, il job SLURM dovrebbe funzionare correttamente")

if __name__ == "__main__":
    main()
