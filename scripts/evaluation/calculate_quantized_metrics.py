#!/usr/bin/env python3
"""
📊 CALCOLO METRICHE COMPREHENSIVE PER MODELLI QUANTIZZATI
Calcola BLEU, ROUGE, METEOR, CIDEr, CLIP per i modelli quantizzati
"""

import os
import json
import argparse
import logging
from datetime import datetime
import subprocess
import sys

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def calculate_metrics_for_quantized(results_file, model_name, output_dir):
    """Calcola metriche comprehensive per modello quantizzato"""
    logger.info(f"📊 Calcolo metriche per {model_name}")
    
    # Usa lo script esistente COMPREHENSIVE_metrics_REAL_CLIP.py
    cmd = [
        sys.executable,
        'scripts/evaluation/COMPREHENSIVE_metrics_REAL_CLIP.py',
        '--results_file', results_file,
        '--model_name', model_name,
        '--output_dir', output_dir,
        '--max_examples', '400'
    ]
    
    try:
        logger.info(f"🚀 Esecuzione: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=1800)  # 30 min timeout
        
        if result.returncode == 0:
            logger.info("✅ Metriche calcolate con successo")
            logger.info(result.stdout)
            return True
        else:
            logger.error(f"❌ Errore calcolo metriche: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        logger.error("❌ Timeout calcolo metriche (30 min)")
        return False
    except Exception as e:
        logger.error(f"❌ Errore esecuzione: {e}")
        return False

def update_radar_charts(comprehensive_dir, radar_dir):
    """Aggiorna i grafici radar con i nuovi modelli"""
    logger.info("📊 Aggiornamento grafici radar...")
    
    cmd = [
        sys.executable,
        'scripts/visualization/create_PERFECT_RADAR_CHARTS.py',
        '--results_dir', comprehensive_dir,
        '--output_dir', radar_dir
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)  # 5 min timeout
        
        if result.returncode == 0:
            logger.info("✅ Grafici radar aggiornati")
            return True
        else:
            logger.error(f"❌ Errore aggiornamento radar: {result.stderr}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Errore aggiornamento radar: {e}")
        return False

def create_comparison_report(results_files, output_dir):
    """Crea report di confronto tra tutti i modelli"""
    logger.info("📋 Creazione report di confronto...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = os.path.join(output_dir, f"quantized_models_comparison_{timestamp}.json")
    
    comparison_data = {
        'timestamp': timestamp,
        'models_evaluated': [],
        'summary': {}
    }
    
    for results_file in results_files:
        if os.path.exists(results_file):
            try:
                with open(results_file, 'r') as f:
                    data = json.load(f)
                
                model_info = {
                    'model': data.get('model', 'Unknown'),
                    'total_examples': data.get('total_examples', 0),
                    'successful_examples': data.get('successful_examples', 0),
                    'success_rate': data.get('successful_examples', 0) / max(data.get('total_examples', 1), 1),
                    'quantization': data.get('quantization', 'unknown'),
                    'results_file': results_file
                }
                
                comparison_data['models_evaluated'].append(model_info)
                
            except Exception as e:
                logger.warning(f"⚠️ Errore lettura {results_file}: {e}")
    
    # Salva report
    with open(report_file, 'w') as f:
        json.dump(comparison_data, f, indent=2)
    
    logger.info(f"✅ Report salvato: {report_file}")
    return report_file

def main():
    parser = argparse.ArgumentParser(description="Calcola metriche per modelli quantizzati")
    parser.add_argument("--results_files", nargs='+', required=True, help="File risultati da processare")
    parser.add_argument("--comprehensive_dir", default="evaluation_results/comprehensive_metrics", help="Directory metriche comprehensive")
    parser.add_argument("--radar_dir", default="evaluation_results/radar_charts_PERFECT", help="Directory grafici radar")
    parser.add_argument("--output_dir", default="evaluation_results/quantized_models", help="Directory output")
    
    args = parser.parse_args()
    
    logger.info("📊 CALCOLO METRICHE MODELLI QUANTIZZATI")
    logger.info("=" * 60)
    logger.info(f"📄 File risultati: {args.results_files}")
    logger.info(f"📁 Dir comprehensive: {args.comprehensive_dir}")
    logger.info(f"📊 Dir radar: {args.radar_dir}")
    logger.info("=" * 60)
    
    # Crea directory
    os.makedirs(args.comprehensive_dir, exist_ok=True)
    os.makedirs(args.radar_dir, exist_ok=True)
    os.makedirs(args.output_dir, exist_ok=True)
    
    success_count = 0
    
    # Calcola metriche per ogni modello
    for results_file in args.results_files:
        if not os.path.exists(results_file):
            logger.warning(f"⚠️ File non trovato: {results_file}")
            continue
        
        # Estrai nome modello dal file
        try:
            with open(results_file, 'r') as f:
                data = json.load(f)
            model_name = data.get('model', os.path.basename(results_file).split('_')[0])
        except:
            model_name = os.path.basename(results_file).split('_')[0]
        
        logger.info(f"🔄 Processando {model_name}...")
        
        if calculate_metrics_for_quantized(results_file, model_name, args.comprehensive_dir):
            success_count += 1
        else:
            logger.error(f"❌ Fallito calcolo metriche per {model_name}")
    
    # Aggiorna grafici radar se almeno un modello è stato processato
    if success_count > 0:
        logger.info("🎨 Aggiornamento visualizzazioni...")
        update_radar_charts(args.comprehensive_dir, args.radar_dir)
    
    # Crea report di confronto
    comparison_report = create_comparison_report(args.results_files, args.output_dir)
    
    # Summary finale
    logger.info("=" * 60)
    logger.info("🎉 CALCOLO METRICHE COMPLETATO!")
    logger.info("=" * 60)
    logger.info(f"✅ Modelli processati: {success_count}/{len(args.results_files)}")
    logger.info(f"📊 Metriche: {args.comprehensive_dir}")
    logger.info(f"📈 Grafici: {args.radar_dir}")
    logger.info(f"📋 Report: {comparison_report}")
    logger.info("=" * 60)
    
    return 0 if success_count > 0 else 1

if __name__ == "__main__":
    exit(main())
