#!/usr/bin/env python3
"""
🔗 MERGE BASELINE RESULTS WITH XML
Unisce i risultati dei modelli baseline con l'XML originale per calcolare le metriche
"""

import os
import json
import argparse
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def merge_results_with_xml(results_file, dataset_file, model_name, output_dir):
    """Unisce risultati baseline con XML originale"""
    logger.info(f"🔗 Merge risultati {model_name} con XML originale")
    
    # Carica risultati
    with open(results_file, 'r') as f:
        results_data = json.load(f)
    
    # Gestisci diversi formati
    if isinstance(results_data, list):
        results = results_data
    else:
        results = results_data.get('results', [])
    
    logger.info(f"📊 Risultati caricati: {len(results)} esempi")
    
    # Carica dataset originale
    with open(dataset_file, 'r') as f:
        dataset = json.load(f)
    
    logger.info(f"📊 Dataset originale: {len(dataset)} esempi")
    
    # Merge dati
    merged_results = []
    
    for i, result in enumerate(results):
        try:
            # Trova esempio corrispondente nel dataset
            if i < len(dataset):
                dataset_example = dataset[i]
                
                # Crea entry merged
                merged_entry = {
                    'id': i,
                    'example_id': result.get('example_id', i),
                    'xml_content': dataset_example.get('xml_content', ''),
                    'ground_truth': result.get('ground_truth', dataset_example.get('caption', '')),
                    'prediction': result.get('prediction', ''),
                    'generated_caption': result.get('prediction', ''),  # Alias per compatibilità
                    'success': result.get('success', True),
                    'model': model_name,
                    'filename': dataset_example.get('filename', f'baseline_{i:04d}.png')
                }
                
                merged_results.append(merged_entry)
                
            else:
                logger.warning(f"⚠️ Esempio {i} non trovato nel dataset originale")
                
        except Exception as e:
            logger.error(f"❌ Errore merge esempio {i}: {e}")
    
    logger.info(f"✅ Merge completato: {len(merged_results)} esempi")
    
    # Salva risultati merged
    os.makedirs(output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(output_dir, f"{model_name}_merged_results_{timestamp}.json")
    
    merged_data = {
        'model': model_name,
        'total_examples': len(merged_results),
        'timestamp': timestamp,
        'source_results': results_file,
        'source_dataset': dataset_file,
        'results': merged_results
    }
    
    with open(output_file, 'w') as f:
        json.dump(merged_data, f, indent=2)
    
    logger.info(f"💾 Risultati merged salvati: {output_file}")
    return output_file

def main():
    parser = argparse.ArgumentParser(description="Merge baseline results with XML")
    parser.add_argument("--results_file", required=True, help="File risultati modello")
    parser.add_argument("--dataset_file", required=True, help="Dataset originale con XML")
    parser.add_argument("--model_name", required=True, help="Nome modello")
    parser.add_argument("--output_dir", default="evaluation_results/merged_results", help="Directory output")
    
    args = parser.parse_args()
    
    print(f"🔗 MERGE BASELINE RESULTS WITH XML")
    print("=" * 50)
    print(f"🤖 Modello: {args.model_name}")
    print(f"📁 Risultati: {os.path.basename(args.results_file)}")
    print(f"📊 Dataset: {os.path.basename(args.dataset_file)}")
    print("=" * 50)
    
    # Verifica file esistano
    if not os.path.exists(args.results_file):
        print(f"❌ File risultati non trovato: {args.results_file}")
        return
    
    if not os.path.exists(args.dataset_file):
        print(f"❌ Dataset non trovato: {args.dataset_file}")
        return
    
    # Merge
    output_file = merge_results_with_xml(
        args.results_file,
        args.dataset_file,
        args.model_name,
        args.output_dir
    )
    
    print(f"\n✅ MERGE COMPLETATO!")
    print(f"📁 Output: {output_file}")

if __name__ == "__main__":
    main()
