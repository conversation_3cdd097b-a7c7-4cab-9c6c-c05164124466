#!/usr/bin/env python3
"""
📊 CALCOLO METRICHE BASELINE NUOVO DATASET
Calcola metriche sui risultati baseline con dataset corretto
"""

import os
import json
import argparse
import logging
from datetime import datetime
import nltk
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from nltk.translate.meteor_score import meteor_score
from rouge_score import rouge_scorer

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Download NLTK data se necessario
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

try:
    nltk.data.find('corpora/wordnet')
except LookupError:
    nltk.download('wordnet')

def calculate_bleu_scores(reference, candidate):
    """Calcola BLEU scores (1-4)"""
    try:
        # Tokenizza
        ref_tokens = nltk.word_tokenize(reference.lower())
        cand_tokens = nltk.word_tokenize(candidate.lower())
        
        # Smoothing per evitare score 0
        smoothing = SmoothingFunction().method1
        
        # Calcola BLEU 1-4
        bleu_1 = sentence_bleu([ref_tokens], cand_tokens, weights=(1, 0, 0, 0), smoothing_function=smoothing)
        bleu_2 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.5, 0.5, 0, 0), smoothing_function=smoothing)
        bleu_3 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.33, 0.33, 0.33, 0), smoothing_function=smoothing)
        bleu_4 = sentence_bleu([ref_tokens], cand_tokens, weights=(0.25, 0.25, 0.25, 0.25), smoothing_function=smoothing)
        
        return {
            'bleu_1': bleu_1,
            'bleu_2': bleu_2,
            'bleu_3': bleu_3,
            'bleu_4': bleu_4
        }
    except Exception as e:
        logger.warning(f"Errore BLEU: {e}")
        return {'bleu_1': 0.0, 'bleu_2': 0.0, 'bleu_3': 0.0, 'bleu_4': 0.0}

def calculate_meteor_score(reference, candidate):
    """Calcola METEOR score"""
    try:
        # Tokenizza
        ref_tokens = nltk.word_tokenize(reference.lower())
        cand_tokens = nltk.word_tokenize(candidate.lower())
        
        score = meteor_score([ref_tokens], cand_tokens)
        return score
    except Exception as e:
        logger.warning(f"Errore METEOR: {e}")
        return 0.0

def calculate_rouge_scores(reference, candidate):
    """Calcola ROUGE scores"""
    try:
        scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
        scores = scorer.score(reference, candidate)
        
        return {
            'rouge_1': scores['rouge1'].fmeasure,
            'rouge_2': scores['rouge2'].fmeasure,
            'rouge_l': scores['rougeL'].fmeasure
        }
    except Exception as e:
        logger.warning(f"Errore ROUGE: {e}")
        return {'rouge_1': 0.0, 'rouge_2': 0.0, 'rouge_l': 0.0}

def calculate_comprehensive_metrics(results_file, output_dir):
    """Calcola metriche comprehensive sui risultati"""
    logger.info(f"📊 Calcolo metriche: {results_file}")
    
    # Carica risultati
    with open(results_file, 'r') as f:
        results = json.load(f)
    
    logger.info(f"📊 Risultati caricati: {len(results)} esempi")
    
    # Inizializza accumulatori
    all_metrics = {
        'bleu_1': [],
        'bleu_2': [],
        'bleu_3': [],
        'bleu_4': [],
        'meteor': [],
        'rouge_1': [],
        'rouge_2': [],
        'rouge_l': []
    }
    
    # Calcola metriche per ogni esempio
    for i, result in enumerate(results):
        try:
            ground_truth = result.get('ground_truth', '')
            generated = result.get('generated_caption', '')
            
            if not ground_truth or not generated:
                logger.warning(f"⚠️ Esempio {i} mancante ground_truth o generated_caption")
                continue
            
            # BLEU scores
            bleu_scores = calculate_bleu_scores(ground_truth, generated)
            all_metrics['bleu_1'].append(bleu_scores['bleu_1'])
            all_metrics['bleu_2'].append(bleu_scores['bleu_2'])
            all_metrics['bleu_3'].append(bleu_scores['bleu_3'])
            all_metrics['bleu_4'].append(bleu_scores['bleu_4'])
            
            # METEOR score
            meteor = calculate_meteor_score(ground_truth, generated)
            all_metrics['meteor'].append(meteor)
            
            # ROUGE scores
            rouge_scores = calculate_rouge_scores(ground_truth, generated)
            all_metrics['rouge_1'].append(rouge_scores['rouge_1'])
            all_metrics['rouge_2'].append(rouge_scores['rouge_2'])
            all_metrics['rouge_l'].append(rouge_scores['rouge_l'])
            
            if (i + 1) % 10 == 0:
                logger.info(f"   📈 Processati {i+1}/{len(results)} esempi")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
    
    # Calcola statistiche finali
    final_metrics = {}
    for metric_name, values in all_metrics.items():
        if values:
            final_metrics[metric_name] = {
                'mean': sum(values) / len(values),
                'min': min(values),
                'max': max(values),
                'count': len(values)
            }
        else:
            final_metrics[metric_name] = {
                'mean': 0.0,
                'min': 0.0,
                'max': 0.0,
                'count': 0
            }
    
    # Aggiungi metadata
    model_name = results[0].get('model', 'unknown') if results else 'unknown'
    final_metrics['metadata'] = {
        'model': model_name,
        'total_examples': len(results),
        'valid_examples': len(all_metrics['bleu_1']),
        'timestamp': datetime.now().isoformat(),
        'dataset_type': 'baseline_corrected_colors'
    }
    
    # Salva metriche
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    metrics_file = os.path.join(output_dir, f"{model_name}_comprehensive_metrics_{timestamp}.json")
    
    with open(metrics_file, 'w') as f:
        json.dump(final_metrics, f, indent=2)
    
    # Log risultati
    logger.info("=" * 60)
    logger.info("📊 METRICHE COMPREHENSIVE CALCOLATE!")
    logger.info("=" * 60)
    logger.info(f"🤖 Modello: {model_name}")
    logger.info(f"📈 Esempi validi: {final_metrics['metadata']['valid_examples']}/{final_metrics['metadata']['total_examples']}")
    logger.info("")
    logger.info("📊 RISULTATI METRICHE:")
    for metric_name in ['bleu_1', 'bleu_2', 'bleu_3', 'bleu_4', 'meteor', 'rouge_1', 'rouge_2', 'rouge_l']:
        mean_score = final_metrics[metric_name]['mean']
        logger.info(f"   {metric_name.upper()}: {mean_score:.4f} ({mean_score*100:.2f}%)")
    logger.info("")
    logger.info(f"📄 File metriche: {metrics_file}")
    logger.info("=" * 60)
    
    return metrics_file

def main():
    parser = argparse.ArgumentParser(description="Calcola metriche baseline nuovo dataset")
    parser.add_argument("--results_file", required=True, help="File risultati JSON")
    parser.add_argument("--output_dir", required=True, help="Directory output")
    
    args = parser.parse_args()
    
    print("📊 CALCOLO METRICHE BASELINE NUOVO DATASET")
    print("=" * 60)
    print(f"📄 File risultati: {args.results_file}")
    print(f"📁 Output: {args.output_dir}")
    print("=" * 60)
    
    if not os.path.exists(args.results_file):
        logger.error(f"❌ File risultati non trovato: {args.results_file}")
        return 1
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Calcola metriche
    metrics_file = calculate_comprehensive_metrics(args.results_file, args.output_dir)
    
    if metrics_file:
        logger.info("🎉 CALCOLO METRICHE COMPLETATO!")
        return 0
    else:
        logger.error("❌ Calcolo metriche fallito")
        return 1

if __name__ == "__main__":
    exit(main())
