#!/usr/bin/env python3
"""
🎯 VALUTAZIONE COMPLETA TUTTI I BASELINE
Florence-2, Idefics3, BLIP-2, Gemma-T9 con REAL CLIP
"""

import os
import sys
import json
import torch
import logging
import argparse
from datetime import datetime
from pathlib import Path
import numpy as np
from PIL import Image
import cairosvg
from io import BytesIO
import base64
import re
import gc

# Transformers e modelli
from transformers import (
    AutoProcessor, AutoModelForCausalLM,
    Blip2Processor, Blip2ForConditionalGeneration,
    AutoTokenizer
)

# CLIP per metriche REALI
import clip

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def convert_custom_xml_to_svg(xml_content):
    """Converte il nostro formato XML custom a SVG standard"""
    try:
        lines = xml_content.strip().split('\n')
        svg_paths = []
        
        for line in lines:
            if '\t' in line:
                style_part, d_part = line.split('\t', 1)
                
                # Estrai attributi style
                style_attrs = {}
                for attr in style_part.split(';'):
                    if '=' in attr:
                        key, value = attr.split('=', 1)
                        style_attrs[key] = value
                
                # Crea elemento path SVG
                path_element = f'<path d="{d_part}" '
                
                # Aggiungi attributi style
                if 'fill' in style_attrs:
                    path_element += f'fill="{style_attrs["fill"]}" '
                if 'stroke' in style_attrs and style_attrs['stroke'] != 'None':
                    path_element += f'stroke="{style_attrs["stroke"]}" '
                if 'stroke-width' in style_attrs:
                    path_element += f'stroke-width="{style_attrs["stroke-width"]}" '
                if 'opacity' in style_attrs:
                    path_element += f'opacity="{style_attrs["opacity"]}" '
                
                path_element += '/>'
                svg_paths.append(path_element)
        
        # Crea SVG completo
        svg_content = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">
{chr(10).join(svg_paths)}
</svg>'''
        
        return svg_content
        
    except Exception as e:
        logger.error(f"Errore conversione XML: {e}")
        return None

def svg_to_image(svg_content, size=(224, 224)):
    """Converte SVG in immagine PIL"""
    try:
        # Converti SVG in PNG
        png_data = cairosvg.svg2png(bytestring=svg_content.encode('utf-8'))
        
        # Carica come PIL Image
        image = Image.open(BytesIO(png_data)).convert('RGB')
        
        # Ridimensiona
        image = image.resize(size, Image.Resampling.LANCZOS)
        
        return image
        
    except Exception as e:
        logger.error(f"Errore conversione SVG->Image: {e}")
        return None

def evaluate_florence2(dataset_path, output_file, max_examples=None):
    """Valuta Florence-2"""
    logger.info("🎨 Valutazione Florence-2...")
    
    try:
        # Carica modello
        model = AutoModelForCausalLM.from_pretrained(
            "microsoft/Florence-2-large", 
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        processor = AutoProcessor.from_pretrained("microsoft/Florence-2-large", trust_remote_code=True)
        
        # Carica dataset
        with open(dataset_path, 'r') as f:
            dataset = json.load(f)
        
        if max_examples:
            dataset = dataset[:max_examples]
        
        results = []
        
        for i, item in enumerate(dataset):
            logger.info(f"   Esempio {i+1}/{len(dataset)}")
            
            try:
                # Converti XML a SVG
                svg_content = convert_custom_xml_to_svg(item['xml_content'])
                if not svg_content:
                    continue
                
                # Converti a immagine
                image = svg_to_image(svg_content)
                if not image:
                    continue
                
                # Genera caption
                prompt = "<MORE_DETAILED_CAPTION>"
                inputs = processor(text=prompt, images=image, return_tensors="pt").to(model.device)
                
                with torch.no_grad():
                    generated_ids = model.generate(
                        input_ids=inputs["input_ids"],
                        pixel_values=inputs["pixel_values"],
                        max_new_tokens=1024,
                        num_beams=3,
                        do_sample=False
                    )
                
                generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]
                
                # Estrai caption
                caption = generated_text.split(prompt)[-1].strip()
                
                results.append({
                    'id': item.get('id', i),
                    'xml_content': item['xml_content'],
                    'ground_truth': item['caption'],
                    'generated_caption': caption,
                    'model': 'Florence-2'
                })
                
                # Cleanup periodico
                if (i + 1) % 10 == 0:
                    gc.collect()
                    torch.cuda.empty_cache()
                
            except Exception as e:
                logger.error(f"Errore esempio {i}: {e}")
                continue
        
        # Salva risultati
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"✅ Florence-2 completato: {len(results)} esempi -> {output_file}")
        
        # Cleanup
        del model
        del processor
        gc.collect()
        torch.cuda.empty_cache()
        
        return len(results)
        
    except Exception as e:
        logger.error(f"❌ Errore Florence-2: {e}")
        return 0

def evaluate_idefics3(dataset_path, output_file, max_examples=None):
    """Valuta Idefics3"""
    logger.info("🤖 Valutazione Idefics3...")
    
    try:
        # Carica modello
        model = AutoModelForCausalLM.from_pretrained(
            "HuggingFaceM4/Idefics3-8B-Llama3",
            torch_dtype=torch.float16,
            device_map="auto",
            trust_remote_code=True
        )
        processor = AutoProcessor.from_pretrained("HuggingFaceM4/Idefics3-8B-Llama3")
        
        # Carica dataset
        with open(dataset_path, 'r') as f:
            dataset = json.load(f)
        
        if max_examples:
            dataset = dataset[:max_examples]
        
        results = []
        
        for i, item in enumerate(dataset):
            logger.info(f"   Esempio {i+1}/{len(dataset)}")
            
            try:
                # Converti XML a SVG
                svg_content = convert_custom_xml_to_svg(item['xml_content'])
                if not svg_content:
                    continue
                
                # Converti a immagine
                image = svg_to_image(svg_content)
                if not image:
                    continue
                
                # Prepara prompt
                messages = [
                    {
                        "role": "user",
                        "content": [
                            {"type": "image"},
                            {"type": "text", "text": "Describe this image in detail."}
                        ]
                    }
                ]
                
                prompt = processor.apply_chat_template(messages, add_generation_prompt=True)
                inputs = processor(text=prompt, images=[image], return_tensors="pt").to(model.device)
                
                # Genera
                with torch.no_grad():
                    generated_ids = model.generate(
                        **inputs,
                        max_new_tokens=500,
                        do_sample=True,
                        temperature=0.7
                    )
                
                generated_text = processor.batch_decode(generated_ids, skip_special_tokens=True)[0]
                
                # Estrai caption (dopo l'ultimo "assistant")
                if "assistant" in generated_text:
                    caption = generated_text.split("assistant")[-1].strip()
                else:
                    caption = generated_text.strip()
                
                results.append({
                    'id': item.get('id', i),
                    'xml_content': item['xml_content'],
                    'ground_truth': item['caption'],
                    'generated_caption': caption,
                    'model': 'Idefics3'
                })
                
                # Cleanup periodico
                if (i + 1) % 10 == 0:
                    gc.collect()
                    torch.cuda.empty_cache()
                
            except Exception as e:
                logger.error(f"Errore esempio {i}: {e}")
                continue
        
        # Salva risultati
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"✅ Idefics3 completato: {len(results)} esempi -> {output_file}")
        
        # Cleanup
        del model
        del processor
        gc.collect()
        torch.cuda.empty_cache()
        
        return len(results)
        
    except Exception as e:
        logger.error(f"❌ Errore Idefics3: {e}")
        return 0

def main():
    parser = argparse.ArgumentParser(description="Valuta tutti i baseline mancanti")
    parser.add_argument("--dataset_path", required=True, help="Path al dataset")
    parser.add_argument("--output_dir", default="evaluation_results", help="Directory output")
    parser.add_argument("--max_examples", type=int, help="Max esempi da valutare")
    parser.add_argument("--models", nargs="+", default=["florence2", "idefics3"], 
                       help="Modelli da valutare")
    
    args = parser.parse_args()
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d")
    
    logger.info("🎯 VALUTAZIONE BASELINE MANCANTI")
    logger.info("=" * 50)
    logger.info(f"Dataset: {args.dataset_path}")
    logger.info(f"Output: {args.output_dir}")
    logger.info(f"Max esempi: {args.max_examples}")
    logger.info(f"Modelli: {args.models}")
    
    results_summary = {}
    
    # Florence-2
    if "florence2" in args.models:
        output_file = os.path.join(args.output_dir, f"florence2_results_{timestamp}.json")
        count = evaluate_florence2(args.dataset_path, output_file, args.max_examples)
        results_summary["Florence-2"] = {"file": output_file, "count": count}
    
    # Idefics3
    if "idefics3" in args.models:
        output_file = os.path.join(args.output_dir, f"idefics3_results_{timestamp}.json")
        count = evaluate_idefics3(args.dataset_path, output_file, args.max_examples)
        results_summary["Idefics3"] = {"file": output_file, "count": count}
    
    # Summary
    logger.info("\n" + "=" * 50)
    logger.info("✅ VALUTAZIONE COMPLETATA!")
    logger.info("=" * 50)
    
    for model, info in results_summary.items():
        logger.info(f"{model}: {info['count']} esempi -> {info['file']}")
    
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
