#!/usr/bin/env python3
"""
📊 CALCOLO METRICHE BASELINE COMPLETE - 400 ESEMPI
Calcola tutte le metriche (BLEU, METEOR, CIDEr, CLIPScore) per i risultati baseline
"""

import os
import json
import argparse
import logging
from datetime import datetime
import numpy as np
from PIL import Image
import torch

# Import metriche
try:
    from shared.utils.metrics import CaptionEvaluator
except ImportError:
    # Fallback se il modulo non è disponibile
    import sys
    sys.path.append('/work/tesi_ediluzio')
    from shared.utils.metrics import CaptionEvaluator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_results(results_file):
    """Carica i risultati di un modello"""
    logger.info(f"📊 Caricamento risultati: {results_file}")
    
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    results = data.get('results', [])
    model_name = data.get('model', 'Unknown')
    
    # Filtra solo risultati validi (senza errori)
    valid_results = [r for r in results if not r['generated_caption'].startswith('ERROR')]
    
    logger.info(f"✅ Risultati caricati: {len(valid_results)}/{len(results)} validi per {model_name}")
    
    return valid_results, model_name

def calculate_comprehensive_metrics(results, model_name, output_file, image_base_path=None):
    """Calcola tutte le metriche per i risultati"""
    logger.info(f"🧮 Calcolo metriche per {model_name}...")
    
    # Prepara dati per evaluator
    ground_truths = [r['ground_truth'] for r in results]
    predictions = [r['generated_caption'] for r in results]
    
    # Prepara path immagini per CLIPScore
    image_paths = None
    if image_base_path:
        image_paths = []
        for r in results:
            if 'image_path' in r:
                image_paths.append(r['image_path'])
            else:
                # Costruisci path dall'image_base_path e filename
                filename = r.get('filename', f"baseline_{r['id']:04d}.png")
                image_paths.append(os.path.join(image_base_path, filename))
    
    # Inizializza evaluator
    evaluator = CaptionEvaluator()
    
    # Calcola metriche
    try:
        metrics = evaluator.calculate_all_metrics(
            predictions=predictions,
            references=ground_truths,
            image_paths=image_paths
        )
        
        # Aggiungi metadati
        metrics['metadata'] = {
            'model': model_name,
            'total_examples': len(results),
            'valid_examples': len(results),
            'timestamp': datetime.now().isoformat(),
            'dataset_type': 'baseline_complete_400'
        }
        
        # Salva metriche
        with open(output_file, 'w') as f:
            json.dump(metrics, f, indent=2)
        
        logger.info(f"✅ Metriche calcolate e salvate: {output_file}")
        
        # Log metriche principali
        logger.info(f"📊 Metriche {model_name}:")
        logger.info(f"   BLEU-1: {metrics.get('bleu_1', {}).get('mean', 0):.4f}")
        logger.info(f"   BLEU-4: {metrics.get('bleu_4', {}).get('mean', 0):.4f}")
        logger.info(f"   METEOR: {metrics.get('meteor', {}).get('mean', 0):.4f}")
        logger.info(f"   CIDEr: {metrics.get('cider', {}).get('mean', 0):.4f}")
        if 'clip_score' in metrics:
            logger.info(f"   CLIPScore: {metrics.get('clip_score', {}).get('mean', 0):.4f}")
        
        return metrics
        
    except Exception as e:
        logger.error(f"❌ Errore calcolo metriche per {model_name}: {e}")
        return None

def create_comparison_report(all_metrics, output_file):
    """Crea report di confronto tra tutti i modelli"""
    logger.info("📋 Creazione report di confronto...")
    
    comparison = {
        'timestamp': datetime.now().isoformat(),
        'dataset': 'baseline_complete_400',
        'models': {},
        'summary': {}
    }
    
    # Raccogli metriche per ogni modello
    metric_names = ['bleu_1', 'bleu_2', 'bleu_3', 'bleu_4', 'meteor', 'cider', 'clip_score', 'rouge_l']
    
    for model_name, metrics in all_metrics.items():
        comparison['models'][model_name] = {}
        
        for metric_name in metric_names:
            if metric_name in metrics:
                comparison['models'][model_name][metric_name] = metrics[metric_name].get('mean', 0)
    
    # Crea summary con ranking
    comparison['summary']['ranking'] = {}
    
    for metric_name in metric_names:
        if any(metric_name in metrics for metrics in all_metrics.values()):
            # Ordina modelli per questa metrica
            model_scores = []
            for model_name, metrics in all_metrics.items():
                if metric_name in metrics:
                    score = metrics[metric_name].get('mean', 0)
                    model_scores.append((model_name, score))
            
            # Ordina in ordine decrescente
            model_scores.sort(key=lambda x: x[1], reverse=True)
            comparison['summary']['ranking'][metric_name] = model_scores
    
    # Salva report
    with open(output_file, 'w') as f:
        json.dump(comparison, f, indent=2)
    
    logger.info(f"✅ Report di confronto salvato: {output_file}")
    
    # Log summary
    logger.info("🏆 Ranking per metrica:")
    for metric_name, ranking in comparison['summary']['ranking'].items():
        logger.info(f"   {metric_name.upper()}:")
        for i, (model, score) in enumerate(ranking[:3]):  # Top 3
            logger.info(f"     {i+1}. {model}: {score:.4f}")
    
    return comparison

def main():
    parser = argparse.ArgumentParser(description='Calcolo Metriche Baseline Complete')
    parser.add_argument('--results_dir', 
                       default='evaluation_results/baseline_COMPLETE_400',
                       help='Directory con i risultati')
    parser.add_argument('--image_base_path',
                       default='data/processed/baseline_dataset_COMPLETE/images',
                       help='Path base per le immagini (per CLIPScore)')
    parser.add_argument('--output_dir',
                       default='evaluation_results/baseline_COMPLETE_400',
                       help='Directory output per le metriche')
    
    args = parser.parse_args()
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Trova tutti i file di risultati
    results_files = []
    for filename in os.listdir(args.results_dir):
        if filename.endswith('_results_*.json') and not filename.endswith('_metrics.json'):
            results_files.append(os.path.join(args.results_dir, filename))
    
    if not results_files:
        logger.error(f"❌ Nessun file di risultati trovato in {args.results_dir}")
        return
    
    logger.info(f"📁 Trovati {len(results_files)} file di risultati")
    
    # Calcola metriche per ogni modello
    all_metrics = {}
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    for results_file in results_files:
        try:
            # Carica risultati
            results, model_name = load_results(results_file)
            
            if not results:
                logger.warning(f"⚠️ Nessun risultato valido in {results_file}")
                continue
            
            # Calcola metriche
            metrics_file = os.path.join(args.output_dir, f'{model_name.lower()}_metrics_{timestamp}.json')
            metrics = calculate_comprehensive_metrics(
                results, 
                model_name, 
                metrics_file,
                args.image_base_path
            )
            
            if metrics:
                all_metrics[model_name] = metrics
        
        except Exception as e:
            logger.error(f"❌ Errore elaborazione {results_file}: {e}")
    
    # Crea report di confronto
    if all_metrics:
        comparison_file = os.path.join(args.output_dir, f'baseline_comparison_{timestamp}.json')
        create_comparison_report(all_metrics, comparison_file)
        
        logger.info(f"🎉 Elaborazione completata! Metriche per {len(all_metrics)} modelli")
    else:
        logger.error("❌ Nessuna metrica calcolata")

if __name__ == "__main__":
    main()
