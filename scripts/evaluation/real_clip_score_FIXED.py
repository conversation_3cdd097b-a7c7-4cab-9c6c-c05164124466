#!/usr/bin/env python3
"""
🎯 REAL CLIP SCORE FIXED
Calcola vero CLIP Score usando conversione SVG→PNG FIXED
"""

import os
import json
import argparse
import logging
import re
import tempfile
import subprocess
import threading
from datetime import datetime
from PIL import Image
import torch
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def parse_xml_to_svg_FIXED(xml_content):
    """Parser SVG MIGLIORATO che fixa tutti i problemi"""
    
    # Inizia SVG con header corretto
    svg_parts = []
    svg_parts.append('<?xml version="1.0" encoding="UTF-8"?>')
    svg_parts.append('<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" width="512" height="512">')
    svg_parts.append('<rect width="512" height="512" fill="white"/>')  # Sfondo bianco
    
    # Split per linee
    lines = xml_content.strip().split('\n')
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # Cerca pattern style= e d=
        style_match = re.search(r'style=([^\\t]+)', line)
        d_match = re.search(r'd=(.+)', line)
        
        if style_match and d_match:
            style_content = style_match.group(1)
            d_content = d_match.group(1)
            
            # Fixa colori RGB
            style_content = re.sub(r'rgb\((\d+),(\d+),(\d+)\)', r'rgb(\1,\2,\3)', style_content)
            
            # Crea path SVG valido
            svg_parts.append(f'<path style="{style_content}" d="{d_content}"/>')
    
    svg_parts.append('</svg>')
    
    return '\n'.join(svg_parts)

def svg_to_png_subprocess_FIXED(svg_content, output_path, size=224):
    """Conversione con subprocess usando file temporaneo"""
    
    # Salva SVG in file temporaneo
    with tempfile.NamedTemporaryFile(mode='w', suffix='.svg', delete=False) as f:
        f.write(svg_content)
        svg_temp = f.name
    
    success = False
    
    try:
        # Metodo 1: rsvg-convert (se disponibile)
        try:
            cmd = [
                'rsvg-convert',
                '--width', str(size),
                '--height', str(size),
                '--background-color', 'white',
                '--output', output_path,
                svg_temp
            ]
            
            result = subprocess.run(cmd, timeout=30, capture_output=True, text=True)
            if result.returncode == 0 and os.path.exists(output_path):
                logger.debug(f"✅ rsvg-convert: {output_path}")
                success = True
            else:
                logger.debug(f"rsvg-convert failed: {result.stderr}")
        except FileNotFoundError:
            logger.debug("rsvg-convert non trovato")
        except Exception as e:
            logger.debug(f"rsvg-convert error: {e}")
        
        # Metodo 2: inkscape (se disponibile)
        if not success:
            try:
                cmd = [
                    'inkscape',
                    '--export-type=png',
                    f'--export-width={size}',
                    f'--export-height={size}',
                    f'--export-filename={output_path}',
                    svg_temp
                ]
                
                result = subprocess.run(cmd, timeout=30, capture_output=True, text=True)
                if result.returncode == 0 and os.path.exists(output_path):
                    logger.debug(f"✅ inkscape: {output_path}")
                    success = True
                else:
                    logger.debug(f"inkscape failed: {result.stderr}")
            except FileNotFoundError:
                logger.debug("inkscape non trovato")
            except Exception as e:
                logger.debug(f"inkscape error: {e}")
        
        # Metodo 3: ImageMagick convert (se disponibile)
        if not success:
            try:
                cmd = [
                    'convert',
                    '-background', 'white',
                    '-size', f'{size}x{size}',
                    svg_temp,
                    output_path
                ]
                
                result = subprocess.run(cmd, timeout=30, capture_output=True, text=True)
                if result.returncode == 0 and os.path.exists(output_path):
                    logger.debug(f"✅ convert: {output_path}")
                    success = True
                else:
                    logger.debug(f"convert failed: {result.stderr}")
            except FileNotFoundError:
                logger.debug("convert non trovato")
            except Exception as e:
                logger.debug(f"convert error: {e}")
        
    finally:
        # Pulizia file temporaneo
        try:
            os.unlink(svg_temp)
        except:
            pass
    
    return success

def svg_to_png_cairosvg_FIXED(svg_content, output_path, size=224):
    """CairoSVG con timeout e error handling migliorato"""
    try:
        import cairosvg
        
        # Usa threading per timeout
        result = [False]
        exception = [None]
        
        def convert_thread():
            try:
                cairosvg.svg2png(
                    bytestring=svg_content.encode('utf-8'),
                    write_to=output_path,
                    output_width=size,
                    output_height=size,
                    background_color='white',
                    dpi=72
                )
                result[0] = os.path.exists(output_path)
            except Exception as e:
                exception[0] = e
        
        thread = threading.Thread(target=convert_thread)
        thread.daemon = True
        thread.start()
        thread.join(timeout=15)  # 15 secondi timeout
        
        if thread.is_alive():
            logger.debug("CairoSVG timeout")
            return False
        
        if exception[0]:
            logger.debug(f"CairoSVG error: {exception[0]}")
            return False
        
        if result[0]:
            logger.debug(f"✅ cairosvg: {output_path}")
            return True
        
        return False
        
    except ImportError:
        logger.debug("CairoSVG non disponibile")
        return False
    except Exception as e:
        logger.debug(f"CairoSVG setup error: {e}")
        return False

def convert_single_svg_FIXED(xml_content, output_path, size=224):
    """Converte singolo SVG con tutti i metodi disponibili"""
    
    # Parse XML in SVG valido
    try:
        svg_content = parse_xml_to_svg_FIXED(xml_content)
    except Exception as e:
        logger.error(f"Errore parsing XML: {e}")
        return False
    
    # Prova tutti i metodi
    methods = [
        ("subprocess", lambda: svg_to_png_subprocess_FIXED(svg_content, output_path, size)),
        ("cairosvg", lambda: svg_to_png_cairosvg_FIXED(svg_content, output_path, size))
    ]
    
    for method_name, method_func in methods:
        try:
            if method_func():
                return True
        except Exception as e:
            logger.debug(f"{method_name} failed: {e}")
            continue
    
    return False

def setup_clip_transformers():
    """Setup CLIP usando transformers"""
    try:
        from transformers import CLIPProcessor, CLIPModel
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"🔧 CLIP su device: {device}")
        
        model_name = "openai/clip-vit-base-patch32"
        model = CLIPModel.from_pretrained(model_name).to(device)
        processor = CLIPProcessor.from_pretrained(model_name)
        
        return model, processor, device
    except Exception as e:
        logger.error(f"❌ Errore setup CLIP: {e}")
        return None, None, None

def calculate_clip_score_real(image_path, text, model, processor, device):
    """Calcola vero CLIP Score"""
    try:
        image = Image.open(image_path).convert('RGB')
        
        inputs = processor(text=[text], images=image, return_tensors="pt", padding=True)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = model(**inputs)
            logits_per_image = outputs.logits_per_image
            clip_score = torch.diagonal(logits_per_image).cpu().numpy()[0]
            normalized_score = 1 / (1 + np.exp(-clip_score))
            
            return float(normalized_score)
            
    except Exception as e:
        logger.warning(f"Errore CLIP Score: {e}")
        return 0.0

def calculate_real_clip_scores_FIXED(results_file, model_name, max_examples=50):
    """Calcola REAL CLIP Scores FIXED"""
    logger.info(f"🎯 Calcolo REAL CLIP Scores FIXED per {model_name} (max {max_examples} esempi)")
    
    # Carica risultati
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    results = data.get('results', [])
    
    if len(results) > max_examples:
        results = results[:max_examples]
        logger.info(f"📊 Limitato a {max_examples} esempi")
    
    # Setup CLIP
    model, processor, device = setup_clip_transformers()
    if model is None:
        return []
    
    # Directory temporanea
    temp_dir = tempfile.mkdtemp(prefix="real_clip_fixed_")
    
    clip_scores = []
    successful = 0
    
    for i, result in enumerate(results):
        logger.info(f"📈 Processando esempio {i+1}/{len(results)}...")
        
        try:
            xml_content = result.get('xml_content', '') or result.get('xml', '')
            generated_caption = result.get('generated_caption', '')
            
            if not xml_content or not generated_caption:
                clip_scores.append(0.0)
                continue
            
            # Genera immagine PNG con metodo FIXED
            img_path = os.path.join(temp_dir, f"real_clip_{i}.png")
            
            if convert_single_svg_FIXED(xml_content, img_path, size=224):
                # Calcola REAL CLIP Score
                score = calculate_clip_score_real(img_path, generated_caption, model, processor, device)
                clip_scores.append(score)
                successful += 1
                
                logger.info(f"✅ Esempio {i}: REAL CLIP Score = {score:.4f}")
                
                # Rimuovi immagine
                try:
                    os.remove(img_path)
                except:
                    pass
            else:
                clip_scores.append(0.0)
                logger.warning(f"❌ Esempio {i}: Conversione fallita")
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            clip_scores.append(0.0)
    
    # Pulizia
    try:
        import shutil
        shutil.rmtree(temp_dir)
    except:
        pass
    
    logger.info(f"✅ REAL CLIP Scores FIXED calcolati: {successful}/{len(results)} successi")
    
    return clip_scores

def main():
    parser = argparse.ArgumentParser(description="Calcola REAL CLIP Score FIXED")
    parser.add_argument("--results_file", required=True, help="File JSON risultati")
    parser.add_argument("--model_name", required=True, help="Nome modello")
    parser.add_argument("--max_examples", type=int, default=50, help="Max esempi")
    parser.add_argument("--output_dir", default="evaluation_results/trained_models", help="Directory output")
    
    args = parser.parse_args()
    
    print(f"🎯 REAL CLIP SCORE FIXED")
    print("=" * 50)
    print(f"🤖 Modello: {args.model_name}")
    print(f"📁 File: {os.path.basename(args.results_file)}")
    print(f"📊 Max esempi: {args.max_examples}")
    print(f"🛠️ Metodi: Parser FIXED + Multi fallback")
    print("=" * 50)
    
    # Calcola REAL CLIP Scores FIXED
    clip_scores = calculate_real_clip_scores_FIXED(
        args.results_file,
        args.model_name,
        args.max_examples
    )
    
    if not clip_scores:
        print("❌ Nessun CLIP Score calcolato")
        return
    
    # Calcola statistiche
    valid_scores = [s for s in clip_scores if s > 0]
    
    if valid_scores:
        clip_stats = {
            'mean': float(np.mean(valid_scores)),
            'std': float(np.std(valid_scores)),
            'min': float(np.min(valid_scores)),
            'max': float(np.max(valid_scores)),
            'valid_count': len(valid_scores),
            'total_count': len(clip_scores)
        }
    else:
        clip_stats = {
            'mean': 0.0, 'std': 0.0, 'min': 0.0, 'max': 0.0,
            'valid_count': 0, 'total_count': len(clip_scores)
        }
    
    # Salva risultati
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = os.path.join(args.output_dir, f"{args.model_name}_REAL_CLIP_FIXED_{timestamp}.json")
    
    results = {
        'model': args.model_name,
        'timestamp': timestamp,
        'real_clip_scores_fixed': clip_scores,
        'statistics': clip_stats,
        'examples_processed': len(clip_scores),
        'method': 'real_clip_fixed_parser_multi_fallback'
    }
    
    os.makedirs(args.output_dir, exist_ok=True)
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Stampa risultati
    print("=" * 50)
    print("🎉 REAL CLIP SCORES FIXED CALCOLATI!")
    print("=" * 50)
    print(f"📊 REAL CLIP Score medio: {clip_stats['mean']:.4f} (±{clip_stats['std']:.4f})")
    print(f"📊 Range: {clip_stats['min']:.4f} - {clip_stats['max']:.4f}")
    print(f"📊 Scores validi: {clip_stats['valid_count']}/{clip_stats['total_count']}")
    print(f"📄 Risultati: {os.path.basename(output_file)}")
    print("=" * 50)
    print("✅ REAL CLIP Score FIXED completato!")

if __name__ == "__main__":
    main()
