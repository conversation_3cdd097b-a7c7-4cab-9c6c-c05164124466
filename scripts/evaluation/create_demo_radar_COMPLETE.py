#!/usr/bin/env python3
"""
📊 DEMO RADAR CHART - Mostra come apparirà il risultato finale
"""

import matplotlib.pyplot as plt
import numpy as np
from math import pi

# Configurazione matplotlib
plt.rcParams['font.family'] = 'DejaVu Sans'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.linewidth'] = 1.2

def create_demo_radar():
    """Crea un radar chart demo con dati realistici"""
    
    # Dati demo basati sui risultati esistenti e aspettative
    models_data = {
        'BLIP-2 (400 ex)': {
            'BLEU-1': 0.238,
            'BLEU-2': 0.132, 
            'BLEU-3': 0.076,
            'BLEU-4': 0.051,
            'METEOR': 0.223,
            'CIDEr': 0.443,
            'CLIPScore': 32.5
        },
        'Florence-2-FIXED (400 ex)': {
            'BLEU-1': 0.366,
            'BLEU-2': 0.153,
            'BLEU-3': 0.059,
            'BLEU-4': 0.028,
            'METEOR': 0.333,
            'CIDEr': 1.385,
            'CLIPScore': 33.3
        },
        'Idefics3-FIXED (400 ex)': {
            'BLEU-1': 0.301,
            'BLEU-2': 0.185,
            'BLEU-3': 0.121,
            'BLEU-4': 0.092,
            'METEOR': 0.385,
            'CIDEr': 4.582,
            'CLIPScore': 38.5
        }
    }
    
    # Definisci metriche e range appropriati
    metric_info = {
        'BLEU-1': {'range': (0, 0.5), 'format': '{:.3f}'},
        'BLEU-2': {'range': (0, 0.3), 'format': '{:.3f}'},
        'BLEU-3': {'range': (0, 0.2), 'format': '{:.3f}'},
        'BLEU-4': {'range': (0, 0.1), 'format': '{:.3f}'},
        'METEOR': {'range': (0, 0.5), 'format': '{:.3f}'},
        'CIDEr': {'range': (0, 5.0), 'format': '{:.3f}'},
        'CLIPScore': {'range': (0, 50), 'format': '{:.1f}'}
    }
    
    metrics = list(metric_info.keys())
    
    # Normalizza i valori
    normalized_data = {}
    for model_name, model_metrics in models_data.items():
        normalized_data[model_name] = {}
        for metric in metrics:
            value = model_metrics[metric]
            min_val, max_val = metric_info[metric]['range']
            normalized_val = ((value - min_val) / (max_val - min_val)) * 100
            normalized_val = max(0, min(100, normalized_val))
            normalized_data[model_name][metric] = normalized_val
    
    # Setup del grafico
    fig, ax = plt.subplots(figsize=(14, 12), subplot_kw=dict(projection='polar'))
    
    # Angoli per ogni metrica
    angles = [n / float(len(metrics)) * 2 * pi for n in range(len(metrics))]
    angles += angles[:1]  # Chiudi il cerchio
    
    # Colori per i modelli
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    
    # Disegna ogni modello
    for i, (model_name, model_metrics) in enumerate(normalized_data.items()):
        values = []
        for metric in metrics:
            values.append(model_metrics[metric])
        values += values[:1]  # Chiudi il cerchio
        
        # Disegna linea e area
        ax.plot(angles, values, 'o-', linewidth=3, label=model_name, color=colors[i], markersize=8)
        ax.fill(angles, values, alpha=0.25, color=colors[i])
    
    # Personalizza il grafico
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(metrics, fontsize=12, fontweight='bold')
    ax.set_ylim(0, 100)
    
    # Griglia radiale
    ax.set_yticks([20, 40, 60, 80, 100])
    ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'], fontsize=10)
    ax.grid(True, alpha=0.3)
    
    # Titolo e legenda
    plt.title('CONFRONTO MODELLI BASELINE\n(Scale Appropriate per Metrica)', 
              size=18, fontweight='bold', pad=30)
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=12)
    
    # Aggiungi valori reali come testo
    info_text = "Valori Reali:\n\n"
    for model_name, model_metrics in models_data.items():
        info_text += f"{model_name}:\n"
        for metric in metrics:
            value = model_metrics[metric]
            format_str = metric_info[metric]['format']
            info_text += f"  {metric}: {format_str.format(value)}\n"
        info_text += "\n"
    
    # Aggiungi box con valori reali
    plt.figtext(0.02, 0.02, info_text, fontsize=9, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    # Salva grafico
    output_file = 'evaluation_results/DEMO_baseline_radar_complete_400.png'
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight', facecolor='white')
    plt.savefig(output_file.replace('.png', '.pdf'), bbox_inches='tight', facecolor='white')
    
    print(f"✅ Demo radar chart salvato: {output_file}")
    print(f"✅ Demo radar chart PDF salvato: {output_file.replace('.png', '.pdf')}")
    
    plt.show()

if __name__ == "__main__":
    create_demo_radar()
