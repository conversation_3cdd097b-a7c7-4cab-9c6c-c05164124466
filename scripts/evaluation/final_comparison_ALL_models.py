#!/usr/bin/env python3
"""
🏆 CONFRONTO FINALE TUTTI I MODELLI
Confronta Gemma-T9 vs tutti i baseline (BLIP-2, Florence-2) con tutte le metriche
"""

import os
import json
import argparse
import logging
from datetime import datetime
import pandas as pd

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_metrics(metrics_file):
    """Carica metriche da file JSON"""
    with open(metrics_file, 'r') as f:
        data = json.load(f)
    return data

def create_comparison_table(metrics_files):
    """Crea tabella di confronto"""
    logger.info("📊 Creando tabella di confronto...")
    
    comparison_data = []
    
    for metrics_file in metrics_files:
        if not os.path.exists(metrics_file):
            logger.warning(f"⚠️ File non trovato: {metrics_file}")
            continue
            
        metrics = load_metrics(metrics_file)
        model_name = metrics.get('model', 'Unknown')
        
        # Estrai metriche
        row = {
            'Model': model_name,
            'Examples': metrics.get('total_examples', 0),
            'BLEU-1': f"{metrics.get('bleu_1', 0.0):.4f}",
            'BLEU-2': f"{metrics.get('bleu_2', 0.0):.4f}",
            'BLEU-3': f"{metrics.get('bleu_3', 0.0):.4f}",
            'BLEU-4': f"{metrics.get('bleu_4', 0.0):.4f}",
            'METEOR': f"{metrics.get('meteor', 0.0):.4f}",
            'CLIP Score': f"{metrics.get('clip_score', {}).get('mean', 0.0):.4f} ± {metrics.get('clip_score', {}).get('std', 0.0):.4f}",
            'CLIP Min': f"{metrics.get('clip_score', {}).get('min', 0.0):.4f}",
            'CLIP Max': f"{metrics.get('clip_score', {}).get('max', 0.0):.4f}",
            'Conversions': metrics.get('successful_conversions', 0)
        }
        
        comparison_data.append(row)
    
    return comparison_data

def print_comparison_table(comparison_data):
    """Stampa tabella di confronto formattata"""
    print("\n" + "="*120)
    print("🏆 CONFRONTO FINALE - TUTTE LE METRICHE")
    print("="*120)
    
    # Header
    print(f"{'Model':<12} {'Examples':<8} {'BLEU-1':<8} {'BLEU-2':<8} {'BLEU-3':<8} {'BLEU-4':<8} {'METEOR':<8} {'CLIP Score':<20} {'CLIP Min':<8} {'CLIP Max':<8}")
    print("-"*120)
    
    # Ordina per CLIP Score (media)
    comparison_data.sort(key=lambda x: float(x['CLIP Score'].split(' ±')[0]), reverse=True)
    
    for row in comparison_data:
        print(f"{row['Model']:<12} {row['Examples']:<8} {row['BLEU-1']:<8} {row['BLEU-2']:<8} {row['BLEU-3']:<8} {row['BLEU-4']:<8} {row['METEOR']:<8} {row['CLIP Score']:<20} {row['CLIP Min']:<8} {row['CLIP Max']:<8}")
    
    print("="*120)

def create_analysis(comparison_data):
    """Crea analisi dettagliata"""
    analysis = []
    
    analysis.append("🔍 ANALISI DETTAGLIATA")
    analysis.append("="*50)
    
    # Trova il migliore per ogni metrica
    best_bleu1 = max(comparison_data, key=lambda x: float(x['BLEU-1']))
    best_bleu4 = max(comparison_data, key=lambda x: float(x['BLEU-4']))
    best_meteor = max(comparison_data, key=lambda x: float(x['METEOR']))
    best_clip = max(comparison_data, key=lambda x: float(x['CLIP Score'].split(' ±')[0]))
    
    analysis.append(f"🥇 Migliore BLEU-1: {best_bleu1['Model']} ({best_bleu1['BLEU-1']})")
    analysis.append(f"🥇 Migliore BLEU-4: {best_bleu4['Model']} ({best_bleu4['BLEU-4']})")
    analysis.append(f"🥇 Migliore METEOR: {best_meteor['Model']} ({best_meteor['METEOR']})")
    analysis.append(f"🥇 Migliore CLIP Score: {best_clip['Model']} ({best_clip['CLIP Score']})")
    analysis.append("")
    
    # Analisi Gemma vs Baseline
    gemma_data = next((x for x in comparison_data if 'Gemma' in x['Model']), None)
    if gemma_data:
        analysis.append("📊 GEMMA-T9 vs BASELINE:")
        analysis.append("-"*30)
        
        for row in comparison_data:
            if 'Gemma' not in row['Model']:
                model_name = row['Model']
                
                # Confronto BLEU-4
                gemma_bleu4 = float(gemma_data['BLEU-4'])
                baseline_bleu4 = float(row['BLEU-4'])
                bleu4_improvement = ((gemma_bleu4 - baseline_bleu4) / max(baseline_bleu4, 0.0001)) * 100
                
                # Confronto METEOR
                gemma_meteor = float(gemma_data['METEOR'])
                baseline_meteor = float(row['METEOR'])
                meteor_improvement = ((gemma_meteor - baseline_meteor) / max(baseline_meteor, 0.0001)) * 100
                
                # Confronto CLIP
                gemma_clip = float(gemma_data['CLIP Score'].split(' ±')[0])
                baseline_clip = float(row['CLIP Score'].split(' ±')[0])
                clip_improvement = ((gemma_clip - baseline_clip) / baseline_clip) * 100
                
                analysis.append(f"vs {model_name}:")
                analysis.append(f"  BLEU-4: {bleu4_improvement:+.1f}%")
                analysis.append(f"  METEOR: {meteor_improvement:+.1f}%")
                analysis.append(f"  CLIP: {clip_improvement:+.1f}%")
                analysis.append("")
    
    return "\n".join(analysis)

def save_comparison_report(comparison_data, analysis, output_dir):
    """Salva report completo"""
    os.makedirs(output_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Report JSON
    json_file = os.path.join(output_dir, f"final_comparison_{timestamp}.json")
    report_data = {
        'timestamp': timestamp,
        'comparison_data': comparison_data,
        'analysis': analysis,
        'summary': {
            'total_models': len(comparison_data),
            'best_overall': max(comparison_data, key=lambda x: float(x['CLIP Score'].split(' ±')[0]))['Model']
        }
    }
    
    with open(json_file, 'w') as f:
        json.dump(report_data, f, indent=2)
    
    # Report TXT
    txt_file = os.path.join(output_dir, f"final_comparison_{timestamp}.txt")
    with open(txt_file, 'w') as f:
        f.write("🏆 CONFRONTO FINALE - TUTTI I MODELLI\n")
        f.write("="*50 + "\n\n")
        
        # Tabella
        f.write(f"{'Model':<12} {'Examples':<8} {'BLEU-1':<8} {'BLEU-2':<8} {'BLEU-3':<8} {'BLEU-4':<8} {'METEOR':<8} {'CLIP Score':<20}\n")
        f.write("-"*100 + "\n")
        
        for row in comparison_data:
            f.write(f"{row['Model']:<12} {row['Examples']:<8} {row['BLEU-1']:<8} {row['BLEU-2']:<8} {row['BLEU-3']:<8} {row['BLEU-4']:<8} {row['METEOR']:<8} {row['CLIP Score']:<20}\n")
        
        f.write("\n" + analysis)
    
    logger.info(f"📁 Report salvato: {json_file}")
    logger.info(f"📁 Report salvato: {txt_file}")
    
    return json_file, txt_file

def main():
    parser = argparse.ArgumentParser(description="Confronto finale tutti i modelli")
    parser.add_argument("--metrics_dir", default="evaluation_results/final_metrics", help="Directory metriche")
    parser.add_argument("--output_dir", default="evaluation_results/final_comparison", help="Directory output")
    
    args = parser.parse_args()
    
    print("🏆 CONFRONTO FINALE TUTTI I MODELLI")
    print("="*50)
    
    # Trova tutti i file di metriche
    metrics_files = []
    if os.path.exists(args.metrics_dir):
        for file in os.listdir(args.metrics_dir):
            if '_ALL_metrics_' in file and file.endswith('.json'):
                metrics_files.append(os.path.join(args.metrics_dir, file))
    
    if not metrics_files:
        print(f"❌ Nessun file di metriche trovato in: {args.metrics_dir}")
        return
    
    logger.info(f"📊 Trovati {len(metrics_files)} file di metriche")
    
    # Crea confronto
    comparison_data = create_comparison_table(metrics_files)
    
    # Stampa tabella
    print_comparison_table(comparison_data)
    
    # Crea analisi
    analysis = create_analysis(comparison_data)
    print("\n" + analysis)
    
    # Salva report
    json_file, txt_file = save_comparison_report(comparison_data, analysis, args.output_dir)
    
    print(f"\n✅ CONFRONTO COMPLETATO!")
    print(f"📁 Report JSON: {json_file}")
    print(f"📁 Report TXT: {txt_file}")

if __name__ == "__main__":
    main()
