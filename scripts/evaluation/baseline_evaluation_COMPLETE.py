#!/usr/bin/env python3
"""
🎯 BASELINE EVALUATION COMPLETA - 400 ESEMPI
Valuta i modelli baseline (BLIP-2, Florence-2, Idefics3) sul dataset completo di 400 immagini PNG
"""

import os
import json
import argparse
import logging
import gc
import time
from datetime import datetime
from PIL import Image
import torch
from transformers import (
    BlipProcessor, BlipForConditionalGeneration,
    AutoProcessor, AutoModelForCausalLM,
    Idefics3ForConditionalGeneration
)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clear_memory():
    """Pulisce memoria GPU e RAM"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    gc.collect()
    time.sleep(1)

def load_dataset_complete(dataset_file):
    """Carica il dataset completo di 400 esempi"""
    logger.info(f"📊 Caricamento dataset completo: {dataset_file}")
    
    with open(dataset_file, 'r') as f:
        data = json.load(f)
    
    logger.info(f"✅ Dataset caricato: {len(data)} esempi")
    
    # Verifica che le immagini esistano
    valid_data = []
    missing_images = 0
    
    for example in data:
        image_path = example.get('image_path', '')
        caption = example.get('caption', '')
        
        if os.path.exists(image_path) and caption:
            valid_data.append({
                'id': example.get('id', len(valid_data)),
                'image_path': image_path,
                'caption': caption,
                'filename': example.get('filename', os.path.basename(image_path))
            })
        else:
            missing_images += 1
            logger.warning(f"❌ Immagine mancante o caption vuoto: {image_path}")
    
    logger.info(f"✅ Esempi validi: {len(valid_data)}")
    if missing_images > 0:
        logger.warning(f"⚠️ Immagini mancanti: {missing_images}")
    
    return valid_data

class BaselineEvaluator:
    """Valutatore per modelli baseline"""

    def __init__(self, device='cpu', force_cpu=False):
        if force_cpu:
            self.device = 'cpu'
        else:
            self.device = device if torch.cuda.is_available() else 'cpu'
        logger.info(f"🖥️ Usando device: {self.device}")

        # Configurazione per evitare problemi di memoria
        if self.device == 'cpu':
            logger.info("🔧 Modalità CPU: Ottimizzazioni memoria attivate")
        else:
            logger.info("⚠️ Modalità GPU: Attenzione alla memoria VRAM")
        
    def evaluate_blip2(self, dataset, output_file):
        """Valuta BLIP-2"""
        logger.info("🔵 Iniziando valutazione BLIP-2...")
        
        try:
            # Carica modello BLIP-2 con ottimizzazioni
            logger.info("📥 Caricamento BLIP-2 processor...")
            processor = BlipProcessor.from_pretrained("Salesforce/blip2-opt-2.7b")

            logger.info("📥 Caricamento BLIP-2 model...")
            if self.device == 'cpu':
                # CPU mode: usa float32 e carica direttamente su CPU
                model = BlipForConditionalGeneration.from_pretrained(
                    "Salesforce/blip2-opt-2.7b",
                    torch_dtype=torch.float32,
                    device_map=None
                )
                model = model.to('cpu')
            else:
                # GPU mode: usa mixed precision
                model = BlipForConditionalGeneration.from_pretrained(
                    "Salesforce/blip2-opt-2.7b",
                    torch_dtype=torch.float16,
                    device_map="auto"
                )
            
            results = []
            start_time = time.time()
            
            for i, example in enumerate(dataset):
                try:
                    # Carica immagine
                    image = Image.open(example['image_path']).convert('RGB')
                    
                    # Genera caption
                    inputs = processor(image, return_tensors="pt").to(self.device)
                    
                    with torch.no_grad():
                        # Genera con parametri ottimizzati per memoria
                        generated_ids = model.generate(
                            **inputs,
                            max_length=80,  # Ridotto per memoria
                            num_beams=2,    # Ridotto per memoria
                            do_sample=False,
                            early_stopping=True
                        )
                    
                    generated_caption = processor.decode(generated_ids[0], skip_special_tokens=True)
                    
                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'ground_truth': example['caption'],
                        'generated_caption': generated_caption,
                        'image_path': example['image_path']
                    })
                    
                    # Progress
                    if (i + 1) % 10 == 0:
                        elapsed = time.time() - start_time
                        avg_time = elapsed / (i + 1)
                        remaining = avg_time * (len(dataset) - i - 1)
                        logger.info(f"📊 BLIP-2 Progress: {i+1}/{len(dataset)} "
                                  f"({(i+1)/len(dataset)*100:.1f}%) - "
                                  f"ETA: {remaining/60:.1f}min")
                    
                    # Pulizia memoria ogni 50 esempi
                    if (i + 1) % 50 == 0:
                        clear_memory()
                
                except Exception as e:
                    logger.error(f"❌ Errore esempio {i}: {e}")
                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'ground_truth': example['caption'],
                        'generated_caption': f"ERROR: {str(e)}",
                        'image_path': example['image_path']
                    })
            
            # Salva risultati
            output_data = {
                'model': 'BLIP-2',
                'model_name': 'Salesforce/blip2-opt-2.7b',
                'total_examples': len(dataset),
                'successful_examples': len([r for r in results if not r['generated_caption'].startswith('ERROR')]),
                'timestamp': datetime.now().isoformat(),
                'device': self.device,
                'results': results
            }
            
            with open(output_file, 'w') as f:
                json.dump(output_data, f, indent=2)
            
            total_time = time.time() - start_time
            logger.info(f"✅ BLIP-2 completato in {total_time/60:.1f}min")
            logger.info(f"💾 Risultati salvati: {output_file}")
            
            # Pulizia finale
            del model, processor
            clear_memory()
            
            return output_data
            
        except Exception as e:
            logger.error(f"❌ Errore BLIP-2: {e}")
            return None

    def evaluate_florence2(self, dataset, output_file):
        """Valuta Florence-2"""
        logger.info("🟠 Iniziando valutazione Florence-2...")

        try:
            # Carica modello Florence-2 con fix per mixed precision
            logger.info("📥 Caricamento Florence-2 processor...")
            processor = AutoProcessor.from_pretrained("microsoft/Florence-2-large", trust_remote_code=True)

            logger.info("📥 Caricamento Florence-2 model...")
            if self.device == 'cpu':
                # CPU mode: usa float32 consistente
                model = AutoModelForCausalLM.from_pretrained(
                    "microsoft/Florence-2-large",
                    torch_dtype=torch.float32,
                    trust_remote_code=True,
                    device_map=None
                )
                model = model.to('cpu')
            else:
                # GPU mode: usa float32 per evitare mixed precision issues
                model = AutoModelForCausalLM.from_pretrained(
                    "microsoft/Florence-2-large",
                    torch_dtype=torch.float32,  # Cambiato da float16 a float32
                    trust_remote_code=True,
                    device_map="auto"
                )

            results = []
            start_time = time.time()

            for i, example in enumerate(dataset):
                try:
                    # Carica immagine
                    image = Image.open(example['image_path']).convert('RGB')

                    # Prompt per caption dettagliata
                    prompt = "<MORE_DETAILED_CAPTION>"

                    # Genera caption
                    inputs = processor(text=prompt, images=image, return_tensors="pt").to(self.device)

                    with torch.no_grad():
                        # Genera con parametri ottimizzati
                        generated_ids = model.generate(
                            input_ids=inputs["input_ids"],
                            pixel_values=inputs["pixel_values"],
                            max_new_tokens=100,  # Ridotto per memoria
                            num_beams=2,         # Ridotto per memoria
                            do_sample=False,
                            early_stopping=True
                        )

                    generated_text = processor.batch_decode(generated_ids, skip_special_tokens=False)[0]

                    # Estrai solo la caption (rimuovi prompt)
                    generated_caption = generated_text.replace(prompt, "").strip()
                    if generated_caption.startswith("<MORE_DETAILED_CAPTION>"):
                        generated_caption = generated_caption.replace("<MORE_DETAILED_CAPTION>", "").strip()

                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'ground_truth': example['caption'],
                        'generated_caption': generated_caption,
                        'image_path': example['image_path']
                    })

                    # Progress
                    if (i + 1) % 10 == 0:
                        elapsed = time.time() - start_time
                        avg_time = elapsed / (i + 1)
                        remaining = avg_time * (len(dataset) - i - 1)
                        logger.info(f"📊 Florence-2 Progress: {i+1}/{len(dataset)} "
                                  f"({(i+1)/len(dataset)*100:.1f}%) - "
                                  f"ETA: {remaining/60:.1f}min")

                    # Pulizia memoria ogni 50 esempi
                    if (i + 1) % 50 == 0:
                        clear_memory()

                except Exception as e:
                    logger.error(f"❌ Errore esempio {i}: {e}")
                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'ground_truth': example['caption'],
                        'generated_caption': f"ERROR: {str(e)}",
                        'image_path': example['image_path']
                    })

            # Salva risultati
            output_data = {
                'model': 'Florence-2',
                'model_name': 'microsoft/Florence-2-large',
                'total_examples': len(dataset),
                'successful_examples': len([r for r in results if not r['generated_caption'].startswith('ERROR')]),
                'timestamp': datetime.now().isoformat(),
                'device': self.device,
                'results': results
            }

            with open(output_file, 'w') as f:
                json.dump(output_data, f, indent=2)

            total_time = time.time() - start_time
            logger.info(f"✅ Florence-2 completato in {total_time/60:.1f}min")
            logger.info(f"💾 Risultati salvati: {output_file}")

            # Pulizia finale
            del model, processor
            clear_memory()

            return output_data

        except Exception as e:
            logger.error(f"❌ Errore Florence-2: {e}")
            return None

    def evaluate_idefics3(self, dataset, output_file):
        """Valuta Idefics3"""
        logger.info("🟢 Iniziando valutazione Idefics3...")

        try:
            # Carica modello Idefics3 con ottimizzazioni memoria
            logger.info("📥 Caricamento Idefics3 processor...")
            processor = AutoProcessor.from_pretrained("HuggingFaceM4/Idefics3-8B-Llama3")

            logger.info("📥 Caricamento Idefics3 model...")
            if self.device == 'cpu':
                # CPU mode: usa float32
                model = Idefics3ForConditionalGeneration.from_pretrained(
                    "HuggingFaceM4/Idefics3-8B-Llama3",
                    torch_dtype=torch.float32,
                    device_map=None,
                    low_cpu_mem_usage=True
                )
                model = model.to('cpu')
            else:
                # GPU mode: usa ottimizzazioni memoria
                model = Idefics3ForConditionalGeneration.from_pretrained(
                    "HuggingFaceM4/Idefics3-8B-Llama3",
                    torch_dtype=torch.float32,  # Usa float32 invece di float16
                    device_map="auto",
                    low_cpu_mem_usage=True,
                    max_memory={0: "8GB"}  # Limita uso VRAM
                )

            results = []
            start_time = time.time()

            for i, example in enumerate(dataset):
                try:
                    # Carica immagine
                    image = Image.open(example['image_path']).convert('RGB')

                    # Prompt per caption dettagliata
                    messages = [
                        {
                            "role": "user",
                            "content": [
                                {"type": "image"},
                                {"type": "text", "text": "Generate a detailed caption for this image."}
                            ]
                        }
                    ]

                    # Genera caption
                    prompt = processor.apply_chat_template(messages, add_generation_prompt=True)
                    inputs = processor(text=prompt, images=[image], return_tensors="pt").to(self.device)

                    with torch.no_grad():
                        # Genera con parametri molto conservativi per memoria
                        generated_ids = model.generate(
                            **inputs,
                            max_new_tokens=80,   # Molto ridotto
                            num_beams=1,         # Greedy decoding per memoria
                            do_sample=False,
                            early_stopping=True,
                            pad_token_id=processor.tokenizer.eos_token_id
                        )

                    generated_text = processor.decode(generated_ids[0], skip_special_tokens=True)

                    # Estrai solo la caption (rimuovi prompt)
                    if "Generate a detailed caption for this image." in generated_text:
                        generated_caption = generated_text.split("Generate a detailed caption for this image.")[-1].strip()
                    else:
                        generated_caption = generated_text.strip()

                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'ground_truth': example['caption'],
                        'generated_caption': generated_caption,
                        'image_path': example['image_path']
                    })

                    # Progress
                    if (i + 1) % 10 == 0:
                        elapsed = time.time() - start_time
                        avg_time = elapsed / (i + 1)
                        remaining = avg_time * (len(dataset) - i - 1)
                        logger.info(f"📊 Idefics3 Progress: {i+1}/{len(dataset)} "
                                  f"({(i+1)/len(dataset)*100:.1f}%) - "
                                  f"ETA: {remaining/60:.1f}min")

                    # Pulizia memoria ogni 50 esempi
                    if (i + 1) % 50 == 0:
                        clear_memory()

                except Exception as e:
                    logger.error(f"❌ Errore esempio {i}: {e}")
                    results.append({
                        'id': example['id'],
                        'filename': example['filename'],
                        'ground_truth': example['caption'],
                        'generated_caption': f"ERROR: {str(e)}",
                        'image_path': example['image_path']
                    })

            # Salva risultati
            output_data = {
                'model': 'Idefics3',
                'model_name': 'HuggingFaceM4/Idefics3-8B-Llama3',
                'total_examples': len(dataset),
                'successful_examples': len([r for r in results if not r['generated_caption'].startswith('ERROR')]),
                'timestamp': datetime.now().isoformat(),
                'device': self.device,
                'results': results
            }

            with open(output_file, 'w') as f:
                json.dump(output_data, f, indent=2)

            total_time = time.time() - start_time
            logger.info(f"✅ Idefics3 completato in {total_time/60:.1f}min")
            logger.info(f"💾 Risultati salvati: {output_file}")

            # Pulizia finale
            del model, processor
            clear_memory()

            return output_data

        except Exception as e:
            logger.error(f"❌ Errore Idefics3: {e}")
            return None

def main():
    parser = argparse.ArgumentParser(description='Baseline Evaluation Completa')
    parser.add_argument('--dataset', 
                       default='data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json',
                       help='Path al dataset JSON')
    parser.add_argument('--output_dir', 
                       default='evaluation_results/baseline_COMPLETE_400',
                       help='Directory output')
    parser.add_argument('--models', 
                       nargs='+', 
                       default=['blip2'],
                       choices=['blip2', 'florence2', 'idefics3'],
                       help='Modelli da valutare')
    parser.add_argument('--device',
                       default='auto',
                       choices=['auto', 'cuda', 'cpu'],
                       help='Device da usare')
    parser.add_argument('--force_cpu',
                       action='store_true',
                       help='Forza modalità CPU per evitare CUDA OOM')
    parser.add_argument('--memory_optimized',
                       action='store_true',
                       help='Usa ottimizzazioni memoria aggressive')
    
    args = parser.parse_args()
    
    # Setup device
    if args.force_cpu:
        device = 'cpu'
        logger.info("🔧 Modalità CPU forzata per evitare problemi memoria")
    elif args.device == 'auto':
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    else:
        device = args.device

    logger.info(f"🖥️ Device selezionato: {device}")
    if args.memory_optimized:
        logger.info("🔧 Ottimizzazioni memoria aggressive attivate")
    
    # Crea directory output
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Carica dataset
    dataset = load_dataset_complete(args.dataset)
    if not dataset:
        logger.error("❌ Impossibile caricare il dataset")
        return
    
    # Inizializza evaluator
    evaluator = BaselineEvaluator(device=device, force_cpu=args.force_cpu)
    
    # Valuta modelli richiesti
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if 'blip2' in args.models:
        output_file = os.path.join(args.output_dir, f'blip2_results_{timestamp}.json')
        evaluator.evaluate_blip2(dataset, output_file)

    if 'florence2' in args.models:
        output_file = os.path.join(args.output_dir, f'florence2_results_{timestamp}.json')
        evaluator.evaluate_florence2(dataset, output_file)

    if 'idefics3' in args.models:
        output_file = os.path.join(args.output_dir, f'idefics3_results_{timestamp}.json')
        evaluator.evaluate_idefics3(dataset, output_file)

    logger.info("🎉 Valutazione baseline completa terminata!")

if __name__ == "__main__":
    main()
