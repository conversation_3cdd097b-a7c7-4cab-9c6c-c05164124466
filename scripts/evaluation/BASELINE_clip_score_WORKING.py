#!/usr/bin/env python3
"""
🎯 VERO CLIP SCORE PER BASELINE MODELS
Usa immagini PNG già generate e calcola CLIP score realistico
"""

import os
import json
import argparse
import logging
import torch
from datetime import datetime
from PIL import Image
import numpy as np

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_clip_transformers():
    """Setup CLIP usando transformers"""
    try:
        from transformers import CLIPProcessor, CLIPModel
        
        device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"🔧 CLIP su device: {device}")
        
        model_name = "openai/clip-vit-base-patch32"
        model = CLIPModel.from_pretrained(model_name).to(device)
        processor = CLIPProcessor.from_pretrained(model_name)
        logger.info(f"✅ Caricato {model_name}")
        
        return model, processor, device
    except Exception as e:
        logger.error(f"❌ Errore setup CLIP: {e}")
        return None, None, None

def calculate_clip_score_baseline(image_path, text, model, processor, device):
    """Calcola CLIP Score per baseline usando immagini esistenti"""
    try:
        if not os.path.exists(image_path):
            logger.warning(f"❌ Immagine non trovata: {image_path}")
            return 0.0
            
        image = Image.open(image_path).convert('RGB')
        
        # Pulisci testo
        clean_text = text.strip()
        if not clean_text:
            return 0.0
        
        # Calcola CLIP Score con truncation
        inputs = processor(text=[clean_text], images=image, return_tensors="pt", padding=True, truncation=True, max_length=77)
        inputs = {k: v.to(device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = model(**inputs)
            logits_per_image = outputs.logits_per_image
            clip_score = torch.diagonal(logits_per_image).cpu().numpy()[0]
            
            # USA IL RAW LOGIT COME PER GEMMA
            return float(clip_score)
            
    except Exception as e:
        logger.warning(f"Errore CLIP Score: {e}")
        return 0.0

def calculate_baseline_clip_scores(results_file, model_name, images_dir, max_examples=100):
    """Calcola CLIP Scores per baseline models"""
    logger.info(f"🎯 Calcolo CLIP Scores per {model_name} (max {max_examples} esempi)")
    
    # Carica risultati
    with open(results_file, 'r') as f:
        data = json.load(f)
    
    # Gestisci diversi formati
    if isinstance(data, list):
        results = data
    else:
        results = data.get('results', [])
    
    if len(results) > max_examples:
        results = results[:max_examples]
        logger.info(f"📊 Limitato a {max_examples} esempi")
    
    # Setup CLIP
    model, processor, device = setup_clip_transformers()
    if model is None:
        return []
    
    clip_scores = []
    successful = 0
    
    for i, result in enumerate(results):
        if (i + 1) % 10 == 0:
            logger.info(f"📈 Processando esempio {i+1}/{len(results)}...")
        
        try:
            # Per baseline: usa immagini PNG già generate
            image_path = os.path.join(images_dir, f"baseline_image_{i:03d}.png")
            generated_caption = result.get('prediction', '') or result.get('generated_caption', '')
            
            if not generated_caption:
                clip_scores.append(0.0)
                continue
            
            # Calcola CLIP Score
            score = calculate_clip_score_baseline(image_path, generated_caption, model, processor, device)
            clip_scores.append(score)
            
            if score > 0:
                successful += 1
            
            if (i + 1) % 50 == 0:
                valid_scores = [s for s in clip_scores if s > 0]
                avg_score = np.mean(valid_scores) if valid_scores else 0
                logger.info(f"📊 Progress {i+1}/{len(results)} - Avg CLIP: {avg_score:.2f}")
                
                # Forza garbage collection
                import gc
                gc.collect()
                
        except Exception as e:
            logger.error(f"❌ Errore esempio {i}: {e}")
            clip_scores.append(0.0)
    
    logger.info(f"✅ CLIP Scores calcolati: {successful}/{len(results)} successi")
    
    return clip_scores

def save_baseline_clip_results(clip_scores, model_name, output_dir="evaluation_results/clip_scores"):
    """Salva risultati CLIP per baseline"""
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{model_name.lower().replace('-', '_')}_BASELINE_CLIP_RAW_{timestamp}.json"
    filepath = os.path.join(output_dir, filename)
    
    # Calcola statistiche
    valid_scores = [s for s in clip_scores if s > 0]
    
    results = {
        "model": model_name,
        "total_examples": len(clip_scores),
        "successful_conversions": len(valid_scores),
        "clip_scores": clip_scores,
        "avg_clip_score": float(np.mean(valid_scores)) if valid_scores else 0.0,
        "std_clip_score": float(np.std(valid_scores)) if valid_scores else 0.0,
        "min_clip_score": float(np.min(valid_scores)) if valid_scores else 0.0,
        "max_clip_score": float(np.max(valid_scores)) if valid_scores else 0.0,
        "timestamp": datetime.now().isoformat()
    }
    
    with open(filepath, 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"💾 Risultati salvati: {filepath}")
    return filepath

def main():
    parser = argparse.ArgumentParser(description='BASELINE CLIP Score Calculator')
    parser.add_argument('--results_file', required=True, help='File JSON risultati baseline')
    parser.add_argument('--model_name', required=True, help='Nome modello baseline')
    parser.add_argument('--images_dir', required=True, help='Directory immagini PNG')
    parser.add_argument('--max_examples', type=int, default=100, help='Max esempi da processare')
    
    args = parser.parse_args()
    
    logger.info("🎯 BASELINE CLIP SCORE CALCULATOR")
    logger.info("=" * 50)
    logger.info(f"🤖 Modello: {args.model_name}")
    logger.info(f"📁 File: {os.path.basename(args.results_file)}")
    logger.info(f"🖼️ Immagini: {args.images_dir}")
    logger.info(f"📊 Max esempi: {args.max_examples}")
    logger.info("=" * 50)
    
    # Calcola CLIP scores
    clip_scores = calculate_baseline_clip_scores(args.results_file, args.model_name, args.images_dir, args.max_examples)
    
    if not clip_scores:
        logger.error("❌ Nessun CLIP score calcolato!")
        return
    
    # Salva risultati
    filepath = save_baseline_clip_results(clip_scores, args.model_name)
    
    # Statistiche finali
    valid_scores = [s for s in clip_scores if s > 0]
    
    print("\n" + "=" * 50)
    print("🎉 BASELINE CLIP SCORES CALCOLATI!")
    print("=" * 50)
    print(f"📊 CLIP Score medio: {np.mean(valid_scores):.4f} (±{np.std(valid_scores):.4f})")
    print(f"📊 Range: {np.min(valid_scores):.4f} - {np.max(valid_scores):.4f}")
    print(f"📊 Scores validi: {len(valid_scores)}/{len(clip_scores)}")
    print(f"📄 Risultati: {os.path.basename(filepath)}")
    print("=" * 50)
    print("✅ BASELINE CLIP Score completato!")

if __name__ == "__main__":
    main()
