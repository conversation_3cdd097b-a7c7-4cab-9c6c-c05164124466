#!/bin/bash
#SBATCH --job-name=BASELINE_BOOST
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/BASELINE_BOOST_%j.out
#SBATCH --error=logs/BASELINE_BOOST_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=3:00:00

echo "🚀 BASELINE EVALUATION - BOOST"
echo "=============================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"
echo "Partition: boost_usr_prod"
echo "=============================="

# Setup environment
cd /work/tesi_ediluzio

# Activate conda environment
export PATH="/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin:$PATH"
eval "$(/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/conda shell.bash hook)"
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false
export CUDA_LAUNCH_BLOCKING=1

# Paths
DATASET_FILE="data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json"
OUTPUT_DIR="evaluation_results/baseline_boost"
SCRIPT="scripts/evaluation/baseline_evaluation_FAST.py"

echo "📊 Configurazione BOOST:"
echo "   Dataset: $DATASET_FILE"
echo "   Output: $OUTPUT_DIR"
echo "   Script: $SCRIPT"
echo "   Max esempi: 100"
echo "   Modello: BLIP2-OPT-2.7B"
echo "   Memoria: 32G"
echo "   CPU: 8"

# Create directories
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🚀 Avvio baseline evaluation su BOOST..."

# Esegui baseline
python "$SCRIPT" \
    --dataset_file "$DATASET_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --max_examples 100

echo "✅ Baseline evaluation BOOST completato!"
echo "📁 Risultati: $OUTPUT_DIR"
