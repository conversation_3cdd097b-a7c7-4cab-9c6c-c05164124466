#!/bin/bash
#SBATCH --job-name=LLAMA_RESUME_SIMPLE
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_RESUME_SIMPLE_%j.out
#SBATCH --error=logs/LLAMA_RESUME_SIMPLE_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00

echo "🟠 LLAMA-3.1-8B RESUME SEMPLIFICATO"
echo "===================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPUs: $CUDA_VISIBLE_DEVICES"
echo "Start time: $(date)"
echo ""

# Setup environment
source ~/.bashrc
echo "🔧 Using system Python with user packages..."

# Verify environment
echo "🔍 Environment check:"
echo "Python: $(which python)"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "GPU count: $(python -c 'import torch; print(torch.cuda.device_count())')"
echo "GPU names: $(python -c 'import torch; [print(f"GPU {i}: {torch.cuda.get_device_name(i)}") for i in range(torch.cuda.device_count())]')"
echo ""

# Set environment variables
export CUDA_LAUNCH_BLOCKING=1
export TOKENIZERS_PARALLELISM=false
export WANDB_PROJECT="svg_caption_llama_resume"
export WANDB_RUN_NAME="llama_t8_resume_simple_$(date +%Y%m%d_%H%M%S)"

# Change to project directory
cd /work/tesi_ediluzio

# Verify checkpoint exists and is valid
echo "🔍 Verifica checkpoint:"
CHECKPOINT_DIR="experiments/xml_direct_input/outputs/llama_t8_continue/checkpoint-18750"

if [ ! -d "$CHECKPOINT_DIR" ]; then
    echo "❌ ERRORE: Checkpoint directory non trovata: $CHECKPOINT_DIR"
    exit 1
fi

echo "✅ Checkpoint directory trovata: $CHECKPOINT_DIR"

# Check required files
REQUIRED_FILES=("adapter_model.safetensors" "trainer_state.json" "training_args.bin" "optimizer.pt" "scheduler.pt")

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$CHECKPOINT_DIR/$file" ]; then
        echo "❌ ERRORE: File mancante nel checkpoint: $file"
        exit 1
    else
        echo "✅ File trovato: $file"
    fi
done

echo ""
echo "🚀 Avvio training resume con script semplificato..."
echo "📂 Resume da: $CHECKPOINT_DIR"
echo ""

# Paths
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
DATA_FILE="data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json"
CONFIG_PATH="experiments/xml_direct_input/configs/llama_t8_2gpu_final.json"
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_continue"
CHECKPOINT="$CHECKPOINT_DIR"

echo "📊 Configurazione:"
echo "   Model: $MODEL_NAME"
echo "   Data: $DATA_FILE"
echo "   Config: $CONFIG_PATH"
echo "   Output: $OUTPUT_DIR"
echo "   Resume: $CHECKPOINT"
echo ""

# Run training with simple script (same as quantized jobs)
python scripts/training/train_lora_simple.py \
    --model_name "$MODEL_NAME" \
    --data_file "$DATA_FILE" \
    --config_path "$CONFIG_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb \
    --wandb_project svg_caption_llama_resume \
    --wandb_run_name llama_t8_resume_simple_$(date +%Y%m%d_%H%M%S) \
    --resume_from_checkpoint "$CHECKPOINT"

echo ""
echo "🏁 Training completato!"
echo "End time: $(date)"
