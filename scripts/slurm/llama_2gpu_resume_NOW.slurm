#!/bin/bash
#SBATCH --job-name=LLAMA_2GPU_NOW
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_2GPU_NOW_%j.out
#SBATCH --error=logs/LLAMA_2GPU_NOW_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00

echo "🟠 LLAMA 2 GPU RESUME NOW"
echo "========================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPUs: $CUDA_VISIBLE_DEVICES"
echo "Start time: $(date)"
echo ""

# Setup environment
source ~/.bashrc

# Verify environment
echo "🔍 Environment check:"
echo "Python: $(which python)"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "GPU count: $(python -c 'import torch; print(torch.cuda.device_count())')"
echo ""

# Set environment variables
export CUDA_LAUNCH_BLOCKING=1
export TOKENIZERS_PARALLELISM=false

# Change to project directory
cd /work/tesi_ediluzio

# Verify checkpoint
CHECKPOINT_DIR="experiments/xml_direct_input/outputs/llama_t8_continue/checkpoint-18750"
echo "🔍 Verifica checkpoint: $CHECKPOINT_DIR"

if [ ! -d "$CHECKPOINT_DIR" ]; then
    echo "❌ ERRORE: Checkpoint non trovato"
    exit 1
fi

echo "✅ Checkpoint trovato"
echo ""
echo "🚀 Avvio training..."

# Run training
python scripts/training/train_lora_LLAMA_2GPU_RESUME.py

echo ""
echo "🏁 Training completato!"
echo "End time: $(date)"
