#!/bin/bash
#SBATCH --job-name=LLAMA_2GPU_WORKING
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_2GPU_WORKING_%j.out
#SBATCH --error=logs/LLAMA_2GPU_WORKING_%j.err
#SBATCH --partition=boost_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00

echo "🟠 LLAMA 2 GPU WORKING VERSION"
echo "=============================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPUs: $CUDA_VISIBLE_DEVICES"
echo "Start time: $(date)"
echo ""

# Setup environment
cd /work/tesi_ediluzio

# Activate conda environment directly (same as quantized jobs)
export PATH="/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin:$PATH"
eval "$(/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/conda shell.bash hook)"
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false

# Paths
MODEL_NAME="meta-llama/Llama-3.1-8B-Instruct"
DATA_FILE="data/processed/FINAL_CORRECT_RGB/train_set_90k_RGB.json"
CONFIG_PATH="experiments/xml_direct_input/configs/llama_t8_2gpu_final.json"
OUTPUT_DIR="experiments/xml_direct_input/outputs/llama_t8_continue"
CHECKPOINT="experiments/xml_direct_input/outputs/llama_t8_continue/checkpoint-18750"

echo "📊 Configurazione:"
echo "   Model: $MODEL_NAME"
echo "   Data: $DATA_FILE"
echo "   Config: $CONFIG_PATH"
echo "   Output: $OUTPUT_DIR"
echo "   Resume: $CHECKPOINT"
echo ""

# Run training with working script (same as quantized but without quantization)
python scripts/training/train_lora_ULTRA_QUANTIZED.py \
    --model_name "$MODEL_NAME" \
    --data_file "$DATA_FILE" \
    --config_path "$CONFIG_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --use_wandb \
    --wandb_project svg_caption_llama_2gpu \
    --wandb_run_name llama_t8_2gpu_resume_$(date +%Y%m%d_%H%M%S) \
    --resume_from_checkpoint "$CHECKPOINT"

echo ""
echo "🏁 Training completato!"
echo "End time: $(date)"
