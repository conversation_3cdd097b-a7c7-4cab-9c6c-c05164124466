#!/bin/bash
#SBATCH --job-name=LLAMA_RESUME_18750
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/LLAMA_RESUME_18750_%j.out
#SBATCH --error=logs/LLAMA_RESUME_18750_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --gres=gpu:2
#SBATCH --cpus-per-task=16
#SBATCH --mem=64G
#SBATCH --time=24:00:00

echo "🟠 LLAMA-3.1-8B RESUME DA CHECKPOINT 18750"
echo "=========================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPUs: $CUDA_VISIBLE_DEVICES"
echo "Start time: $(date)"
echo ""

# Setup environment
source ~/.bashrc
echo "🔧 Using system Python with user packages..."

# Verify environment
echo "🔍 Environment check:"
echo "Python: $(which python)"
echo "CUDA available: $(python -c 'import torch; print(torch.cuda.is_available())')"
echo "GPU count: $(python -c 'import torch; print(torch.cuda.device_count())')"
echo "GPU names: $(python -c 'import torch; [print(f"GPU {i}: {torch.cuda.get_device_name(i)}") for i in range(torch.cuda.device_count())]')"
echo ""

# Set environment variables
export CUDA_LAUNCH_BLOCKING=1
export TOKENIZERS_PARALLELISM=false
export WANDB_PROJECT="svg_caption_llama_resume"
export WANDB_RUN_NAME="llama_t8_resume_18750_$(date +%Y%m%d_%H%M%S)"

# Change to project directory
cd /work/tesi_ediluzio

# Verify checkpoint exists and is valid
echo "🔍 Verifica checkpoint:"
CHECKPOINT_DIR="experiments/xml_direct_input/outputs/llama_t8_continue/checkpoint-18750"

if [ ! -d "$CHECKPOINT_DIR" ]; then
    echo "❌ ERRORE: Checkpoint directory non trovata: $CHECKPOINT_DIR"
    exit 1
fi

echo "✅ Checkpoint directory trovata: $CHECKPOINT_DIR"

# Check required files
REQUIRED_FILES=("adapter_model.safetensors" "trainer_state.json" "training_args.bin" "optimizer.pt" "scheduler.pt")

for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$CHECKPOINT_DIR/$file" ]; then
        echo "❌ ERRORE: File mancante nel checkpoint: $file"
        exit 1
    else
        echo "✅ File trovato: $file"
    fi
done

echo ""
echo "🚀 Avvio training resume..."
echo "📂 Resume da: $CHECKPOINT_DIR"
echo ""

# Run training
python scripts/training/train_lora_LLAMA_RESUME_18750.py

echo ""
echo "🏁 Training completato!"
echo "End time: $(date)"
