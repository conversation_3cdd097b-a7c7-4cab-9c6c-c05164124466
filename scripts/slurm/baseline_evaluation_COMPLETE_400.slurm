#!/bin/bash
#SBATCH --job-name=baseline_eval_400_ALL
#SBATCH --account=tesi_ediluzio
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks-per-node=1
#SBATCH --cpus-per-task=8
#SBATCH --gres=gpu:1
#SBATCH --mem=48GB
#SBATCH --time=08:00:00
#SBATCH --output=logs/baseline_eval_400_ALL_%j.out
#SBATCH --error=logs/baseline_eval_400_ALL_%j.err

echo "🚀 BASELINE EVALUATION COMPLETA - 400 ESEMPI (ALL_USR_PROD)"
echo "============================================================"
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "Partition: all_usr_prod"
echo "Start time: $(date)"
echo "============================================================"

# Setup environment
source /homes/ediluzio/.bashrc
conda activate svg_env_new

# Variabili
DATASET_FILE="data/processed/baseline_dataset_COMPLETE/baseline_dataset_SIMPLE_20250723_165736.json"
OUTPUT_DIR="evaluation_results/baseline_COMPLETE_400"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Crea directory output
mkdir -p $OUTPUT_DIR
mkdir -p logs

echo "📊 Dataset: $DATASET_FILE"
echo "📁 Output: $OUTPUT_DIR"
echo "⏰ Timestamp: $TIMESTAMP"
echo ""

# Verifica dataset
if [ ! -f "$DATASET_FILE" ]; then
    echo "❌ ERRORE: Dataset non trovato: $DATASET_FILE"
    exit 1
fi

echo "✅ Dataset trovato: $(wc -l < $DATASET_FILE) righe"
echo ""

# GPU info
echo "🖥️ GPU INFO:"
nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
echo ""

# Memoria disponibile
echo "💾 MEMORIA DISPONIBILE:"
free -h
echo ""

# Spazio disco
echo "💿 SPAZIO DISCO:"
df -h /work/tesi_ediluzio
echo ""

echo "🔵 FASE 1: VALUTAZIONE BLIP-2"
echo "=============================="
python scripts/evaluation/baseline_evaluation_COMPLETE.py \
    --dataset "$DATASET_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models blip2 \
    --device auto

if [ $? -eq 0 ]; then
    echo "✅ BLIP-2 completato con successo"
else
    echo "❌ BLIP-2 fallito"
fi

echo ""
echo "🟠 FASE 2: VALUTAZIONE FLORENCE-2"
echo "================================="
python scripts/evaluation/baseline_evaluation_COMPLETE.py \
    --dataset "$DATASET_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models florence2 \
    --device auto

if [ $? -eq 0 ]; then
    echo "✅ Florence-2 completato con successo"
else
    echo "❌ Florence-2 fallito"
fi

echo ""
echo "🟢 FASE 3: VALUTAZIONE IDEFICS3"
echo "==============================="
python scripts/evaluation/baseline_evaluation_COMPLETE.py \
    --dataset "$DATASET_FILE" \
    --output_dir "$OUTPUT_DIR" \
    --models idefics3 \
    --device auto

if [ $? -eq 0 ]; then
    echo "✅ Idefics3 completato con successo"
else
    echo "❌ Idefics3 fallito"
fi

echo ""
echo "🧮 FASE 4: CALCOLO METRICHE"
echo "==========================="
python scripts/evaluation/calculate_baseline_metrics_COMPLETE.py \
    --results_dir "$OUTPUT_DIR" \
    --image_base_path "data/processed/baseline_dataset_COMPLETE/images" \
    --output_dir "$OUTPUT_DIR"

if [ $? -eq 0 ]; then
    echo "✅ Calcolo metriche completato con successo"
else
    echo "❌ Calcolo metriche fallito"
fi

echo ""
echo "📊 FASE 5: CREAZIONE RADAR CHART"
echo "================================"
python scripts/evaluation/create_radar_COMPLETE_400.py \
    --metrics_dir "$OUTPUT_DIR" \
    --output_dir "$OUTPUT_DIR" \
    --title "CONFRONTO MODELLI BASELINE\n(Dataset Completo - 400 Esempi)"

if [ $? -eq 0 ]; then
    echo "✅ Radar chart creato con successo"
else
    echo "❌ Creazione radar chart fallita"
fi

echo ""
echo "📋 RISULTATI FINALI:"
echo "===================="
echo "📁 Directory output: $OUTPUT_DIR"
echo "📊 File generati:"
ls -la "$OUTPUT_DIR"/*_${TIMESTAMP}* 2>/dev/null || echo "Nessun file con timestamp trovato"
ls -la "$OUTPUT_DIR"/*.json "$OUTPUT_DIR"/*.png 2>/dev/null || echo "Nessun file risultato trovato"

echo ""
echo "💾 SPAZIO DISCO FINALE:"
df -h /work/tesi_ediluzio

echo ""
echo "🎉 BASELINE EVALUATION COMPLETA TERMINATA!"
echo "=========================================="
echo "End time: $(date)"
echo "Job ID: $SLURM_JOB_ID"
