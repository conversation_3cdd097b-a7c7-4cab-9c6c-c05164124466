#!/bin/bash
#SBATCH --job-name=GEMMA_QUANT_EVAL
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=8
#SBATCH --mem=32GB
#SBATCH --gres=gpu:1
#SBATCH --time=06:00:00
#SBATCH --output=logs/GEMMA_QUANT_EVAL_%j.out
#SBATCH --error=logs/GEMMA_QUANT_EVAL_%j.err

echo "🎯 EVALUATION GEMMA-2-9B QUANTIZZATO"
echo "===================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "GPU: $CUDA_VISIBLE_DEVICES"
echo "Timestamp: $(date)"
echo "===================================="

# Setup environment
source ~/.bashrc
cd /work/tesi_ediluzio

# Attiva environment
conda activate svg_caption || {
    echo "❌ Errore attivazione environment"
    exit 1
}

# Verifica GPU
nvidia-smi
echo ""

# Parametri modello (DA AGGIORNARE QUANDO PRONTO)
MODEL_NAME="GEMMA_QUANT"
BASE_MODEL="google/gemma-2-9b-it"
ADAPTER_PATH="experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized/checkpoint-FINAL"  # DA AGGIORNARE
DATASET="data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json"
OUTPUT_DIR="evaluation_results/quantized_models"

echo "🤖 Modello: $MODEL_NAME"
echo "📁 Base model: $BASE_MODEL"
echo "🔗 Adapter: $ADAPTER_PATH"
echo "📊 Dataset: $DATASET"
echo ""

# Crea directory output
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🚀 FASE 1: INFERENCE E GENERAZIONE CAPTION"
echo "=========================================="

# Controlla se l'adapter esiste
if [ ! -d "$ADAPTER_PATH" ]; then
    echo "⚠️ Adapter non trovato: $ADAPTER_PATH"
    echo "🔄 Cercando checkpoint più recente..."
    
    # Cerca l'ultimo checkpoint
    CHECKPOINT_DIR="experiments/xml_direct_input/outputs/gemma_t9_scratch_quantized"
    if [ -d "$CHECKPOINT_DIR" ]; then
        LATEST_CHECKPOINT=$(find "$CHECKPOINT_DIR" -name "checkpoint-*" -type d | sort -V | tail -1)
        if [ -n "$LATEST_CHECKPOINT" ]; then
            ADAPTER_PATH="$LATEST_CHECKPOINT"
            echo "✅ Trovato checkpoint: $ADAPTER_PATH"
        else
            echo "❌ Nessun checkpoint trovato in $CHECKPOINT_DIR"
            exit 1
        fi
    else
        echo "❌ Directory training non trovata: $CHECKPOINT_DIR"
        exit 1
    fi
fi

# Esegui evaluation
python scripts/evaluation/evaluate_quantized_models.py \
    --model_name "$MODEL_NAME" \
    --model_path "$BASE_MODEL" \
    --adapter_path "$ADAPTER_PATH" \
    --dataset "$DATASET" \
    --output_dir "$OUTPUT_DIR" \
    --max_examples 400

if [ $? -eq 0 ]; then
    echo "✅ Inference completata con successo"
else
    echo "❌ Errore durante inference"
    exit 1
fi

echo ""
echo "📊 FASE 2: CALCOLO METRICHE COMPREHENSIVE"
echo "========================================"

# Trova il file risultati più recente
RESULTS_FILE=$(find "$OUTPUT_DIR" -name "${MODEL_NAME}_quantized_results_*.json" | sort | tail -1)

if [ -z "$RESULTS_FILE" ]; then
    echo "❌ File risultati non trovato"
    exit 1
fi

echo "📄 File risultati: $RESULTS_FILE"

# Calcola metriche
python scripts/evaluation/calculate_quantized_metrics.py \
    --results_files "$RESULTS_FILE" \
    --comprehensive_dir "evaluation_results/comprehensive_metrics" \
    --radar_dir "evaluation_results/radar_charts_PERFECT" \
    --output_dir "$OUTPUT_DIR"

if [ $? -eq 0 ]; then
    echo "✅ Metriche calcolate con successo"
else
    echo "❌ Errore calcolo metriche"
    exit 1
fi

echo ""
echo "🎉 EVALUATION GEMMA QUANTIZZATO COMPLETATA!"
echo "==========================================="
echo "📁 Risultati in: $OUTPUT_DIR"
echo "📊 Metriche in: evaluation_results/comprehensive_metrics"
echo "📈 Grafici in: evaluation_results/radar_charts_PERFECT"
echo "Timestamp fine: $(date)"
echo "==========================================="
