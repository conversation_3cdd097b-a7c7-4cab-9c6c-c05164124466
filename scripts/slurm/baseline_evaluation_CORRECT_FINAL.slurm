#!/bin/bash
#SBATCH --job-name=BASELINE_CORRECT
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/BASELINE_CORRECT_%j.out
#SBATCH --error=logs/BASELINE_CORRECT_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=6:00:00

echo "🎯 BASELINE EVALUATION CORRETTO"
echo "==============================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "Parametri: OTTIMIZZATI"
echo "Dataset: RGB CORRETTO"
echo "Modelli: BLIP2, Florence2, Idefics3 REALI"
echo "==============================="

# Setup environment
cd /work/tesi_ediluzio

# Activate conda environment directly
export PATH="/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin:$PATH"
eval "$(/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/conda shell.bash hook)"
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false

# Paths
DATASET="data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json"
OUTPUT_DIR="evaluation_results/baseline_CORRECT_FINAL"
SCRIPT="scripts/evaluation/baseline_evaluation_CORRECT_FINAL.py"

echo "📊 Configurazione:"
echo "   Dataset: $DATASET"
echo "   Output: $OUTPUT_DIR"
echo "   Script: $SCRIPT"

# Create directories
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🚀 Avvio baseline evaluation CORRETTO..."

# Esegui evaluation con parametri ottimizzati
python "$SCRIPT" \
    --dataset "$DATASET" \
    --output_dir "$OUTPUT_DIR" \
    --models blip2 florence2 idefics3 \
    --max_examples 400

echo "✅ Baseline evaluation completato!"
echo "📁 Risultati: $OUTPUT_DIR"

# Verifica risultati
echo ""
echo "📊 RIEPILOGO RISULTATI:"
if [ -d "$OUTPUT_DIR" ]; then
    echo "✅ Directory output creata"
    ls -la "$OUTPUT_DIR"
    
    echo ""
    echo "📄 File risultati:"
    find "$OUTPUT_DIR" -name "*_results_CORRECT_*.json" -exec basename {} \;
else
    echo "❌ Directory output non trovata"
fi

echo ""
echo "🎯 PROSSIMI PASSI:"
echo "1. Verifica risultati in: $OUTPUT_DIR"
echo "2. Calcola metriche con: scripts/evaluation/calculate_baseline_metrics_FIXED.py"
echo "3. Genera radar chart con: scripts/visualization/create_baseline_radar_FIXED.py"
