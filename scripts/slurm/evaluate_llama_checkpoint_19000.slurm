#!/bin/bash
#SBATCH --job-name=EVAL_LLAMA_19000
#SBATCH --account=tesi_ediluzio
#SBATCH --output=logs/EVAL_LLAMA_19000_%j.out
#SBATCH --error=logs/EVAL_LLAMA_19000_%j.err
#SBATCH --partition=all_usr_prod
#SBATCH --nodes=1
#SBATCH --gres=gpu:1
#SBATCH --mem=32G
#SBATCH --cpus-per-task=8
#SBATCH --time=4:00:00

echo "🎯 EVALUATION LLAMA T8 CHECKPOINT-19000"
echo "======================================="
echo "Job ID: $SLURM_JOB_ID"
echo "Node: $SLURM_NODELIST"
echo "Checkpoint: checkpoint-19000"
echo "======================================="

# Setup environment
cd /work/tesi_ediluzio

# Activate conda environment
export PATH="/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin:$PATH"
eval "$(/homes/admin/spack/opt/spack/linux-ivybridge/anaconda3-2023.09-0-dyblbfizocp3rfv77i4yhkbvbb5xamnh/bin/conda shell.bash hook)"
conda activate svg_env_new

# Environment variables
export HUGGINGFACE_HUB_TOKEN=*************************************
export TOKENIZERS_PARALLELISM=false

# Paths
CHECKPOINT="experiments/xml_direct_input/outputs/llama_t8_continue/checkpoint-18750"
DATASET="data/processed/FINAL_CORRECT_RGB/baseline_set_400_RGB_FIXED.json"
OUTPUT_DIR="evaluation_results/trained_models"
SCRIPT="scripts/evaluation/run_trained_model_inference.py"

echo "📊 Configurazione:"
echo "   Checkpoint: $CHECKPOINT"
echo "   Dataset: $DATASET"
echo "   Output: $OUTPUT_DIR"
echo "   Script: $SCRIPT"

# Create directories
mkdir -p "$OUTPUT_DIR"
mkdir -p logs

echo "🚀 Avvio evaluation Llama T8..."

# Evaluation
python "$SCRIPT" \
    --checkpoint "$CHECKPOINT" \
    --model_name llama_t8 \
    --dataset "$DATASET" \
    --output_dir "$OUTPUT_DIR" \
    --max_examples 100

echo "✅ Evaluation Llama T8 completata!"
echo "📁 Risultati: $OUTPUT_DIR"
